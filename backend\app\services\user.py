"""
用户管理服务
"""
from typing import List, Optional

from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.orm import Session

from app.core.security import get_password_hash
from app.models.department import Department
from app.models.role import Role
from app.models.user import User
from app.schemas.auth import UserCreate, UserUpdate


class UserService:
    """用户管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        获取用户列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            用户列表
        """
        return self.db.query(User).offset(skip).limit(limit).all()

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        根据ID获取用户

        Args:
            user_id: 用户ID

        Returns:
            用户对象或None
        """
        return self.db.query(User).filter(User.id == user_id).first()

    def get_user_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户

        Args:
            username: 用户名

        Returns:
            用户对象或None
        """
        return self.db.query(User).filter(User.username == username).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户

        Args:
            email: 邮箱

        Returns:
            用户对象或None
        """
        return self.db.query(User).filter(User.email == email).first()

    def create_user(self, user_data: UserCreate) -> User:
        """
        创建用户

        Args:
            user_data: 用户创建数据

        Returns:
            创建的用户对象

        Raises:
            HTTPException: 用户名或邮箱已存在时抛出异常
        """
        # 检查用户名是否已存在
        if self.get_user_by_username(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已存在"
            )

        # 检查邮箱是否已存在
        if self.get_user_by_email(user_data.email):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已存在")

        # 创建用户
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            hashed_password=hashed_password,
            is_active=user_data.is_active,
        )

        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)

        return db_user

    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """
        更新用户信息

        Args:
            user_id: 用户ID
            user_data: 用户更新数据

        Returns:
            更新后的用户对象

        Raises:
            HTTPException: 用户不存在或用户名/邮箱已被其他用户使用时抛出异常
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

        # 检查用户名是否被其他用户使用
        if user_data.username and user_data.username != user.username:
            existing_user = self.get_user_by_username(user_data.username)
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已被其他用户使用"
                )

        # 检查邮箱是否被其他用户使用
        if user_data.email and user_data.email != user.email:
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已被其他用户使用"
                )

        # 更新用户信息
        update_data = user_data.dict(exclude_unset=True)
        if "password" in update_data:
            update_data["hashed_password"] = get_password_hash(
                update_data.pop("password")
            )

        for field, value in update_data.items():
            setattr(user, field, value)

        self.db.commit()
        self.db.refresh(user)

        return user

    def delete_user(self, user_id: int) -> bool:
        """
        删除用户

        Args:
            user_id: 用户ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 用户不存在时抛出异常
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

        self.db.delete(user)
        self.db.commit()

        return True

    def assign_role_to_user(self, user_id: int, role_id: int) -> User:
        """
        为用户分配角色

        Args:
            user_id: 用户ID
            role_id: 角色ID

        Returns:
            更新后的用户对象

        Raises:
            HTTPException: 用户或角色不存在时抛出异常
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

        role = self.db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

        if role not in user.roles:
            user.roles.append(role)
            self.db.commit()
            self.db.refresh(user)

        return user

    def remove_role_from_user(self, user_id: int, role_id: int) -> User:
        """
        移除用户角色

        Args:
            user_id: 用户ID
            role_id: 角色ID

        Returns:
            更新后的用户对象

        Raises:
            HTTPException: 用户或角色不存在时抛出异常
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

        role = self.db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

        if role in user.roles:
            user.roles.remove(role)
            self.db.commit()
            self.db.refresh(user)

        return user

    def assign_department_to_user(self, user_id: int, department_id: int) -> User:
        """
        为用户分配部门

        Args:
            user_id: 用户ID
            department_id: 部门ID

        Returns:
            更新后的用户对象

        Raises:
            HTTPException: 用户或部门不存在时抛出异常
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

        department = (
            self.db.query(Department).filter(Department.id == department_id).first()
        )
        if not department:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

        # 清除用户现有部门
        user.departments.clear()
        # 分配新部门
        user.departments.append(department)

        self.db.commit()
        self.db.refresh(user)

        return user
