"""
部门相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class DepartmentBase(BaseModel):
    """部门基础模式"""

    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: int = 0


class DepartmentCreate(DepartmentBase):
    """部门创建模式"""

    pass


class DepartmentUpdate(BaseModel):
    """部门更新模式"""

    name: Optional[str] = None
    description: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None


class Department(DepartmentBase):
    """部门响应模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class DepartmentTree(Department):
    """部门树形结构模式"""

    children: List["DepartmentTree"] = []

    model_config = {"from_attributes": True}


# 更新前向引用
DepartmentTree.model_rebuild()
