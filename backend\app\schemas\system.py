"""
系统监控相关的Pydantic模式
"""
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class AlertSeverity(str, Enum):
    """告警严重程度枚举"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """告警状态枚举"""

    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"


class ComparisonOperator(str, Enum):
    """比较操作符枚举"""

    GT = ">"
    LT = "<"
    GTE = ">="
    LTE = "<="
    EQ = "=="
    NE = "!="


# 系统指标相关模式
class SystemMetricsBase(BaseModel):
    """系统指标基础模式"""

    cpu_percent: Optional[float] = Field(None, description="CPU使用率(%)")
    cpu_count: Optional[int] = Field(None, description="CPU核心数")
    cpu_freq_current: Optional[float] = Field(None, description="当前CPU频率(MHz)")
    cpu_freq_max: Optional[float] = Field(None, description="最大CPU频率(MHz)")

    memory_total: Optional[int] = Field(None, description="总内存(bytes)")
    memory_available: Optional[int] = Field(None, description="可用内存(bytes)")
    memory_used: Optional[int] = Field(None, description="已用内存(bytes)")
    memory_percent: Optional[float] = Field(None, description="内存使用率(%)")

    swap_total: Optional[int] = Field(None, description="总交换分区(bytes)")
    swap_used: Optional[int] = Field(None, description="已用交换分区(bytes)")
    swap_percent: Optional[float] = Field(None, description="交换分区使用率(%)")

    disk_total: Optional[int] = Field(None, description="总磁盘空间(bytes)")
    disk_used: Optional[int] = Field(None, description="已用磁盘空间(bytes)")
    disk_free: Optional[int] = Field(None, description="可用磁盘空间(bytes)")
    disk_percent: Optional[float] = Field(None, description="磁盘使用率(%)")

    disk_read_bytes: Optional[int] = Field(None, description="磁盘读取字节数")
    disk_write_bytes: Optional[int] = Field(None, description="磁盘写入字节数")
    disk_read_count: Optional[int] = Field(None, description="磁盘读取次数")
    disk_write_count: Optional[int] = Field(None, description="磁盘写入次数")

    network_bytes_sent: Optional[int] = Field(None, description="网络发送字节数")
    network_bytes_recv: Optional[int] = Field(None, description="网络接收字节数")
    network_packets_sent: Optional[int] = Field(None, description="网络发送包数")
    network_packets_recv: Optional[int] = Field(None, description="网络接收包数")

    process_count: Optional[int] = Field(None, description="进程数量")
    load_avg_1: Optional[float] = Field(None, description="1分钟平均负载")
    load_avg_5: Optional[float] = Field(None, description="5分钟平均负载")
    load_avg_15: Optional[float] = Field(None, description="15分钟平均负载")
    uptime: Optional[float] = Field(None, description="系统运行时间(秒)")


class SystemMetricsCreate(SystemMetricsBase):
    """系统指标创建模式"""

    pass


class SystemMetrics(SystemMetricsBase):
    """系统指标响应模式"""

    id: int
    created_at: datetime

    model_config = {"from_attributes": True}


# 进程信息相关模式
class ProcessInfo(BaseModel):
    """进程信息模式"""

    pid: int = Field(..., description="进程ID")
    name: str = Field(..., description="进程名称")
    cpu_percent: Optional[float] = Field(None, description="CPU使用率(%)")
    memory_percent: Optional[float] = Field(None, description="内存使用率(%)")
    status: Optional[str] = Field(None, description="进程状态")
    ppid: Optional[int] = Field(None, description="父进程ID")
    username: Optional[str] = Field(None, description="用户名")
    create_time: Optional[float] = Field(None, description="创建时间戳")
    num_threads: Optional[int] = Field(None, description="线程数")


# 网络连接相关模式
class NetworkConnection(BaseModel):
    """网络连接模式"""

    local_address: str = Field(..., description="本地地址")
    remote_address: str = Field(..., description="远程地址")
    status: str = Field(..., description="连接状态")
    pid: Optional[int] = Field(None, description="进程ID")


# 磁盘分区相关模式
class DiskPartition(BaseModel):
    """磁盘分区模式"""

    device: str = Field(..., description="设备名")
    mountpoint: str = Field(..., description="挂载点")
    fstype: str = Field(..., description="文件系统类型")
    total: int = Field(..., description="总空间(bytes)")
    used: int = Field(..., description="已用空间(bytes)")
    free: int = Field(..., description="可用空间(bytes)")
    percent: float = Field(..., description="使用率(%)")


# 资源分析相关模式
class ResourceAnalysis(BaseModel):
    """资源分析模式"""

    cpu_status: str = Field(..., description="CPU状态")
    memory_status: str = Field(..., description="内存状态")
    disk_status: str = Field(..., description="磁盘状态")
    overall_status: str = Field(..., description="整体状态")
    recommendations: List[str] = Field(default=[], description="优化建议")


# 告警规则相关模式
class AlertRuleBase(BaseModel):
    """告警规则基础模式"""

    name: str = Field(..., min_length=1, max_length=100, description="规则名称")
    description: Optional[str] = Field(None, max_length=1000, description="规则描述")
    metric_name: str = Field(..., max_length=100, description="监控指标名称")
    threshold_value: float = Field(..., description="阈值")
    comparison_operator: ComparisonOperator = Field(..., description="比较操作符")
    severity: AlertSeverity = Field(AlertSeverity.MEDIUM, description="严重程度")
    duration_minutes: int = Field(5, ge=1, le=1440, description="持续时间(分钟)")
    evaluation_interval: int = Field(60, ge=10, le=3600, description="评估间隔(秒)")
    notification_enabled: bool = Field(True, description="是否启用通知")
    notification_channels: Optional[Dict[str, Any]] = Field(None, description="通知渠道配置")
    is_active: bool = Field(True, description="是否激活")


class AlertRuleCreate(AlertRuleBase):
    """告警规则创建模式"""

    pass


class AlertRuleUpdate(BaseModel):
    """告警规则更新模式"""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="规则名称")
    description: Optional[str] = Field(None, max_length=1000, description="规则描述")
    threshold_value: Optional[float] = Field(None, description="阈值")
    comparison_operator: Optional[ComparisonOperator] = Field(None, description="比较操作符")
    severity: Optional[AlertSeverity] = Field(None, description="严重程度")
    duration_minutes: Optional[int] = Field(None, ge=1, le=1440, description="持续时间(分钟)")
    evaluation_interval: Optional[int] = Field(
        None, ge=10, le=3600, description="评估间隔(秒)"
    )
    notification_enabled: Optional[bool] = Field(None, description="是否启用通知")
    notification_channels: Optional[Dict[str, Any]] = Field(None, description="通知渠道配置")
    is_active: Optional[bool] = Field(None, description="是否激活")


class AlertRule(AlertRuleBase):
    """告警规则响应模式"""

    id: int
    last_evaluation: Optional[datetime] = None
    last_triggered: Optional[datetime] = None
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# 系统告警相关模式
class SystemAlertBase(BaseModel):
    """系统告警基础模式"""

    alert_type: str = Field(..., max_length=50, description="告警类型")
    severity: AlertSeverity = Field(..., description="严重程度")
    title: str = Field(..., max_length=200, description="告警标题")
    message: str = Field(..., description="告警消息")
    metric_name: str = Field(..., max_length=100, description="指标名称")
    threshold_value: float = Field(..., description="阈值")
    comparison_operator: str = Field(..., max_length=10, description="比较操作符")
    current_value: Optional[float] = Field(None, description="当前值")
    status: AlertStatus = Field(AlertStatus.ACTIVE, description="告警状态")
    is_active: bool = Field(True, description="是否激活")


class SystemAlertCreate(SystemAlertBase):
    """系统告警创建模式"""

    pass


class SystemAlertUpdate(BaseModel):
    """系统告警更新模式"""

    status: Optional[AlertStatus] = Field(None, description="告警状态")
    acknowledged_by: Optional[str] = Field(None, max_length=100, description="确认人")
    resolved_by: Optional[str] = Field(None, max_length=100, description="解决人")
    notes: Optional[str] = Field(None, description="备注")


class SystemAlert(SystemAlertBase):
    """系统告警响应模式"""

    id: int
    triggered_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    resolved_by: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# 查询相关模式
class SystemMetricsQuery(BaseModel):
    """系统指标查询模式"""

    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    metrics: Optional[List[str]] = Field(None, description="指标名称列表")
    interval: Optional[int] = Field(None, ge=60, description="聚合间隔(秒)")
    limit: Optional[int] = Field(100, ge=1, le=10000, description="限制数量")


class AlertQuery(BaseModel):
    """告警查询模式"""

    severity: Optional[AlertSeverity] = Field(None, description="严重程度")
    status: Optional[AlertStatus] = Field(None, description="告警状态")
    alert_type: Optional[str] = Field(None, description="告警类型")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    skip: int = Field(0, ge=0, description="跳过数量")
    limit: int = Field(100, ge=1, le=1000, description="限制数量")


# 统计相关模式
class SystemStats(BaseModel):
    """系统统计模式"""

    total_alerts: int = Field(0, description="总告警数")
    active_alerts: int = Field(0, description="活跃告警数")
    critical_alerts: int = Field(0, description="严重告警数")
    alert_rules_count: int = Field(0, description="告警规则数")
    avg_cpu_usage: Optional[float] = Field(None, description="平均CPU使用率")
    avg_memory_usage: Optional[float] = Field(None, description="平均内存使用率")
    avg_disk_usage: Optional[float] = Field(None, description="平均磁盘使用率")


# 系统概览模式
class SystemOverview(BaseModel):
    """系统概览模式"""

    current_metrics: SystemMetrics
    resource_analysis: ResourceAnalysis
    active_alerts: List[SystemAlert]
    top_processes: List[ProcessInfo]
    disk_partitions: List[DiskPartition]
    network_connections: List[NetworkConnection]
    system_stats: SystemStats
