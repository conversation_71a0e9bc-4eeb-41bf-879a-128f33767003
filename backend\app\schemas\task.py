"""
任务相关的Pydantic模式
"""
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class TaskType(str, Enum):
    """任务类型枚举"""

    COMMAND = "command"
    SCRIPT = "script"
    HTTP_REQUEST = "http_request"
    DATABASE = "database"
    FILE_OPERATION = "file_operation"
    BACKUP = "backup"
    DEPLOYMENT = "deployment"
    MONITORING = "monitoring"
    CUSTOM = "custom"


class ScheduleType(str, Enum):
    """调度类型枚举"""

    MANUAL = "manual"
    CRON = "cron"
    INTERVAL = "interval"


class TaskStatus(str, Enum):
    """任务执行状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class DependencyType(str, Enum):
    """依赖类型枚举"""

    SUCCESS = "success"
    FAILURE = "failure"
    ALWAYS = "always"


# 任务基础模式
class TaskBase(BaseModel):
    """任务基础模式"""

    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    task_type: TaskType = Field(..., description="任务类型")
    app_id: Optional[int] = Field(None, description="关联应用ID")

    # 任务配置
    command: Optional[str] = Field(None, description="执行命令")
    parameters: Optional[Dict[str, Any]] = Field(None, description="任务参数")
    working_directory: Optional[str] = Field(None, max_length=500, description="工作目录")
    environment_vars: Optional[Dict[str, str]] = Field(None, description="环境变量")

    # 调度配置
    schedule_type: ScheduleType = Field(ScheduleType.MANUAL, description="调度类型")
    cron_expression: Optional[str] = Field(None, max_length=100, description="Cron表达式")
    interval_seconds: Optional[int] = Field(None, ge=1, description="间隔秒数")

    # 控制配置
    is_active: bool = Field(True, description="是否激活")
    max_retries: int = Field(0, ge=0, le=10, description="最大重试次数")
    timeout_seconds: int = Field(3600, ge=1, le=86400, description="超时时间(秒)")


class TaskCreate(TaskBase):
    """任务创建模式"""

    pass


class TaskUpdate(BaseModel):
    """任务更新模式"""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    task_type: Optional[TaskType] = Field(None, description="任务类型")
    app_id: Optional[int] = Field(None, description="关联应用ID")

    # 任务配置
    command: Optional[str] = Field(None, description="执行命令")
    parameters: Optional[Dict[str, Any]] = Field(None, description="任务参数")
    working_directory: Optional[str] = Field(None, max_length=500, description="工作目录")
    environment_vars: Optional[Dict[str, str]] = Field(None, description="环境变量")

    # 调度配置
    schedule_type: Optional[ScheduleType] = Field(None, description="调度类型")
    cron_expression: Optional[str] = Field(None, max_length=100, description="Cron表达式")
    interval_seconds: Optional[int] = Field(None, ge=1, description="间隔秒数")

    # 控制配置
    is_active: Optional[bool] = Field(None, description="是否激活")
    max_retries: Optional[int] = Field(None, ge=0, le=10, description="最大重试次数")
    timeout_seconds: Optional[int] = Field(None, ge=1, le=86400, description="超时时间(秒)")


class Task(TaskBase):
    """任务响应模式"""

    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    last_run_at: Optional[datetime] = None
    next_run_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


class TaskWithStats(Task):
    """带统计信息的任务模式"""

    total_executions: int = 0
    success_executions: int = 0
    failed_executions: int = 0
    last_execution_status: Optional[TaskStatus] = None
    average_duration: Optional[float] = None


# 任务执行相关模式
class TaskExecutionBase(BaseModel):
    """任务执行基础模式"""

    task_id: int
    status: TaskStatus = TaskStatus.PENDING
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    exit_code: Optional[int] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    error_message: Optional[str] = None
    executor_host: Optional[str] = None
    process_id: Optional[int] = None
    retry_count: int = 0
    is_retry: bool = False
    parent_execution_id: Optional[int] = None


class TaskExecutionCreate(TaskExecutionBase):
    """任务执行创建模式"""

    pass


class TaskExecutionUpdate(BaseModel):
    """任务执行更新模式"""

    status: Optional[TaskStatus] = None
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    exit_code: Optional[int] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    error_message: Optional[str] = None
    process_id: Optional[int] = None


class TaskExecution(TaskExecutionBase):
    """任务执行响应模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# 任务模板相关模式
class TaskTemplateBase(BaseModel):
    """任务模板基础模式"""

    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=1000, description="模板描述")
    category: Optional[str] = Field(None, max_length=50, description="模板分类")
    task_type: TaskType = Field(..., description="任务类型")
    command_template: Optional[str] = Field(None, description="命令模板")
    parameters_schema: Optional[Dict[str, Any]] = Field(None, description="参数模式")
    default_parameters: Optional[Dict[str, Any]] = Field(None, description="默认参数")
    is_public: bool = Field(True, description="是否为公共模板")


class TaskTemplateCreate(TaskTemplateBase):
    """任务模板创建模式"""

    pass


class TaskTemplateUpdate(BaseModel):
    """任务模板更新模式"""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=1000, description="模板描述")
    category: Optional[str] = Field(None, max_length=50, description="模板分类")
    task_type: Optional[TaskType] = Field(None, description="任务类型")
    command_template: Optional[str] = Field(None, description="命令模板")
    parameters_schema: Optional[Dict[str, Any]] = Field(None, description="参数模式")
    default_parameters: Optional[Dict[str, Any]] = Field(None, description="默认参数")
    is_public: Optional[bool] = Field(None, description="是否为公共模板")


class TaskTemplate(TaskTemplateBase):
    """任务模板响应模式"""

    id: int
    is_system: bool
    usage_count: int
    created_by: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# 任务依赖相关模式
class TaskDependencyBase(BaseModel):
    """任务依赖基础模式"""

    task_id: int
    depends_on_task_id: int
    dependency_type: DependencyType = DependencyType.SUCCESS
    is_active: bool = True


class TaskDependencyCreate(TaskDependencyBase):
    """任务依赖创建模式"""

    pass


class TaskDependencyUpdate(BaseModel):
    """任务依赖更新模式"""

    dependency_type: Optional[DependencyType] = None
    is_active: Optional[bool] = None


class TaskDependency(TaskDependencyBase):
    """任务依赖响应模式"""

    id: int
    created_at: datetime

    model_config = {"from_attributes": True}


# 查询和统计相关模式
class TaskQuery(BaseModel):
    """任务查询模式"""

    app_id: Optional[int] = None
    task_type: Optional[TaskType] = None
    schedule_type: Optional[ScheduleType] = None
    is_active: Optional[bool] = None
    keyword: Optional[str] = None
    created_by: Optional[int] = None
    skip: int = 0
    limit: int = 100


class TaskExecutionQuery(BaseModel):
    """任务执行查询模式"""

    task_id: Optional[int] = None
    status: Optional[TaskStatus] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    skip: int = 0
    limit: int = 100


class TaskStats(BaseModel):
    """任务统计模式"""

    total_tasks: int
    active_tasks: int
    inactive_tasks: int
    scheduled_tasks: int
    manual_tasks: int
    by_type: Dict[str, int]
    by_status: Dict[str, int]


class TaskExecutionStats(BaseModel):
    """任务执行统计模式"""

    total_executions: int
    success_executions: int
    failed_executions: int
    running_executions: int
    average_duration: Optional[float]
    success_rate: float


# 批量操作模式
class TaskBatchOperation(BaseModel):
    """任务批量操作模式"""

    task_ids: List[int]
    operation: str  # activate, deactivate, delete, execute


class TaskExecuteRequest(BaseModel):
    """任务执行请求模式"""

    task_id: int
    parameters: Optional[Dict[str, Any]] = None
    force: bool = False  # 是否强制执行（忽略依赖）


class TaskScheduleInfo(BaseModel):
    """任务调度信息模式"""

    task_id: int
    task_name: str
    next_run_at: Optional[datetime]
    last_run_at: Optional[datetime]
    is_active: bool
    schedule_type: ScheduleType
    cron_expression: Optional[str]
    interval_seconds: Optional[int]
