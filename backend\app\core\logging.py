"""
日志配置
"""
import logging
import logging.config
import sys
from pathlib import Path
from typing import Any, Dict

from app.core.config import settings


def setup_logging() -> None:
    """设置日志配置"""

    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 日志配置
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(filename)s %(lineno)d %(funcName)s %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",
                "formatter": "detailed",
                "filename": log_dir / "app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": log_dir / "error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "app": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            "sqlalchemy.pool": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["file"],
                "propagate": False,
            },
        },
        "root": {"level": "INFO", "handlers": ["console", "file"]},
    }

    # 在生产环境中调整日志级别
    if settings.ENVIRONMENT == "production":
        logging_config["handlers"]["console"]["level"] = "WARNING"
        logging_config["loggers"]["app"]["level"] = "INFO"
        logging_config["root"]["level"] = "WARNING"

    # 在开发环境中启用详细日志
    if settings.ENVIRONMENT == "development":
        logging_config["handlers"]["console"]["formatter"] = "detailed"
        logging_config["loggers"]["sqlalchemy.engine"]["level"] = (
            "INFO" if settings.DEBUG else "WARNING"
        )

    # 应用日志配置
    logging.config.dictConfig(logging_config)

    # 设置第三方库的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("multipart").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(f"app.{name}")


class LoggerMixin:
    """日志记录器混入类"""

    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        return get_logger(self.__class__.__name__)


# 请求日志中间件
class RequestLoggingMiddleware:
    """请求日志中间件"""

    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware.request")

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request_info = {
                "method": scope["method"],
                "path": scope["path"],
                "query_string": scope["query_string"].decode(),
                "client": scope.get("client"),
                "headers": dict(scope.get("headers", [])),
            }

            self.logger.info(
                f"Request: {request_info['method']} {request_info['path']}",
                extra={"request_info": request_info},
            )

        await self.app(scope, receive, send)
