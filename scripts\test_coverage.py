#!/usr/bin/env python3
"""
测试覆盖率检查脚本
"""
import subprocess
import sys
import json
import os
from pathlib import Path
from typing import Dict, Any, List


def run_command(command: List[str], cwd: Path = None) -> tuple[int, str, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=False
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)


def run_backend_tests():
    """运行后端测试"""
    print("🐍 运行后端测试...")
    
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # 运行测试并生成覆盖率报告
    returncode, stdout, stderr = run_command([
        "python", "-m", "pytest",
        "--cov=app",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov",
        "--cov-report=xml:coverage.xml",
        "--cov-report=json:coverage.json",
        "--tb=short",
        "-v"
    ], cwd=backend_dir)
    
    if returncode == 0:
        print("✅ 后端测试通过")
        
        # 解析覆盖率报告
        coverage_file = backend_dir / "coverage.json"
        if coverage_file.exists():
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data["totals"]["percent_covered"]
            print(f"📊 后端代码覆盖率: {total_coverage:.1f}%")
            
            return True, total_coverage
        else:
            print("⚠️  覆盖率报告文件未找到")
            return True, 0
    else:
        print("❌ 后端测试失败:")
        print(stdout)
        print(stderr)
        return False, 0


def run_frontend_tests():
    """运行前端测试"""
    print("\n🎨 运行前端测试...")
    
    frontend_dir = Path(__file__).parent.parent / "frontend"
    
    # 检查 npm 是否可用
    returncode, _, _ = run_command(["npm", "--version"])
    if returncode != 0:
        print("❌ npm 不可用，跳过前端测试")
        return False, 0
    
    # 运行测试
    returncode, stdout, stderr = run_command([
        "npm", "run", "test:coverage"
    ], cwd=frontend_dir)
    
    if returncode == 0:
        print("✅ 前端测试通过")
        
        # 尝试解析覆盖率
        coverage_file = frontend_dir / "coverage" / "coverage-summary.json"
        if coverage_file.exists():
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
            
            total_coverage = coverage_data["total"]["lines"]["pct"]
            print(f"📊 前端代码覆盖率: {total_coverage:.1f}%")
            
            return True, total_coverage
        else:
            print("⚠️  前端覆盖率报告文件未找到")
            return True, 0
    else:
        print("❌ 前端测试失败:")
        print(stdout)
        print(stderr)
        return False, 0


def analyze_coverage_gaps(backend_dir: Path, frontend_dir: Path):
    """分析覆盖率缺口"""
    print("\n🔍 分析覆盖率缺口...")
    
    gaps = []
    
    # 分析后端覆盖率缺口
    backend_coverage_file = backend_dir / "coverage.json"
    if backend_coverage_file.exists():
        with open(backend_coverage_file, 'r') as f:
            coverage_data = json.load(f)
        
        for filename, file_data in coverage_data["files"].items():
            coverage_pct = file_data["summary"]["percent_covered"]
            if coverage_pct < 80:  # 低于80%的文件
                gaps.append({
                    "file": filename,
                    "type": "backend",
                    "coverage": coverage_pct,
                    "missing_lines": file_data["missing_lines"]
                })
    
    # 分析前端覆盖率缺口
    frontend_coverage_file = frontend_dir / "coverage" / "coverage-final.json"
    if frontend_coverage_file.exists():
        with open(frontend_coverage_file, 'r') as f:
            coverage_data = json.load(f)
        
        for filename, file_data in coverage_data.items():
            if "src/" in filename:  # 只关注源代码文件
                lines_pct = file_data["l"]["pct"]
                if lines_pct < 80:  # 低于80%的文件
                    gaps.append({
                        "file": filename,
                        "type": "frontend",
                        "coverage": lines_pct,
                        "uncovered_lines": len(file_data["l"]["uncoveredLines"])
                    })
    
    if gaps:
        print("📋 发现覆盖率缺口:")
        for gap in sorted(gaps, key=lambda x: x["coverage"]):
            print(f"  - {gap['file']} ({gap['type']}): {gap['coverage']:.1f}%")
    else:
        print("✅ 未发现明显的覆盖率缺口")
    
    return gaps


def generate_coverage_recommendations(gaps: List[Dict[str, Any]]) -> List[str]:
    """生成覆盖率改进建议"""
    recommendations = []
    
    # 按文件类型分组
    backend_gaps = [g for g in gaps if g["type"] == "backend"]
    frontend_gaps = [g for g in gaps if g["type"] == "frontend"]
    
    if backend_gaps:
        recommendations.append("后端改进建议:")
        for gap in backend_gaps[:5]:  # 只显示前5个
            recommendations.append(f"  - 为 {gap['file']} 添加测试用例")
            if "missing_lines" in gap:
                recommendations.append(f"    缺失行数: {len(gap['missing_lines'])}")
    
    if frontend_gaps:
        recommendations.append("前端改进建议:")
        for gap in frontend_gaps[:5]:  # 只显示前5个
            recommendations.append(f"  - 为 {gap['file']} 添加组件测试")
            if "uncovered_lines" in gap:
                recommendations.append(f"    未覆盖行数: {gap['uncovered_lines']}")
    
    # 通用建议
    if gaps:
        recommendations.extend([
            "",
            "通用建议:",
            "  - 优先测试核心业务逻辑",
            "  - 添加边界条件测试",
            "  - 增加错误处理测试",
            "  - 考虑添加集成测试"
        ])
    
    return recommendations


def generate_test_report():
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"
    
    # 运行测试
    backend_success, backend_coverage = run_backend_tests()
    frontend_success, frontend_coverage = run_frontend_tests()
    
    # 分析覆盖率缺口
    gaps = analyze_coverage_gaps(backend_dir, frontend_dir)
    
    # 生成建议
    recommendations = generate_coverage_recommendations(gaps)
    
    # 创建报告
    report = {
        "timestamp": subprocess.run(
            ["python", "-c", "import datetime; print(datetime.datetime.now().isoformat())"],
            capture_output=True,
            text=True
        ).stdout.strip(),
        "backend": {
            "tests_passed": backend_success,
            "coverage": backend_coverage
        },
        "frontend": {
            "tests_passed": frontend_success,
            "coverage": frontend_coverage
        },
        "overall": {
            "all_tests_passed": backend_success and frontend_success,
            "average_coverage": (backend_coverage + frontend_coverage) / 2 if backend_coverage and frontend_coverage else 0
        },
        "gaps": gaps,
        "recommendations": recommendations
    }
    
    # 保存报告
    report_file = project_root / "test_coverage_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 测试报告已保存到: {report_file}")
    
    # 显示摘要
    print("\n📈 测试覆盖率摘要:")
    print(f"  后端测试: {'✅ 通过' if backend_success else '❌ 失败'}")
    print(f"  后端覆盖率: {backend_coverage:.1f}%")
    print(f"  前端测试: {'✅ 通过' if frontend_success else '❌ 失败'}")
    print(f"  前端覆盖率: {frontend_coverage:.1f}%")
    print(f"  平均覆盖率: {report['overall']['average_coverage']:.1f}%")
    
    if recommendations:
        print("\n💡 改进建议:")
        for rec in recommendations:
            print(rec)
    
    # 设置目标覆盖率
    target_coverage = 80
    overall_coverage = report['overall']['average_coverage']
    
    if overall_coverage >= target_coverage:
        print(f"\n🎉 覆盖率达到目标 ({target_coverage}%)！")
        return True
    else:
        print(f"\n⚠️  覆盖率未达到目标 ({target_coverage}%)，当前: {overall_coverage:.1f}%")
        return False


def main():
    """主函数"""
    print("🧪 开始测试覆盖率检查...")
    print("=" * 50)
    
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent
    if not (project_root / "backend").exists() or not (project_root / "frontend").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 生成测试报告
    success = generate_test_report()
    
    print("=" * 50)
    if success:
        print("✅ 测试覆盖率检查完成，达到目标")
        sys.exit(0)
    else:
        print("⚠️  测试覆盖率检查完成，需要改进")
        sys.exit(1)


if __name__ == "__main__":
    main()
