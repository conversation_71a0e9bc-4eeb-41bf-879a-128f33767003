"""
菜单模型
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Menu(Base):
    """菜单模型"""

    __tablename__ = "menus"

    id = Column(Integer, primary_key=True, index=True, comment="菜单ID")
    name = Column(String(100), nullable=False, comment="菜单名称")
    path = Column(String(200), nullable=True, comment="菜单路径")
    component = Column(String(200), nullable=True, comment="组件路径")
    icon = Column(String(100), nullable=True, comment="菜单图标")
    parent_id = Column(Integer, ForeignKey("menus.id"), nullable=True, comment="父菜单ID")
    sort_order = Column(Integer, default=0, comment="排序")
    is_hidden = Column(Boolean, default=False, comment="是否隐藏")
    is_external = Column(<PERSON><PERSON><PERSON>, default=False, comment="是否外部链接")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    parent = relationship("Menu", remote_side=[id], back_populates="children")
    children = relationship("Menu", back_populates="parent")
    roles = relationship("Role", secondary="role_menus", back_populates="menus")
