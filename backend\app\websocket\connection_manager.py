"""
WebSocket连接管理器
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 存储活跃连接 {app_id: {connection_id: websocket}}
        self.active_connections: Dict[int, Dict[str, WebSocket]] = {}
        # 存储连接订阅的消息类型 {connection_id: set(message_types)}
        self.subscriptions: Dict[str, Set[str]] = {}
        # 连接计数器
        self.connection_counter = 0

    def generate_connection_id(self) -> str:
        """生成连接ID"""
        self.connection_counter += 1
        return f"conn_{self.connection_counter}_{datetime.now().timestamp()}"

    async def connect(self, websocket: WebSocket, app_id: int) -> str:
        """接受WebSocket连接"""
        await websocket.accept()

        connection_id = self.generate_connection_id()

        if app_id not in self.active_connections:
            self.active_connections[app_id] = {}

        self.active_connections[app_id][connection_id] = websocket
        self.subscriptions[connection_id] = set()

        logger.info(
            f"WebSocket connected: app_id={app_id}, connection_id={connection_id}"
        )
        return connection_id

    def disconnect(self, app_id: int, connection_id: str):
        """断开WebSocket连接"""
        if app_id in self.active_connections:
            if connection_id in self.active_connections[app_id]:
                del self.active_connections[app_id][connection_id]

                # 如果应用没有活跃连接了，删除应用记录
                if not self.active_connections[app_id]:
                    del self.active_connections[app_id]

        # 清理订阅信息
        if connection_id in self.subscriptions:
            del self.subscriptions[connection_id]

        logger.info(
            f"WebSocket disconnected: app_id={app_id}, connection_id={connection_id}"
        )

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")

    async def send_to_app(self, app_id: int, message: dict, message_type: str = None):
        """向指定应用的所有连接发送消息"""
        if app_id not in self.active_connections:
            return

        disconnected_connections = []

        for connection_id, websocket in self.active_connections[app_id].items():
            # 检查连接是否订阅了此类型的消息
            if message_type and message_type not in self.subscriptions.get(
                connection_id, set()
            ):
                continue

            try:
                await websocket.send_text(json.dumps(message))
            except WebSocketDisconnect:
                disconnected_connections.append(connection_id)
            except Exception as e:
                logger.error(
                    f"Failed to send message to connection {connection_id}: {e}"
                )
                disconnected_connections.append(connection_id)

        # 清理断开的连接
        for connection_id in disconnected_connections:
            self.disconnect(app_id, connection_id)

    async def broadcast_to_all(self, message: dict, message_type: str = None):
        """向所有连接广播消息"""
        for app_id in list(self.active_connections.keys()):
            await self.send_to_app(app_id, message, message_type)

    def subscribe(self, connection_id: str, message_types: List[str]):
        """订阅消息类型"""
        if connection_id in self.subscriptions:
            self.subscriptions[connection_id].update(message_types)
            logger.info(f"Connection {connection_id} subscribed to: {message_types}")

    def unsubscribe(self, connection_id: str, message_types: List[str]):
        """取消订阅消息类型"""
        if connection_id in self.subscriptions:
            self.subscriptions[connection_id] -= set(message_types)
            logger.info(
                f"Connection {connection_id} unsubscribed from: {message_types}"
            )

    def get_connection_count(self, app_id: int = None) -> int:
        """获取连接数量"""
        if app_id:
            return len(self.active_connections.get(app_id, {}))
        return sum(len(connections) for connections in self.active_connections.values())

    def get_active_apps(self) -> List[int]:
        """获取有活跃连接的应用列表"""
        return list(self.active_connections.keys())


# 全局连接管理器实例
connection_manager = ConnectionManager()


class MessageType:
    """WebSocket消息类型常量"""

    # 系统消息
    PING = "ping"
    PONG = "pong"

    # 应用状态更新
    APP_STATUS_UPDATE = "app_status_update"
    SERVICE_STATUS_UPDATE = "service_status_update"

    # 任务相关
    TASK_STATUS_UPDATE = "task_status_update"
    TASK_EXECUTION_UPDATE = "task_execution_update"

    # 日志相关
    LOG_MESSAGE = "log_message"
    LOG_ALERT = "log_alert"

    # 监控相关
    SYSTEM_METRICS = "system_metrics"
    ALERT_TRIGGERED = "alert_triggered"

    # 配置更新
    CONFIG_UPDATE = "config_update"


async def send_app_status_update(app_id: int, status_data: dict):
    """发送应用状态更新"""
    message = {
        "type": MessageType.APP_STATUS_UPDATE,
        "data": status_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(app_id, message, MessageType.APP_STATUS_UPDATE)


async def send_service_status_update(app_id: int, service_type: str, status: str):
    """发送服务状态更新"""
    message = {
        "type": MessageType.SERVICE_STATUS_UPDATE,
        "data": {"service_type": service_type, "status": status, "app_id": app_id},
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(
        app_id, message, MessageType.SERVICE_STATUS_UPDATE
    )


async def send_task_execution_update(app_id: int, task_data: dict):
    """发送任务执行更新"""
    message = {
        "type": MessageType.TASK_EXECUTION_UPDATE,
        "data": task_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(
        app_id, message, MessageType.TASK_EXECUTION_UPDATE
    )


async def send_log_message(app_id: int, log_data: dict):
    """发送日志消息"""
    message = {
        "type": MessageType.LOG_MESSAGE,
        "data": log_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(app_id, message, MessageType.LOG_MESSAGE)


async def send_system_metrics(app_id: int, metrics_data: dict):
    """发送系统指标"""
    message = {
        "type": MessageType.SYSTEM_METRICS,
        "data": metrics_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(app_id, message, MessageType.SYSTEM_METRICS)


async def send_alert_triggered(app_id: int, alert_data: dict):
    """发送告警触发"""
    message = {
        "type": MessageType.ALERT_TRIGGERED,
        "data": alert_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(app_id, message, MessageType.ALERT_TRIGGERED)


async def send_config_update(app_id: int, config_data: dict):
    """发送配置更新"""
    message = {
        "type": MessageType.CONFIG_UPDATE,
        "data": config_data,
        "timestamp": datetime.now().isoformat(),
    }
    await connection_manager.send_to_app(app_id, message, MessageType.CONFIG_UPDATE)
