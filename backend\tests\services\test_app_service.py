"""
应用管理服务单元测试
"""
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.models.app import App, AppConfig, AppService
from app.schemas.app import AppConfigCreate, AppConfigUpdate, AppCreate, AppUpdate
from app.services.app import AppConfigService, AppManagementService


class TestAppManagementService:
    """应用管理服务测试类"""

    def test_get_apps(self, db_session: Session):
        """测试获取应用列表"""
        service = AppManagementService(db_session)

        # 创建测试数据
        app1 = App(name="App1", description="Test App 1", created_by=1)
        app2 = App(name="App2", description="Test App 2", created_by=1)
        db_session.add_all([app1, app2])
        db_session.commit()

        # 测试获取所有应用
        apps = service.get_apps()
        assert len(apps) == 2
        assert apps[0].name == "App1"
        assert apps[1].name == "App2"

        # 测试分页
        apps = service.get_apps(skip=1, limit=1)
        assert len(apps) == 1
        assert apps[0].name == "App2"

        # 测试按用户过滤
        apps = service.get_apps(user_id=1)
        assert len(apps) == 2

        apps = service.get_apps(user_id=999)
        assert len(apps) == 0

    def test_get_app_by_id(self, db_session: Session):
        """测试根据ID获取应用"""
        service = AppManagementService(db_session)

        # 创建测试数据
        app = App(name="Test App", description="Test", created_by=1)
        db_session.add(app)
        db_session.commit()

        # 测试获取存在的应用
        result = service.get_app_by_id(app.id)
        assert result is not None
        assert result.name == "Test App"

        # 测试获取不存在的应用
        result = service.get_app_by_id(999)
        assert result is None

    def test_get_app_by_name(self, db_session: Session):
        """测试根据名称获取应用"""
        service = AppManagementService(db_session)

        # 创建测试数据
        app = App(name="Test App", description="Test", created_by=1)
        db_session.add(app)
        db_session.commit()

        # 测试获取存在的应用
        result = service.get_app_by_name("Test App")
        assert result is not None
        assert result.name == "Test App"

        # 测试获取不存在的应用
        result = service.get_app_by_name("Nonexistent App")
        assert result is None

    @patch("os.path.exists")
    def test_create_app_success(self, mock_exists, db_session: Session):
        """测试成功创建应用"""
        mock_exists.return_value = True
        service = AppManagementService(db_session)

        app_data = AppCreate(
            name="New App",
            description="New application",
            frontend_dir="/path/to/frontend",
            backend_dir="/path/to/backend",
            frontend_start_cmd="npm start",
            backend_start_cmd="python main.py",
            frontend_port=3000,
            backend_port=8000,
            is_active=True,
        )

        result = service.create_app(app_data, user_id=1)

        assert result.name == "New App"
        assert result.description == "New application"
        assert result.created_by == 1
        assert result.is_active is True

        # 验证服务记录是否创建
        frontend_service = (
            db_session.query(AppService)
            .filter(
                AppService.app_id == result.id, AppService.service_type == "frontend"
            )
            .first()
        )
        assert frontend_service is not None
        assert frontend_service.port == 3000

        backend_service = (
            db_session.query(AppService)
            .filter(
                AppService.app_id == result.id, AppService.service_type == "backend"
            )
            .first()
        )
        assert backend_service is not None
        assert backend_service.port == 8000

    def test_create_app_duplicate_name(self, db_session: Session):
        """测试创建重复名称的应用"""
        service = AppManagementService(db_session)

        # 创建已存在的应用
        existing_app = App(name="Existing App", created_by=1)
        db_session.add(existing_app)
        db_session.commit()

        app_data = AppCreate(
            name="Existing App", description="Duplicate app", is_active=True
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_app(app_data, user_id=1)

        assert exc_info.value.status_code == 400
        assert "应用名称已存在" in str(exc_info.value.detail)

    @patch("os.path.exists")
    def test_create_app_invalid_directory(self, mock_exists, db_session: Session):
        """测试创建应用时目录不存在"""
        mock_exists.return_value = False
        service = AppManagementService(db_session)

        app_data = AppCreate(
            name="New App", frontend_dir="/nonexistent/path", is_active=True
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_app(app_data, user_id=1)

        assert exc_info.value.status_code == 400
        assert "前端目录不存在" in str(exc_info.value.detail)

    @patch("os.path.exists")
    def test_update_app_success(self, mock_exists, db_session: Session):
        """测试成功更新应用"""
        mock_exists.return_value = True
        service = AppManagementService(db_session)

        # 创建测试应用
        app = App(name="Original App", description="Original", created_by=1)
        db_session.add(app)
        db_session.commit()

        update_data = AppUpdate(
            name="Updated App",
            description="Updated description",
            frontend_dir="/new/path",
        )

        result = service.update_app(app.id, update_data, user_id=1)

        assert result.name == "Updated App"
        assert result.description == "Updated description"
        assert result.frontend_dir == "/new/path"

    def test_update_app_not_found(self, db_session: Session):
        """测试更新不存在的应用"""
        service = AppManagementService(db_session)

        update_data = AppUpdate(name="Updated App")

        with pytest.raises(HTTPException) as exc_info:
            service.update_app(999, update_data, user_id=1)

        assert exc_info.value.status_code == 404
        assert "应用不存在" in str(exc_info.value.detail)

    def test_update_app_permission_denied(self, db_session: Session):
        """测试更新应用权限不足"""
        service = AppManagementService(db_session)

        # 创建其他用户的应用
        app = App(name="Other User App", created_by=2)
        db_session.add(app)
        db_session.commit()

        update_data = AppUpdate(name="Updated App")

        with pytest.raises(HTTPException) as exc_info:
            service.update_app(app.id, update_data, user_id=1)

        assert exc_info.value.status_code == 403
        assert "无权限修改此应用" in str(exc_info.value.detail)

    def test_delete_app_success(self, db_session: Session):
        """测试成功删除应用"""
        service = AppManagementService(db_session)

        # 创建测试应用
        app = App(name="To Delete", created_by=1)
        db_session.add(app)
        db_session.commit()

        result = service.delete_app(app.id, user_id=1)
        assert result is True

        # 验证应用已删除
        deleted_app = db_session.query(App).filter(App.id == app.id).first()
        assert deleted_app is None

    def test_delete_app_with_running_services(self, db_session: Session):
        """测试删除有运行服务的应用"""
        service = AppManagementService(db_session)

        # 创建测试应用和运行中的服务
        app = App(name="Running App", created_by=1)
        db_session.add(app)
        db_session.commit()

        running_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(running_service)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.delete_app(app.id, user_id=1)

        assert exc_info.value.status_code == 400
        assert "请先停止服务" in str(exc_info.value.detail)

    def test_get_app_with_services(self, db_session: Session):
        """测试获取应用及其服务状态"""
        service = AppManagementService(db_session)

        # 创建测试应用
        app = App(name="Test App", description="Test", created_by=1)
        db_session.add(app)
        db_session.commit()

        # 创建服务记录
        frontend_service = AppService(
            app_id=app.id,
            service_type="frontend",
            status="running",
            pid=1234,
            port=3000,
        )
        backend_service = AppService(
            app_id=app.id, service_type="backend", status="stopped", port=8000
        )
        db_session.add_all([frontend_service, backend_service])
        db_session.commit()

        result = service.get_app_with_services(app.id)

        assert result["name"] == "Test App"
        assert result["frontend_status"] == "running"
        assert result["frontend_pid"] == 1234
        assert result["frontend_port"] == 3000
        assert result["backend_status"] == "stopped"
        assert result["backend_port"] == 8000

    def test_get_app_stats(self, db_session: Session):
        """测试获取应用统计信息"""
        service = AppManagementService(db_session)

        # 创建测试数据
        app1 = App(name="App1", created_by=1)
        app2 = App(name="App2", created_by=1)
        db_session.add_all([app1, app2])
        db_session.commit()

        # 创建服务记录
        services = [
            AppService(app_id=app1.id, service_type="frontend", status="running"),
            AppService(app_id=app1.id, service_type="backend", status="stopped"),
            AppService(app_id=app2.id, service_type="frontend", status="error"),
        ]
        db_session.add_all(services)
        db_session.commit()

        stats = service.get_app_stats()

        assert stats["total_apps"] == 2
        assert stats["running_apps"] == 1
        assert stats["stopped_apps"] == 1
        assert stats["error_apps"] == 1


class TestAppConfigService:
    """应用配置服务测试类"""

    def test_get_app_configs(self, db_session: Session):
        """测试获取应用配置列表"""
        service = AppConfigService(db_session)

        # 创建测试应用
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        # 创建配置
        config1 = AppConfig(app_id=app.id, key="key1", value="value1")
        config2 = AppConfig(app_id=app.id, key="key2", value="value2")
        db_session.add_all([config1, config2])
        db_session.commit()

        configs = service.get_app_configs(app.id)
        assert len(configs) == 2
        assert configs[0].key == "key1"
        assert configs[1].key == "key2"

    def test_create_config_success(self, db_session: Session):
        """测试成功创建配置"""
        service = AppConfigService(db_session)

        # 创建测试应用
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        config_data = AppConfigCreate(
            key="test_key", value="test_value", description="Test configuration"
        )

        result = service.create_config(app.id, config_data, user_id=1)

        assert result.key == "test_key"
        assert result.value == "test_value"
        assert result.description == "Test configuration"
        assert result.app_id == app.id

    def test_create_config_duplicate_key(self, db_session: Session):
        """测试创建重复键的配置"""
        service = AppConfigService(db_session)

        # 创建测试应用和配置
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        existing_config = AppConfig(app_id=app.id, key="existing_key", value="value")
        db_session.add(existing_config)
        db_session.commit()

        config_data = AppConfigCreate(key="existing_key", value="new_value")

        with pytest.raises(HTTPException) as exc_info:
            service.create_config(app.id, config_data)

        assert exc_info.value.status_code == 400
        assert "配置键已存在" in str(exc_info.value.detail)

    def test_update_config_success(self, db_session: Session):
        """测试成功更新配置"""
        service = AppConfigService(db_session)

        # 创建测试应用和配置
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        config = AppConfig(app_id=app.id, key="test_key", value="old_value")
        db_session.add(config)
        db_session.commit()

        update_data = AppConfigUpdate(
            value="new_value", description="Updated description"
        )

        result = service.update_config(config.id, update_data, user_id=1)

        assert result.value == "new_value"
        assert result.description == "Updated description"

    def test_update_config_not_found(self, db_session: Session):
        """测试更新不存在的配置"""
        service = AppConfigService(db_session)

        update_data = AppConfigUpdate(value="new_value")

        with pytest.raises(HTTPException) as exc_info:
            service.update_config(999, update_data)

        assert exc_info.value.status_code == 404
        assert "配置不存在" in str(exc_info.value.detail)

    def test_delete_config_success(self, db_session: Session):
        """测试成功删除配置"""
        service = AppConfigService(db_session)

        # 创建测试应用和配置
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        config = AppConfig(app_id=app.id, key="test_key", value="test_value")
        db_session.add(config)
        db_session.commit()

        result = service.delete_config(config.id, user_id=1)
        assert result is True

        # 验证配置已删除
        deleted_config = (
            db_session.query(AppConfig).filter(AppConfig.id == config.id).first()
        )
        assert deleted_config is None

    def test_delete_config_not_found(self, db_session: Session):
        """测试删除不存在的配置"""
        service = AppConfigService(db_session)

        with pytest.raises(HTTPException) as exc_info:
            service.delete_config(999)

        assert exc_info.value.status_code == 404
        assert "配置不存在" in str(exc_info.value.detail)
