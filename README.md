# 应用项目管理系统

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/your-username/app-project-manager)
[![Coverage](https://img.shields.io/badge/coverage-85%25-green)](https://github.com/your-username/app-project-manager)
[![License](https://img.shields.io/badge/license-MIT-blue)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.10+-blue)](https://python.org)
[![Vue](https://img.shields.io/badge/vue-3.0+-green)](https://vuejs.org)

一个基于 FastAPI + Vue 3 的现代化应用项目管理系统，提供完整的应用生命周期管理、实时监控、用户权限管理等功能。

## ✨ 功能特性

- 🚀 **应用生命周期管理** - 完整的应用部署、启动、停止、更新流程
- 📊 **实时监控和日志** - 实时性能监控、日志聚合和分析
- 👥 **用户权限管理** - 基于角色的访问控制（RBAC）
- 🔄 **任务调度系统** - 支持定时任务和异步任务处理
- 📱 **响应式前端界面** - 现代化的用户界面，支持移动端
- 🔌 **WebSocket 实时通信** - 实时状态更新和通知
- 🔒 **安全性** - JWT 认证、密码加密、API 限流
- 📈 **性能优化** - 数据库查询优化、缓存机制、懒加载
- 🧪 **高测试覆盖率** - 完善的单元测试和集成测试

## 🛠 技术栈

### 后端技术
- **FastAPI** - 现代、快速的 Web 框架，支持自动 API 文档
- **SQLAlchemy** - Python SQL 工具包和 ORM，支持多种数据库
- **PostgreSQL** - 企业级关系型数据库
- **Redis** - 内存数据库，用于缓存和会话存储
- **Alembic** - 数据库迁移工具
- **Celery** - 分布式任务队列
- **Pydantic** - 数据验证和序列化
- **pytest** - 测试框架

### 前端技术
- **Vue 3** - 渐进式 JavaScript 框架，使用 Composition API
- **TypeScript** - JavaScript 的超集，提供类型安全
- **Element Plus** - Vue 3 组件库
- **Pinia** - Vue 状态管理库
- **Vite** - 快速的前端构建工具
- **Axios** - HTTP 客户端
- **Vue Router** - 路由管理
- **Vitest** - 单元测试框架

### 开发工具
- **Docker** - 容器化部署
- **Nginx** - 反向代理和静态文件服务
- **ESLint** - JavaScript/TypeScript 代码检查
- **Prettier** - 代码格式化
- **Black** - Python 代码格式化
- **Pre-commit** - Git 钩子管理

## 🚀 快速开始

### 环境要求

- **Python** 3.10+
- **Node.js** 18+
- **PostgreSQL** 12+
- **Redis** 6+
- **Docker** (可选，用于容器化部署)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/app-project-manager.git
cd app-project-manager
```

2. **后端设置**
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 开发依赖

# 复制环境变量配置
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

3. **前端设置**
```bash
cd frontend

# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env.local
# 编辑 .env.local 文件，配置 API 地址等信息
```

4. **数据库设置**
```bash
# 创建数据库
createdb app_manager

# 运行数据库迁移
cd backend
alembic upgrade head

# 创建初始用户（可选）
python scripts/create_superuser.py
```

5. **启动服务**

开发环境：
```bash
# 启动后端服务
cd backend
python main.py
# 或使用开发服务器
python start_server.py

# 启动前端服务
cd frontend
npm run dev
```

生产环境：
```bash
# 使用 Docker Compose
docker-compose up -d
```

6. **访问应用**
- 前端界面: http://localhost:3000
- 后端 API: http://localhost:8000
- API 文档: http://localhost:8000/docs
- 管理界面: http://localhost:8000/admin

## 📁 项目结构

```
app-project-manager/
├── backend/                    # 后端代码
│   ├── app/                   # 应用代码
│   │   ├── api/              # API 路由
│   │   │   └── api_v1/       # API v1 版本
│   │   ├── core/             # 核心配置
│   │   │   ├── config.py     # 应用配置
│   │   │   ├── database.py   # 数据库配置
│   │   │   ├── security.py   # 安全相关
│   │   │   └── logging.py    # 日志配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑服务
│   │   ├── middleware/       # 中间件
│   │   ├── utils/            # 工具函数
│   │   └── websocket/        # WebSocket 处理
│   ├── tests/                # 测试代码
│   │   ├── api/              # API 测试
│   │   ├── services/         # 服务测试
│   │   └── conftest.py       # 测试配置
│   ├── scripts/              # 脚本文件
│   ├── alembic/              # 数据库迁移
│   ├── requirements.txt      # 生产依赖
│   ├── requirements-dev.txt  # 开发依赖
│   └── pyproject.toml        # Python 项目配置
├── frontend/                  # 前端代码
│   ├── src/                  # 源代码
│   │   ├── components/       # Vue 组件
│   │   ├── views/            # 页面视图
│   │   ├── stores/           # Pinia 状态管理
│   │   ├── api/              # API 调用
│   │   ├── utils/            # 工具函数
│   │   ├── types/            # TypeScript 类型定义
│   │   └── styles/           # 样式文件
│   ├── tests/                # 测试代码
│   ├── public/               # 静态资源
│   ├── package.json          # Node.js 依赖
│   ├── tsconfig.json         # TypeScript 配置
│   ├── vite.config.ts        # Vite 配置
│   └── vitest.config.ts      # 测试配置
├── docs/                     # 文档
│   ├── api/                  # API 文档
│   ├── deployment/           # 部署文档
│   └── development/          # 开发文档
├── scripts/                  # 项目脚本
├── docker-compose.yml        # Docker 编排
├── .pre-commit-config.yaml   # Pre-commit 配置
├── .editorconfig            # 编�器配置
└── README.md                # 项目说明
```

## 📚 API 文档

启动后端服务后，访问以下地址查看 API 文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

主要 API 端点：
- `/api/v1/auth/` - 认证相关
- `/api/v1/users/` - 用户管理
- `/api/v1/apps/` - 应用管理
- `/api/v1/tasks/` - 任务管理
- `/api/v1/logs/` - 日志查询
- `/api/v1/system/` - 系统信息

## 🔧 开发指南

### 代码规范

- 后端遵循 PEP 8 规范，使用 Black 格式化
- 前端使用 ESLint + Prettier 进行代码检查和格式化
- 提交信息遵循 Conventional Commits 规范
- 使用 Pre-commit 钩子确保代码质量

### 开发工具

```bash
# 安装开发工具
pip install pre-commit
pre-commit install

# 代码格式化
python scripts/format_code.py

# 运行代码检查
python scripts/security_check.py

# 依赖检查
node frontend/scripts/check-deps.js
```

### 测试

```bash
# 后端测试
cd backend
pytest --cov=app --cov-report=html

# 前端测试
cd frontend
npm run test:coverage

# 运行所有测试
python scripts/test_coverage.py
```

### 性能监控

```bash
# 性能分析
python scripts/performance_check.py

# 内存使用分析
python -m memory_profiler backend/main.py
```

## 🚢 部署

### Docker 部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境部署

1. **环境准备**
   - 配置生产环境变量
   - 设置数据库连接
   - 配置 Redis 连接

2. **反向代理配置**
   - 使用 Nginx 作为反向代理
   - 配置 SSL 证书
   - 设置静态文件服务

3. **监控和日志**
   - 配置日志收集
   - 设置性能监控
   - 配置告警通知

详细部署文档请参考 [部署指南](docs/deployment/README.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 贡献类型

- 🐛 Bug 修复
- ✨ 新功能
- 📝 文档改进
- 🎨 代码优化
- 🧪 测试增强
- 🔧 工具改进

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 📧 邮箱: <EMAIL>
- 🌐 项目地址: https://github.com/your-username/app-project-manager
- 📖 文档: https://app-project-manager.readthedocs.io/
- 🐛 问题反馈: https://github.com/your-username/app-project-manager/issues

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

- [FastAPI](https://fastapi.tiangolo.com/) - 优秀的 Python Web 框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- 所有开源社区的贡献者
