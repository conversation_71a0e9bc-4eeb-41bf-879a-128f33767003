"""
菜单相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class MenuBase(BaseModel):
    """菜单基础模式"""

    name: str
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: int = 0
    is_hidden: bool = False
    is_external: bool = False


class MenuCreate(MenuBase):
    """菜单创建模式"""

    pass


class MenuUpdate(BaseModel):
    """菜单更新模式"""

    name: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None
    is_hidden: Optional[bool] = None
    is_external: Optional[bool] = None


class Menu(MenuBase):
    """菜单响应模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class MenuTree(Menu):
    """菜单树形结构模式"""

    children: List["MenuTree"] = []

    model_config = {"from_attributes": True}


class UserMenu(BaseModel):
    """用户菜单模式（用于前端路由）"""

    id: int
    name: str
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    sort_order: int = 0
    is_hidden: bool = False
    is_external: bool = False
    children: List["UserMenu"] = []

    model_config = {"from_attributes": True}


# 更新前向引用
MenuTree.model_rebuild()
UserMenu.model_rebuild()
