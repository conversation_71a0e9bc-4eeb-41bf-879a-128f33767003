"""
系统监控相关模型
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Integer, String, Text
from sqlalchemy.sql import func

from app.core.database import Base


class SystemMetrics(Base):
    """系统指标模型"""

    __tablename__ = "system_metrics"

    id = Column(Integer, primary_key=True, index=True, comment="指标ID")

    # CPU指标
    cpu_percent = Column(Float, nullable=True, comment="CPU使用率(%)")
    cpu_count = Column(Integer, nullable=True, comment="CPU核心数")
    cpu_freq_current = Column(Float, nullable=True, comment="当前CPU频率(MHz)")
    cpu_freq_max = Column(Float, nullable=True, comment="最大CPU频率(MHz)")

    # 内存指标
    memory_total = Column(Integer, nullable=True, comment="总内存(bytes)")
    memory_available = Column(Integer, nullable=True, comment="可用内存(bytes)")
    memory_used = Column(Integer, nullable=True, comment="已用内存(bytes)")
    memory_percent = Column(Float, nullable=True, comment="内存使用率(%)")

    # 交换分区指标
    swap_total = Column(Integer, nullable=True, comment="总交换分区(bytes)")
    swap_used = Column(Integer, nullable=True, comment="已用交换分区(bytes)")
    swap_percent = Column(Float, nullable=True, comment="交换分区使用率(%)")

    # 磁盘指标
    disk_total = Column(Integer, nullable=True, comment="总磁盘空间(bytes)")
    disk_used = Column(Integer, nullable=True, comment="已用磁盘空间(bytes)")
    disk_free = Column(Integer, nullable=True, comment="可用磁盘空间(bytes)")
    disk_percent = Column(Float, nullable=True, comment="磁盘使用率(%)")

    # 磁盘IO指标
    disk_read_bytes = Column(Integer, nullable=True, comment="磁盘读取字节数")
    disk_write_bytes = Column(Integer, nullable=True, comment="磁盘写入字节数")
    disk_read_count = Column(Integer, nullable=True, comment="磁盘读取次数")
    disk_write_count = Column(Integer, nullable=True, comment="磁盘写入次数")

    # 网络指标
    network_bytes_sent = Column(Integer, nullable=True, comment="网络发送字节数")
    network_bytes_recv = Column(Integer, nullable=True, comment="网络接收字节数")
    network_packets_sent = Column(Integer, nullable=True, comment="网络发送包数")
    network_packets_recv = Column(Integer, nullable=True, comment="网络接收包数")

    # 系统指标
    process_count = Column(Integer, nullable=True, comment="进程数量")
    load_avg_1 = Column(Float, nullable=True, comment="1分钟平均负载")
    load_avg_5 = Column(Float, nullable=True, comment="5分钟平均负载")
    load_avg_15 = Column(Float, nullable=True, comment="15分钟平均负载")
    uptime = Column(Float, nullable=True, comment="系统运行时间(秒)")

    # 时间戳
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )


class SystemAlert(Base):
    """系统告警模型"""

    __tablename__ = "system_alerts"

    id = Column(Integer, primary_key=True, index=True, comment="告警ID")
    alert_type = Column(String(50), nullable=False, comment="告警类型")
    severity = Column(
        String(20), default="medium", comment="严重程度(low/medium/high/critical)"
    )
    title = Column(String(200), nullable=False, comment="告警标题")
    message = Column(Text, nullable=False, comment="告警消息")

    # 告警规则
    metric_name = Column(String(100), nullable=False, comment="指标名称")
    threshold_value = Column(Float, nullable=False, comment="阈值")
    comparison_operator = Column(
        String(10), nullable=False, comment="比较操作符(>,<,>=,<=,==)"
    )
    current_value = Column(Float, nullable=True, comment="当前值")

    # 告警状态
    status = Column(
        String(20), default="active", comment="告警状态(active/acknowledged/resolved)"
    )
    is_active = Column(Boolean, default=True, comment="是否激活")

    # 时间信息
    triggered_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="触发时间"
    )
    acknowledged_at = Column(DateTime(timezone=True), nullable=True, comment="确认时间")
    resolved_at = Column(DateTime(timezone=True), nullable=True, comment="解决时间")

    # 其他信息
    acknowledged_by = Column(String(100), nullable=True, comment="确认人")
    resolved_by = Column(String(100), nullable=True, comment="解决人")
    notes = Column(Text, nullable=True, comment="备注")

    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )


class AlertRule(Base):
    """告警规则模型"""

    __tablename__ = "alert_rules"

    id = Column(Integer, primary_key=True, index=True, comment="规则ID")
    name = Column(String(100), nullable=False, comment="规则名称")
    description = Column(Text, nullable=True, comment="规则描述")

    # 规则配置
    metric_name = Column(String(100), nullable=False, comment="监控指标名称")
    threshold_value = Column(Float, nullable=False, comment="阈值")
    comparison_operator = Column(String(10), nullable=False, comment="比较操作符")
    severity = Column(String(20), default="medium", comment="严重程度")

    # 触发条件
    duration_minutes = Column(Integer, default=5, comment="持续时间(分钟)")
    evaluation_interval = Column(Integer, default=60, comment="评估间隔(秒)")

    # 通知配置
    notification_enabled = Column(Boolean, default=True, comment="是否启用通知")
    notification_channels = Column(Text, nullable=True, comment="通知渠道(JSON)")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_evaluation = Column(DateTime(timezone=True), nullable=True, comment="最后评估时间")
    last_triggered = Column(DateTime(timezone=True), nullable=True, comment="最后触发时间")

    # 创建信息
    created_by = Column(String(100), nullable=True, comment="创建者")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )


class SystemProcess(Base):
    """系统进程快照模型"""

    __tablename__ = "system_processes"

    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    snapshot_time = Column(
        DateTime(timezone=True), server_default=func.now(), comment="快照时间"
    )

    # 进程信息
    pid = Column(Integer, nullable=False, comment="进程ID")
    name = Column(String(200), nullable=False, comment="进程名称")
    status = Column(String(20), nullable=True, comment="进程状态")

    # 资源使用
    cpu_percent = Column(Float, nullable=True, comment="CPU使用率(%)")
    memory_percent = Column(Float, nullable=True, comment="内存使用率(%)")
    memory_rss = Column(Integer, nullable=True, comment="物理内存使用(bytes)")
    memory_vms = Column(Integer, nullable=True, comment="虚拟内存使用(bytes)")

    # 进程详情
    ppid = Column(Integer, nullable=True, comment="父进程ID")
    username = Column(String(100), nullable=True, comment="用户名")
    create_time = Column(Float, nullable=True, comment="创建时间戳")
    num_threads = Column(Integer, nullable=True, comment="线程数")

    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="记录创建时间"
    )
