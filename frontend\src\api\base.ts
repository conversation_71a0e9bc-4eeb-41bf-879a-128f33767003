/**
 * API服务基类
 */
import type { AxiosResponse } from 'axios'
import service from './request'
import type { 
  ApiResponse, 
  PaginatedResponse, 
  PaginationParams,
  RequestConfig 
} from '@/types/api'

export abstract class BaseApiService {
  protected baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  /**
   * 发送GET请求
   */
  protected async get<T = any>(
    url: string, 
    params?: Record<string, any>
  ): Promise<T> {
    const response: AxiosResponse<T> = await service.get(`${this.baseUrl}${url}`, {
      params
    })
    return response.data
  }

  /**
   * 发送POST请求
   */
  protected async post<T = any>(
    url: string, 
    data?: any,
    config?: Partial<RequestConfig>
  ): Promise<T> {
    const response: AxiosResponse<T> = await service.post(
      `${this.baseUrl}${url}`, 
      data,
      config
    )
    return response.data
  }

  /**
   * 发送PUT请求
   */
  protected async put<T = any>(
    url: string, 
    data?: any
  ): Promise<T> {
    const response: AxiosResponse<T> = await service.put(`${this.baseUrl}${url}`, data)
    return response.data
  }

  /**
   * 发送PATCH请求
   */
  protected async patch<T = any>(
    url: string, 
    data?: any
  ): Promise<T> {
    const response: AxiosResponse<T> = await service.patch(`${this.baseUrl}${url}`, data)
    return response.data
  }

  /**
   * 发送DELETE请求
   */
  protected async delete<T = any>(url: string): Promise<T> {
    const response: AxiosResponse<T> = await service.delete(`${this.baseUrl}${url}`)
    return response.data
  }

  /**
   * 分页查询
   */
  protected async paginate<T = any>(
    url: string,
    params: PaginationParams = {}
  ): Promise<PaginatedResponse<T>> {
    const {
      page = 1,
      size = 20,
      search,
      sort,
      order = 'desc'
    } = params

    const queryParams: Record<string, any> = {
      page,
      size,
      order
    }

    if (search) {
      queryParams.search = search
    }

    if (sort) {
      queryParams.sort = sort
    }

    return this.get<PaginatedResponse<T>>(url, queryParams)
  }

  /**
   * 批量删除
   */
  protected async batchDelete(
    url: string,
    ids: Array<string | number>
  ): Promise<void> {
    await this.post(`${url}/batch-delete`, { ids })
  }

  /**
   * 文件上传
   */
  protected async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response: AxiosResponse<T> = await service.post(
      `${this.baseUrl}${url}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(progress)
          }
        }
      }
    )

    return response.data
  }

  /**
   * 文件下载
   */
  protected async download(
    url: string,
    filename?: string,
    params?: Record<string, any>
  ): Promise<void> {
    const response: AxiosResponse<Blob> = await service.get(
      `${this.baseUrl}${url}`,
      {
        params,
        responseType: 'blob'
      }
    )

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    
    // 从响应头获取文件名或使用提供的文件名
    const contentDisposition = response.headers['content-disposition']
    const defaultFilename = filename || 'download'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      link.download = filenameMatch ? filenameMatch[1] : defaultFilename
    } else {
      link.download = defaultFilename
    }

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  /**
   * 健康检查
   */
  protected async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health')
      return true
    } catch {
      return false
    }
  }
}

/**
 * CRUD操作基类
 */
export abstract class CrudApiService<T, CreateT = Partial<T>, UpdateT = Partial<T>> extends BaseApiService {
  
  /**
   * 获取列表
   */
  async getList(params?: PaginationParams): Promise<PaginatedResponse<T>> {
    return this.paginate<T>('', params)
  }

  /**
   * 根据ID获取详情
   */
  async getById(id: string | number): Promise<T> {
    return this.get<T>(`/${id}`)
  }

  /**
   * 创建
   */
  async create(data: CreateT): Promise<T> {
    return this.post<T>('', data)
  }

  /**
   * 更新
   */
  async update(id: string | number, data: UpdateT): Promise<T> {
    return this.put<T>(`/${id}`, data)
  }

  /**
   * 删除
   */
  async deleteById(id: string | number): Promise<void> {
    await this.delete(`/${id}`)
  }

  /**
   * 批量删除
   */
  async batchDeleteByIds(ids: Array<string | number>): Promise<void> {
    await this.batchDelete('', ids)
  }
}
