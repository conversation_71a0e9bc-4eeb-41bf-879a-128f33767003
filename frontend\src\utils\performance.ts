/**
 * 前端性能优化工具
 */

// 性能监控类型
export interface PerformanceMetrics {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  memory?: {
    used: number
    total: number
  }
}

// 性能监控器
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initObservers()
  }

  private initObservers() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // 监控导航性能
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.logNavigationMetrics(entry as PerformanceNavigationTiming)
          }
        }
      })
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.push(navObserver)

      // 监控资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.logResourceMetrics(entry as PerformanceResourceTiming)
          }
        }
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.push(resourceObserver)
    }
  }

  private logNavigationMetrics(entry: PerformanceNavigationTiming) {
    console.log('Navigation Performance:', {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint()
    })
  }

  private logResourceMetrics(entry: PerformanceResourceTiming) {
    if (entry.duration > 1000) { // 只记录超过1秒的资源
      console.warn('Slow Resource:', {
        name: entry.name,
        duration: entry.duration,
        size: entry.transferSize
      })
    }
  }

  private getFirstPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    return firstPaint ? firstPaint.startTime : null
  }

  private getFirstContentfulPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : null
  }

  // 开始性能测量
  start(name: string): void {
    const startTime = performance.now()
    this.metrics.set(name, {
      name,
      startTime,
      memory: this.getMemoryInfo()
    })
  }

  // 结束性能测量
  end(name: string): PerformanceMetrics | null {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    const completedMetric: PerformanceMetrics = {
      ...metric,
      endTime,
      duration,
      memory: this.getMemoryInfo()
    }

    this.metrics.set(name, completedMetric)
    
    // 记录慢操作
    if (duration > 100) {
      console.warn(`Slow operation "${name}": ${duration.toFixed(2)}ms`)
    }

    return completedMetric
  }

  private getMemoryInfo() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize
      }
    }
    return undefined
  }

  // 获取所有指标
  getMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values())
  }

  // 清理观察器
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor()

// 性能装饰器
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      performanceMonitor.start(metricName)
      try {
        const result = await originalMethod.apply(this, args)
        performanceMonitor.end(metricName)
        return result
      } catch (error) {
        performanceMonitor.end(metricName)
        throw error
      }
    }

    return descriptor
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false

  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 图片懒加载
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null

  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              const src = img.dataset.src
              if (src) {
                img.src = src
                img.removeAttribute('data-src')
                this.observer?.unobserve(img)
              }
            }
          })
        },
        {
          rootMargin: '50px'
        }
      )
    }
  }

  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.observe(img)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// 虚拟滚动工具
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0

  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container
    this.itemHeight = itemHeight
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2
  }

  getVisibleRange(scrollTop: number, totalItems: number) {
    this.startIndex = Math.floor(scrollTop / this.itemHeight)
    this.endIndex = Math.min(this.startIndex + this.visibleCount, totalItems)

    return {
      startIndex: this.startIndex,
      endIndex: this.endIndex,
      offsetY: this.startIndex * this.itemHeight
    }
  }
}

// 内存泄漏检测
export class MemoryLeakDetector {
  private initialMemory: number = 0
  private checkInterval: NodeJS.Timeout | null = null

  start() {
    if ('memory' in performance) {
      this.initialMemory = (performance as any).memory.usedJSHeapSize
      
      this.checkInterval = setInterval(() => {
        const currentMemory = (performance as any).memory.usedJSHeapSize
        const memoryIncrease = currentMemory - this.initialMemory
        
        if (memoryIncrease > 50 * 1024 * 1024) { // 50MB
          console.warn('Potential memory leak detected:', {
            initial: this.formatBytes(this.initialMemory),
            current: this.formatBytes(currentMemory),
            increase: this.formatBytes(memoryIncrease)
          })
        }
      }, 30000) // 每30秒检查一次
    }
  }

  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }
}

// 导出工具实例
export const lazyImageLoader = new LazyImageLoader()
export const memoryLeakDetector = new MemoryLeakDetector()

// 性能优化建议
export function getPerformanceRecommendations(): string[] {
  const recommendations: string[] = []

  // 检查首屏加载时间
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart
    if (loadTime > 3000) {
      recommendations.push('首屏加载时间过长，建议优化资源加载')
    }
  }

  // 检查资源大小
  const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
  const largeResources = resources.filter(resource => resource.transferSize > 1024 * 1024) // 1MB
  if (largeResources.length > 0) {
    recommendations.push('存在大型资源文件，建议进行压缩或分割')
  }

  // 检查内存使用
  if ('memory' in performance) {
    const memory = (performance as any).memory
    if (memory.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
      recommendations.push('内存使用量较高，建议检查是否存在内存泄漏')
    }
  }

  return recommendations
}
