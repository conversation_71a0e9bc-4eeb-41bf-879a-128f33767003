"""
任务执行服务单元测试
"""
from datetime import datetime, timedelta
from unittest.mock import AsyncM<PERSON>, Mock, patch

import pytest
from sqlalchemy.orm import Session

from app.models.app import App
from app.models.task import Task, TaskExecution
from app.services.task_execution_service import TaskExecutionService


class TestTaskExecutionService:
    """任务执行服务测试类"""

    def test_get_task_executions(self, db_session: Session):
        """测试获取任务执行记录"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        execution1 = TaskExecution(
            task_id=task.id,
            status="success",
            start_time=datetime.utcnow() - timedelta(hours=1),
            end_time=datetime.utcnow() - timedelta(minutes=59),
        )
        execution2 = TaskExecution(
            task_id=task.id,
            status="failed",
            start_time=datetime.utcnow() - timedelta(hours=2),
            end_time=datetime.utcnow() - timedelta(hours=1, minutes=58),
        )
        db_session.add_all([execution1, execution2])
        db_session.commit()

        # 测试获取所有执行记录
        executions = service.get_task_executions()
        assert len(executions) == 2

        # 测试按任务ID过滤
        executions = service.get_task_executions(task_id=task.id)
        assert len(executions) == 2
        assert all(e.task_id == task.id for e in executions)

        # 测试按状态过滤
        executions = service.get_task_executions(status="success")
        assert len(executions) == 1
        assert executions[0].status == "success"

        # 测试分页
        executions = service.get_task_executions(skip=1, limit=1)
        assert len(executions) == 1

    def test_get_execution_by_id(self, db_session: Session):
        """测试根据ID获取执行记录"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        execution = TaskExecution(
            task_id=task.id, status="success", result="Hello World"
        )
        db_session.add(execution)
        db_session.commit()

        # 测试获取存在的执行记录
        result = service.get_execution_by_id(execution.id)
        assert result is not None
        assert result.status == "success"
        assert result.result == "Hello World"

        # 测试获取不存在的执行记录
        result = service.get_execution_by_id(999)
        assert result is None

    @patch("subprocess.run")
    def test_execute_task_success(self, mock_run, db_session: Session):
        """测试成功执行任务"""
        # 设置模拟
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = "Hello World"
        mock_result.stderr = ""
        mock_run.return_value = mock_result

        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            timeout_seconds=300,
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        # 执行任务
        execution = service.execute_task(task.id)

        assert execution.status == "success"
        assert execution.result == "Hello World"
        assert execution.start_time is not None
        assert execution.end_time is not None
        assert execution.error_message is None

        # 验证subprocess.run被正确调用
        mock_run.assert_called_once_with(
            "echo hello",
            shell=True,
            capture_output=True,
            text=True,
            timeout=300,
            cwd=None,
        )

    @patch("subprocess.run")
    def test_execute_task_failure(self, mock_run, db_session: Session):
        """测试任务执行失败"""
        # 设置模拟
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stdout = ""
        mock_result.stderr = "Command failed"
        mock_run.return_value = mock_result

        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="invalid_command",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        # 执行任务
        execution = service.execute_task(task.id)

        assert execution.status == "failed"
        assert execution.result == ""
        assert execution.error_message == "Command failed"
        assert execution.start_time is not None
        assert execution.end_time is not None

    @patch("subprocess.run")
    def test_execute_task_timeout(self, mock_run, db_session: Session):
        """测试任务执行超时"""
        import subprocess

        # 设置模拟超时异常
        mock_run.side_effect = subprocess.TimeoutExpired("echo hello", 300)

        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="sleep 600",
            timeout_seconds=300,
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        # 执行任务
        execution = service.execute_task(task.id)

        assert execution.status == "failed"
        assert "timeout" in execution.error_message.lower()
        assert execution.start_time is not None
        assert execution.end_time is not None

    def test_execute_task_not_found(self, db_session: Session):
        """测试执行不存在的任务"""
        service = TaskExecutionService(db_session)

        with pytest.raises(ValueError) as exc_info:
            service.execute_task(999)

        assert "Task not found" in str(exc_info.value)

    def test_get_execution_statistics(self, db_session: Session):
        """测试获取执行统计信息"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        # 创建执行记录
        executions = [
            TaskExecution(task_id=task.id, status="success"),
            TaskExecution(task_id=task.id, status="success"),
            TaskExecution(task_id=task.id, status="failed"),
            TaskExecution(task_id=task.id, status="running"),
        ]
        db_session.add_all(executions)
        db_session.commit()

        # 获取统计信息
        stats = service.get_execution_statistics()

        assert stats["total_executions"] == 4
        assert stats["success_count"] == 2
        assert stats["failed_count"] == 1
        assert stats["running_count"] == 1
        assert stats["success_rate"] == 50.0  # 2/4 * 100

        # 测试按任务ID过滤的统计
        task_stats = service.get_execution_statistics(task_id=task.id)
        assert task_stats["total_executions"] == 4

    def test_cancel_execution(self, db_session: Session):
        """测试取消任务执行"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="sleep 600",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        execution = TaskExecution(
            task_id=task.id, status="running", start_time=datetime.utcnow()
        )
        db_session.add(execution)
        db_session.commit()

        # 取消执行
        result = service.cancel_execution(execution.id)

        assert result is True

        # 验证状态已更新
        db_session.refresh(execution)
        assert execution.status == "cancelled"
        assert execution.end_time is not None

    def test_cancel_execution_not_running(self, db_session: Session):
        """测试取消非运行中的任务执行"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        execution = TaskExecution(
            task_id=task.id,
            status="success",
            start_time=datetime.utcnow() - timedelta(minutes=5),
            end_time=datetime.utcnow() - timedelta(minutes=4),
        )
        db_session.add(execution)
        db_session.commit()

        # 尝试取消已完成的执行
        with pytest.raises(ValueError) as exc_info:
            service.cancel_execution(execution.id)

        assert "Cannot cancel" in str(exc_info.value)

    def test_retry_execution(self, db_session: Session):
        """测试重试任务执行"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        failed_execution = TaskExecution(
            task_id=task.id,
            status="failed",
            start_time=datetime.utcnow() - timedelta(minutes=5),
            end_time=datetime.utcnow() - timedelta(minutes=4),
            error_message="Command failed",
        )
        db_session.add(failed_execution)
        db_session.commit()

        # 模拟成功的重试
        with patch("subprocess.run") as mock_run:
            mock_result = Mock()
            mock_result.returncode = 0
            mock_result.stdout = "Hello World"
            mock_result.stderr = ""
            mock_run.return_value = mock_result

            new_execution = service.retry_execution(failed_execution.id)

            assert new_execution.status == "success"
            assert new_execution.result == "Hello World"
            assert new_execution.id != failed_execution.id  # 新的执行记录

    def test_get_recent_executions(self, db_session: Session):
        """测试获取最近的执行记录"""
        service = TaskExecutionService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        task = Task(
            app_id=app.id,
            name="Test Task",
            task_type="command",
            command="echo hello",
            created_by=1,
        )
        db_session.add(task)
        db_session.commit()

        # 创建不同时间的执行记录
        now = datetime.utcnow()
        executions = [
            TaskExecution(
                task_id=task.id,
                status="success",
                start_time=now - timedelta(hours=1),
                end_time=now - timedelta(hours=1) + timedelta(minutes=1),
            ),
            TaskExecution(
                task_id=task.id,
                status="failed",
                start_time=now - timedelta(hours=2),
                end_time=now - timedelta(hours=2) + timedelta(minutes=1),
            ),
            TaskExecution(
                task_id=task.id,
                status="success",
                start_time=now - timedelta(days=2),
                end_time=now - timedelta(days=2) + timedelta(minutes=1),
            ),
        ]
        db_session.add_all(executions)
        db_session.commit()

        # 获取最近24小时的执行记录
        recent_executions = service.get_recent_executions(hours=24)
        assert len(recent_executions) == 2  # 只有前两个在24小时内

        # 获取最近1小时的执行记录
        recent_executions = service.get_recent_executions(hours=1)
        assert len(recent_executions) == 1  # 只有第一个在1小时内
