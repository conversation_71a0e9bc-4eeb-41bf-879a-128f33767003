[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    migrations,
    alembic/versions,
    build,
    dist,
    *.egg-info
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401
    # Test files can have unused imports and long lines
    tests/*:F401,E501
    # Settings files can have long lines
    */settings.py:E501
    */config.py:E501

[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
show_error_codes = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-psycopg2.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-psutil.*]
ignore_missing_imports = True
