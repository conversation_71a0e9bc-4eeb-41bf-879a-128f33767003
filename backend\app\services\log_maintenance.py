"""
日志维护服务
"""
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Optional

import schedule
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.services.log import log_collection_service


class LogMaintenanceService:
    """日志维护服务"""

    def __init__(self):
        self.is_running = False
        self.maintenance_thread: Optional[threading.Thread] = None
        self._setup_schedule()

    def _setup_schedule(self):
        """设置维护计划"""
        # 每天凌晨2点清理旧日志
        schedule.every().day.at("02:00").do(self._cleanup_old_logs)

        # 每小时检查日志轮转
        schedule.every().hour.do(self._check_log_rotation)

        # 每10分钟检查告警规则
        schedule.every(10).minutes.do(self._check_alert_rules)

    def start(self):
        """启动维护服务"""
        if self.is_running:
            return

        self.is_running = True
        self.maintenance_thread = threading.Thread(target=self._run_maintenance)
        self.maintenance_thread.daemon = True
        self.maintenance_thread.start()
        print("Log maintenance service started")

    def stop(self):
        """停止维护服务"""
        self.is_running = False
        if self.maintenance_thread:
            self.maintenance_thread.join(timeout=5)
        print("Log maintenance service stopped")

    def _run_maintenance(self):
        """运行维护任务"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                print(f"Error in log maintenance: {e}")
                time.sleep(60)

    def _cleanup_old_logs(self):
        """清理旧日志"""
        try:
            db = SessionLocal()
            try:
                log_collection_service.cleanup_old_logs(db, days=30)
                print(f"Log cleanup completed at {datetime.now()}")
            finally:
                db.close()
        except Exception as e:
            print(f"Error in log cleanup: {e}")

    def _check_log_rotation(self):
        """检查日志轮转"""
        try:
            # 这里可以添加额外的日志轮转检查逻辑
            print(f"Log rotation check completed at {datetime.now()}")
        except Exception as e:
            print(f"Error in log rotation check: {e}")

    def _check_alert_rules(self):
        """检查告警规则"""
        try:
            # 这里可以添加定期检查告警规则的逻辑
            print(f"Alert rules check completed at {datetime.now()}")
        except Exception as e:
            print(f"Error in alert rules check: {e}")


# 全局日志维护服务实例
log_maintenance_service = LogMaintenanceService()
