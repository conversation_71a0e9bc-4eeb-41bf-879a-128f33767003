"""
实时服务单元测试
"""
import asyncio
from datetime import datetime
from unittest.mock import AsyncM<PERSON>, MagicMock, Mock, patch

import pytest

from app.services.realtime_service import RealtimeService, realtime_service


class TestRealtimeService:
    """实时服务测试类"""

    @pytest.fixture
    def service(self):
        """创建实时服务实例"""
        return RealtimeService()

    @pytest.mark.asyncio
    async def test_start_service(self, service):
        """测试启动实时服务"""
        assert service.is_running is False
        assert len(service.tasks) == 0

        with patch.object(
            service, "_monitor_system_metrics", new_callable=AsyncMock
        ) as mock_metrics, patch.object(
            service, "_monitor_app_status", new_callable=AsyncMock
        ) as mock_app, patch.object(
            service, "_monitor_task_executions", new_callable=AsyncMock
        ) as mock_task, patch.object(
            service, "_cleanup_connections", new_callable=AsyncMock
        ) as mock_cleanup:
            await service.start()

            assert service.is_running is True
            assert len(service.tasks) == 4

            # 停止服务以清理任务
            await service.stop()

    @pytest.mark.asyncio
    async def test_stop_service(self, service):
        """测试停止实时服务"""
        # 先启动服务
        with patch.object(
            service, "_monitor_system_metrics", new_callable=AsyncMock
        ), patch.object(
            service, "_monitor_app_status", new_callable=AsyncMock
        ), patch.object(
            service, "_monitor_task_executions", new_callable=AsyncMock
        ), patch.object(
            service, "_cleanup_connections", new_callable=AsyncMock
        ):
            await service.start()
            assert service.is_running is True

            # 停止服务
            await service.stop()
            assert service.is_running is False
            assert len(service.tasks) == 0

    @pytest.mark.asyncio
    async def test_start_already_running(self, service):
        """测试启动已运行的服务"""
        service.is_running = True

        await service.start()

        # 应该没有创建新任务
        assert len(service.tasks) == 0

    @pytest.mark.asyncio
    async def test_stop_not_running(self, service):
        """测试停止未运行的服务"""
        assert service.is_running is False

        await service.stop()

        # 应该正常完成，不抛出异常
        assert service.is_running is False

    @pytest.mark.asyncio
    @patch("app.services.realtime_service.connection_manager")
    @patch("app.services.realtime_service.send_system_metrics")
    async def test_monitor_system_metrics(
        self, mock_send_metrics, mock_connection_manager, service
    ):
        """测试系统指标监控"""
        # 模拟有活跃连接
        mock_connection_manager.get_active_apps.return_value = [1, 2]

        # 模拟系统指标
        mock_metrics = {"cpu_usage": 45.5, "memory_usage": 60.2, "disk_usage": 30.1}
        service.system_monitor.get_system_metrics = AsyncMock(return_value=mock_metrics)

        # 启动监控（只运行一次）
        service.is_running = True

        # 创建一个任务来运行监控方法
        async def run_once():
            # 模拟监控循环的一次迭代
            active_apps = mock_connection_manager.get_active_apps()
            if active_apps:
                metrics = await service.system_monitor.get_system_metrics()
                for app_id in active_apps:
                    await mock_send_metrics(app_id, metrics)

        await run_once()

        # 验证系统指标被发送给所有活跃应用
        assert mock_send_metrics.call_count == 2
        mock_send_metrics.assert_any_call(1, mock_metrics)
        mock_send_metrics.assert_any_call(2, mock_metrics)

    @pytest.mark.asyncio
    @patch("app.services.realtime_service.connection_manager")
    @patch("app.services.realtime_service.get_db")
    @patch("app.services.realtime_service.send_service_status_update")
    async def test_monitor_app_status(
        self, mock_send_status, mock_get_db, mock_connection_manager, service
    ):
        """测试应用状态监控"""
        # 模拟数据库会话
        mock_db = Mock()
        mock_get_db.return_value.__next__ = Mock(return_value=mock_db)

        # 模拟有活跃连接
        mock_connection_manager.get_active_apps.return_value = [1]

        # 模拟应用服务
        mock_service = Mock()
        mock_service.app_id = 1
        mock_service.service_type = "frontend"
        mock_service.status = "running"
        mock_service.pid = 1234
        mock_service.stopped_at = None

        mock_db.query.return_value.filter.return_value.all.return_value = [mock_service]

        # 模拟进程不存在
        with patch("psutil.pid_exists", return_value=False):
            service.is_running = True

            # 创建一个任务来运行监控方法
            async def run_once():
                active_apps = mock_connection_manager.get_active_apps()
                if active_apps:
                    for app_id in active_apps:
                        services = mock_db.query().filter().all()
                        for svc in services:
                            if svc.status == "running" and svc.pid:
                                import psutil

                                if not psutil.pid_exists(svc.pid):
                                    svc.status = "stopped"
                                    svc.pid = None
                                    svc.stopped_at = datetime.utcnow()
                                    mock_db.commit()
                                    await mock_send_status(
                                        app_id, svc.service_type, "stopped"
                                    )

            await run_once()

            # 验证服务状态被更新
            assert mock_service.status == "stopped"
            assert mock_service.pid is None
            assert mock_service.stopped_at is not None
            mock_send_status.assert_called_once_with(1, "frontend", "stopped")

    @pytest.mark.asyncio
    @patch("app.services.realtime_service.send_log_message")
    async def test_push_log_message(self, mock_send_log, service):
        """测试推送日志消息"""
        app_id = 1
        log_data = {
            "level": "INFO",
            "message": "Test log message",
            "timestamp": datetime.now().isoformat(),
        }

        await service.push_log_message(app_id, log_data)

        mock_send_log.assert_called_once_with(app_id, log_data)

    @pytest.mark.asyncio
    @patch("app.services.realtime_service.send_alert_triggered")
    async def test_push_alert(self, mock_send_alert, service):
        """测试推送告警消息"""
        app_id = 1
        alert_data = {"alert_id": 123, "name": "High CPU Usage", "severity": "high"}

        await service.push_alert(app_id, alert_data)

        mock_send_alert.assert_called_once_with(app_id, alert_data)

    @patch("app.services.realtime_service.connection_manager")
    def test_get_connection_stats(self, mock_connection_manager, service):
        """测试获取连接统计信息"""
        # 模拟连接管理器返回值
        mock_connection_manager.get_active_apps.return_value = [1, 2, 3]
        mock_connection_manager.get_connection_count.side_effect = lambda app_id=None: {
            None: 5,  # 总连接数
            1: 2,  # 应用1的连接数
            2: 2,  # 应用2的连接数
            3: 1,  # 应用3的连接数
        }.get(app_id, 0)

        service.is_running = True
        stats = service.get_connection_stats()

        assert stats["total_connections"] == 5
        assert stats["active_apps"] == 3
        assert stats["app_connections"] == {1: 2, 2: 2, 3: 1}
        assert stats["is_running"] is True


class TestRealtimeServiceGlobal:
    """全局实时服务测试类"""

    @pytest.mark.asyncio
    @patch.object(realtime_service, "start")
    async def test_start_realtime_service(self, mock_start):
        """测试启动全局实时服务"""
        from app.services.realtime_service import start_realtime_service

        await start_realtime_service()

        mock_start.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(realtime_service, "stop")
    async def test_stop_realtime_service(self, mock_stop):
        """测试停止全局实时服务"""
        from app.services.realtime_service import stop_realtime_service

        await stop_realtime_service()

        mock_stop.assert_called_once()

    def test_get_realtime_service(self):
        """测试获取全局实时服务实例"""
        from app.services.realtime_service import get_realtime_service

        service = get_realtime_service()

        assert service is realtime_service
        assert isinstance(service, RealtimeService)


class TestRealtimeServiceIntegration:
    """实时服务集成测试类"""

    @pytest.mark.asyncio
    async def test_service_lifecycle(self):
        """测试服务完整生命周期"""
        service = RealtimeService()

        # 初始状态
        assert service.is_running is False
        assert len(service.tasks) == 0

        # 启动服务
        with patch.object(
            service, "_monitor_system_metrics", new_callable=AsyncMock
        ), patch.object(
            service, "_monitor_app_status", new_callable=AsyncMock
        ), patch.object(
            service, "_monitor_task_executions", new_callable=AsyncMock
        ), patch.object(
            service, "_cleanup_connections", new_callable=AsyncMock
        ):
            await service.start()
            assert service.is_running is True
            assert len(service.tasks) == 4

            # 停止服务
            await service.stop()
            assert service.is_running is False
            assert len(service.tasks) == 0

    @pytest.mark.asyncio
    async def test_error_handling_in_monitoring(self):
        """测试监控过程中的错误处理"""
        service = RealtimeService()

        # 模拟监控方法抛出异常
        async def failing_monitor():
            raise Exception("Monitor failed")

        with patch.object(
            service, "_monitor_system_metrics", side_effect=failing_monitor
        ), patch.object(
            service, "_monitor_app_status", new_callable=AsyncMock
        ), patch.object(
            service, "_monitor_task_executions", new_callable=AsyncMock
        ), patch.object(
            service, "_cleanup_connections", new_callable=AsyncMock
        ):
            await service.start()

            # 等待一小段时间让任务运行
            await asyncio.sleep(0.1)

            # 服务应该仍在运行，即使某个监控任务失败
            assert service.is_running is True

            await service.stop()

    @pytest.mark.asyncio
    @patch("app.services.realtime_service.connection_manager")
    async def test_no_active_connections(self, mock_connection_manager):
        """测试没有活跃连接时的行为"""
        service = RealtimeService()

        # 模拟没有活跃连接
        mock_connection_manager.get_active_apps.return_value = []

        service.is_running = True

        # 运行系统指标监控
        async def run_metrics_once():
            active_apps = mock_connection_manager.get_active_apps()
            if active_apps:
                # 不应该执行到这里
                assert False, "Should not send metrics when no active apps"

        await run_metrics_once()

        # 测试通过，没有异常抛出
        assert True
