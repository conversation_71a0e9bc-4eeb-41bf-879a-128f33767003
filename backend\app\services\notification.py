"""
通知服务
"""
import logging
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.log import LogAlertRecord


class NotificationService:
    """通知服务"""

    def __init__(self):
        self.logger = logging.getLogger("notification")
        self.email_config = {
            "smtp_server": getattr(settings, "SMTP_SERVER", "localhost"),
            "smtp_port": getattr(settings, "SMTP_PORT", 587),
            "smtp_username": getattr(settings, "SMTP_USERNAME", ""),
            "smtp_password": getattr(settings, "SMTP_PASSWORD", ""),
            "from_email": getattr(settings, "FROM_EMAIL", "<EMAIL>"),
            "use_tls": getattr(settings, "SMTP_USE_TLS", True),
        }

    def send_alert_notification(
        self, alert_record: LogAlertRecord, recipients: List[str]
    ):
        """发送告警通知"""
        try:
            # 发送邮件通知
            if recipients:
                self._send_email_notification(alert_record, recipients)

            # 这里可以添加其他通知方式，如短信、钉钉、企业微信等
            # self._send_sms_notification(alert_record, phone_numbers)
            # self._send_dingtalk_notification(alert_record, webhook_url)

            self.logger.info(f"Alert notification sent for record {alert_record.id}")

        except Exception as e:
            self.logger.error(f"Failed to send alert notification: {str(e)}")

    def _send_email_notification(
        self, alert_record: LogAlertRecord, recipients: List[str]
    ):
        """发送邮件通知"""
        try:
            # 创建邮件内容
            subject = f"日志告警 - {alert_record.alert_rule.rule_name}"

            html_content = self._generate_email_html(alert_record)
            text_content = self._generate_email_text(alert_record)

            # 创建邮件消息
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = self.email_config["from_email"]
            msg["To"] = ", ".join(recipients)

            # 添加文本和HTML内容
            text_part = MIMEText(text_content, "plain", "utf-8")
            html_part = MIMEText(html_content, "html", "utf-8")

            msg.attach(text_part)
            msg.attach(html_part)

            # 发送邮件
            with smtplib.SMTP(
                self.email_config["smtp_server"], self.email_config["smtp_port"]
            ) as server:
                if self.email_config["use_tls"]:
                    server.starttls()

                if self.email_config["smtp_username"]:
                    server.login(
                        self.email_config["smtp_username"],
                        self.email_config["smtp_password"],
                    )

                server.send_message(msg)

            self.logger.info(f"Email notification sent to {recipients}")

        except Exception as e:
            self.logger.error(f"Failed to send email notification: {str(e)}")
            raise

    def _generate_email_html(self, alert_record: LogAlertRecord) -> str:
        """生成HTML邮件内容"""
        return f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .alert-container {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
                .alert-header {{ background-color: #dc3545; color: white; padding: 10px; border-radius: 5px; margin-bottom: 15px; }}
                .alert-info {{ margin-bottom: 10px; }}
                .alert-info strong {{ color: #495057; }}
                .timestamp {{ color: #6c757d; font-size: 0.9em; }}
            </style>
        </head>
        <body>
            <div class="alert-container">
                <div class="alert-header">
                    <h2>🚨 日志告警通知</h2>
                </div>
                
                <div class="alert-info">
                    <strong>告警规则:</strong> {alert_record.alert_rule.rule_name}
                </div>
                
                <div class="alert-info">
                    <strong>应用ID:</strong> {alert_record.app_id}
                </div>
                
                <div class="alert-info">
                    <strong>日志级别:</strong> {alert_record.alert_rule.level}
                </div>
                
                <div class="alert-info">
                    <strong>匹配模式:</strong> {alert_record.alert_rule.pattern}
                </div>
                
                <div class="alert-info">
                    <strong>触发数量:</strong> {alert_record.log_count} 条日志
                </div>
                
                <div class="alert-info">
                    <strong>时间窗口:</strong> {alert_record.alert_rule.time_window} 分钟
                </div>
                
                <div class="alert-info">
                    <strong>触发时间:</strong> {alert_record.triggered_at.strftime('%Y-%m-%d %H:%M:%S')}
                </div>
                
                <div class="alert-info">
                    <strong>告警消息:</strong><br>
                    {alert_record.message}
                </div>
                
                <div class="timestamp">
                    此邮件由应用项目管理系统自动发送，请勿回复。
                </div>
            </div>
        </body>
        </html>
        """

    def _generate_email_text(self, alert_record: LogAlertRecord) -> str:
        """生成纯文本邮件内容"""
        return f"""
日志告警通知

告警规则: {alert_record.alert_rule.rule_name}
应用ID: {alert_record.app_id}
日志级别: {alert_record.alert_rule.level}
匹配模式: {alert_record.alert_rule.pattern}
触发数量: {alert_record.log_count} 条日志
时间窗口: {alert_record.alert_rule.time_window} 分钟
触发时间: {alert_record.triggered_at.strftime('%Y-%m-%d %H:%M:%S')}

告警消息:
{alert_record.message}

---
此邮件由应用项目管理系统自动发送，请勿回复。
        """

    def _send_sms_notification(
        self, alert_record: LogAlertRecord, phone_numbers: List[str]
    ):
        """发送短信通知（示例实现）"""
        # 这里可以集成短信服务提供商的API
        # 如阿里云短信、腾讯云短信等
        pass

    def _send_dingtalk_notification(
        self, alert_record: LogAlertRecord, webhook_url: str
    ):
        """发送钉钉通知（示例实现）"""
        try:
            import requests

            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": f"日志告警 - {alert_record.alert_rule.rule_name}",
                    "text": f"""
### 🚨 日志告警通知

**告警规则:** {alert_record.alert_rule.rule_name}

**应用ID:** {alert_record.app_id}

**日志级别:** {alert_record.alert_rule.level}

**触发数量:** {alert_record.log_count} 条日志

**触发时间:** {alert_record.triggered_at.strftime('%Y-%m-%d %H:%M:%S')}

**告警消息:** {alert_record.message}
                    """,
                },
            }

            response = requests.post(webhook_url, json=message, timeout=10)
            response.raise_for_status()

            self.logger.info("DingTalk notification sent successfully")

        except Exception as e:
            self.logger.error(f"Failed to send DingTalk notification: {str(e)}")

    def test_notification(
        self, recipients: List[str], notification_type: str = "email"
    ):
        """测试通知功能"""
        try:
            if notification_type == "email":
                # 创建测试邮件
                subject = "日志告警系统 - 测试通知"

                html_content = """
                <html>
                <body>
                    <h2>📧 测试通知</h2>
                    <p>这是一封测试邮件，用于验证日志告警通知功能是否正常工作。</p>
                    <p>如果您收到此邮件，说明通知系统配置正确。</p>
                    <p><em>发送时间: {}</em></p>
                </body>
                </html>
                """.format(
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                )

                text_content = f"""
测试通知

这是一条测试消息，用于验证日志告警通知功能是否正常工作。
如果您收到此消息，说明通知系统配置正确。

发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """

                # 创建邮件消息
                msg = MIMEMultipart("alternative")
                msg["Subject"] = subject
                msg["From"] = self.email_config["from_email"]
                msg["To"] = ", ".join(recipients)

                text_part = MIMEText(text_content, "plain", "utf-8")
                html_part = MIMEText(html_content, "html", "utf-8")

                msg.attach(text_part)
                msg.attach(html_part)

                # 发送邮件
                with smtplib.SMTP(
                    self.email_config["smtp_server"], self.email_config["smtp_port"]
                ) as server:
                    if self.email_config["use_tls"]:
                        server.starttls()

                    if self.email_config["smtp_username"]:
                        server.login(
                            self.email_config["smtp_username"],
                            self.email_config["smtp_password"],
                        )

                    server.send_message(msg)

                return {"success": True, "message": "Test email sent successfully"}

            else:
                return {
                    "success": False,
                    "message": f"Unsupported notification type: {notification_type}",
                }

        except Exception as e:
            self.logger.error(f"Failed to send test notification: {str(e)}")
            return {"success": False, "message": str(e)}


# 全局通知服务实例
notification_service = NotificationService()
