/**
 * 前端懒加载工具
 */
import { defineAsyncComponent, type AsyncComponentLoader, type Component } from 'vue'
import { ElLoading } from 'element-plus'

// 懒加载配置
interface LazyLoadOptions {
  loading?: Component
  error?: Component
  delay?: number
  timeout?: number
  suspensible?: boolean
  onError?: (error: Error, retry: () => void, fail: () => void, attempts: number) => any
}

// 默认加载组件
const DefaultLoadingComponent = {
  template: `
    <div class="lazy-loading">
      <el-loading-spinner />
      <p>加载中...</p>
    </div>
  `,
  style: `
    .lazy-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #666;
    }
  `
}

// 默认错误组件
const DefaultErrorComponent = {
  template: `
    <div class="lazy-error">
      <p>组件加载失败</p>
      <button @click="retry">重试</button>
    </div>
  `,
  props: ['retry'],
  style: `
    .lazy-error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #f56c6c;
    }
    .lazy-error button {
      margin-top: 10px;
      padding: 8px 16px;
      background: #409eff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  `
}

/**
 * 创建懒加载组件
 */
export function createLazyComponent(
  loader: AsyncComponentLoader,
  options: LazyLoadOptions = {}
) {
  return defineAsyncComponent({
    loader,
    loadingComponent: options.loading || DefaultLoadingComponent,
    errorComponent: options.error || DefaultErrorComponent,
    delay: options.delay || 200,
    timeout: options.timeout || 10000,
    suspensible: options.suspensible ?? true,
    onError: options.onError || ((error, retry, fail, attempts) => {
      console.error('Lazy component loading failed:', error)
      if (attempts <= 3) {
        retry()
      } else {
        fail()
      }
    })
  })
}

/**
 * 路由懒加载
 */
export function lazyRoute(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    delay: 100,
    timeout: 15000
  })
}

/**
 * 图片懒加载指令
 */
export const vLazyImage = {
  mounted(el: HTMLImageElement, binding: any) {
    const { value: src, modifiers } = binding
    
    // 设置默认图片
    if (modifiers.placeholder) {
      el.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
    }
    
    // 创建 Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            
            // 创建新的图片对象来预加载
            const newImg = new Image()
            newImg.onload = () => {
              img.src = src
              img.classList.add('lazy-loaded')
            }
            newImg.onerror = () => {
              img.classList.add('lazy-error')
              if (modifiers.error) {
                img.src = modifiers.error
              }
            }
            newImg.src = src
            
            observer.unobserve(img)
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )
    
    observer.observe(el)
    
    // 保存 observer 以便清理
    el._lazyObserver = observer
  },
  
  unmounted(el: HTMLImageElement) {
    if (el._lazyObserver) {
      el._lazyObserver.disconnect()
    }
  }
}

/**
 * 内容懒加载指令
 */
export const vLazyContent = {
  mounted(el: HTMLElement, binding: any) {
    const { value: loadFn } = binding
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (typeof loadFn === 'function') {
              loadFn()
            }
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '100px'
      }
    )
    
    observer.observe(el)
    el._contentObserver = observer
  },
  
  unmounted(el: HTMLElement) {
    if (el._contentObserver) {
      el._contentObserver.disconnect()
    }
  }
}

/**
 * 虚拟滚动组件
 */
export interface VirtualScrollItem {
  id: string | number
  height?: number
  [key: string]: any
}

export interface VirtualScrollOptions {
  itemHeight: number
  containerHeight: number
  buffer?: number
  threshold?: number
}

export class VirtualScroll {
  private container: HTMLElement
  private options: Required<VirtualScrollOptions>
  private items: VirtualScrollItem[] = []
  private visibleItems: VirtualScrollItem[] = []
  private startIndex = 0
  private endIndex = 0
  private scrollTop = 0

  constructor(container: HTMLElement, options: VirtualScrollOptions) {
    this.container = container
    this.options = {
      buffer: 5,
      threshold: 0,
      ...options
    }
    
    this.bindEvents()
  }

  private bindEvents() {
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
  }

  private handleScroll() {
    this.scrollTop = this.container.scrollTop
    this.updateVisibleItems()
  }

  private updateVisibleItems() {
    const { itemHeight, containerHeight, buffer } = this.options
    
    this.startIndex = Math.max(
      0,
      Math.floor(this.scrollTop / itemHeight) - buffer
    )
    
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    this.endIndex = Math.min(
      this.items.length,
      this.startIndex + visibleCount + buffer * 2
    )
    
    this.visibleItems = this.items.slice(this.startIndex, this.endIndex)
    
    // 触发更新事件
    this.container.dispatchEvent(new CustomEvent('virtual-scroll-update', {
      detail: {
        visibleItems: this.visibleItems,
        startIndex: this.startIndex,
        endIndex: this.endIndex,
        offsetY: this.startIndex * itemHeight
      }
    }))
  }

  setItems(items: VirtualScrollItem[]) {
    this.items = items
    this.updateVisibleItems()
  }

  scrollToIndex(index: number) {
    const { itemHeight } = this.options
    this.container.scrollTop = index * itemHeight
  }

  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll)
  }
}

/**
 * 分页加载管理器
 */
export class PaginationLoader {
  private loading = false
  private hasMore = true
  private page = 1
  private pageSize = 20

  constructor(
    private loadFn: (page: number, pageSize: number) => Promise<any[]>,
    options: { pageSize?: number } = {}
  ) {
    this.pageSize = options.pageSize || 20
  }

  async loadMore(): Promise<any[]> {
    if (this.loading || !this.hasMore) {
      return []
    }

    this.loading = true
    
    try {
      const data = await this.loadFn(this.page, this.pageSize)
      
      if (data.length < this.pageSize) {
        this.hasMore = false
      }
      
      this.page++
      return data
    } catch (error) {
      console.error('Load more failed:', error)
      throw error
    } finally {
      this.loading = false
    }
  }

  reset() {
    this.page = 1
    this.hasMore = true
    this.loading = false
  }

  get isLoading() {
    return this.loading
  }

  get canLoadMore() {
    return this.hasMore && !this.loading
  }
}

/**
 * 预加载管理器
 */
export class PreloadManager {
  private preloadedComponents = new Map<string, Promise<any>>()
  private preloadedImages = new Set<string>()

  preloadComponent(name: string, importFn: () => Promise<any>) {
    if (!this.preloadedComponents.has(name)) {
      this.preloadedComponents.set(name, importFn())
    }
    return this.preloadedComponents.get(name)!
  }

  preloadImage(src: string): Promise<void> {
    if (this.preloadedImages.has(src)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.preloadedImages.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  preloadImages(srcs: string[]): Promise<void[]> {
    return Promise.all(srcs.map(src => this.preloadImage(src)))
  }

  clear() {
    this.preloadedComponents.clear()
    this.preloadedImages.clear()
  }
}

// 导出实例
export const preloadManager = new PreloadManager()
