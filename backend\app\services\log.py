"""
日志管理服务
"""
import asyncio
import logging
import os
import re
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.app import App, AppLog
from app.models.log import LogAlert, LogAlertRecord
from app.schemas.log import Log, LogCreate, LogQuery, LogRotationConfig, LogStats


class LogCollectionService:
    """日志收集服务"""

    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        self.rotation_config = LogRotationConfig()
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        self.logger = logging.getLogger("log_collection")
        self.logger.setLevel(logging.INFO)

        # 创建轮转文件处理器
        handler = RotatingFileHandler(
            self.log_dir / "collection.log",
            maxBytes=self.rotation_config.max_size,
            backupCount=self.rotation_config.backup_count,
        )

        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def collect_log(self, db: Session, log_data: LogCreate) -> AppLog:
        """收集单条日志"""
        from app.core.exceptions import BusinessException, DatabaseException

        try:
            # 创建日志记录
            db_log = AppLog(
                app_id=log_data.app_id,
                service_type=log_data.service_type,
                level=log_data.level,
                message=log_data.message,
            )

            db.add(db_log)
            db.commit()
            db.refresh(db_log)

            # 记录到文件
            try:
                self._write_to_file(log_data)
            except Exception as file_error:
                self.logger.warning(f"Failed to write log to file: {file_error}")
                # 文件写入失败不应该影响数据库记录

            # 检查告警规则
            try:
                self._check_alert_rules(db, db_log)
            except Exception as alert_error:
                self.logger.warning(f"Failed to check alert rules: {alert_error}")
                # 告警检查失败不应该影响日志收集

            self.logger.info(
                f"Collected log for app {log_data.app_id}: {log_data.level}"
            )
            return db_log

        except Exception as e:
            self.logger.error(f"Failed to collect log: {str(e)}")
            db.rollback()
            if "database" in str(e).lower() or "connection" in str(e).lower():
                raise DatabaseException(f"日志收集失败: {str(e)}")
            else:
                raise BusinessException(f"日志收集失败: {str(e)}")

    def collect_logs_batch(
        self, db: Session, logs_data: List[LogCreate]
    ) -> List[AppLog]:
        """批量收集日志"""
        try:
            db_logs = []
            for log_data in logs_data:
                db_log = AppLog(
                    app_id=log_data.app_id,
                    service_type=log_data.service_type,
                    level=log_data.level,
                    message=log_data.message,
                )
                db_logs.append(db_log)

            db.add_all(db_logs)
            db.commit()

            # 批量写入文件
            for log_data in logs_data:
                self._write_to_file(log_data)

            # 批量检查告警规则
            for db_log in db_logs:
                self._check_alert_rules(db, db_log)

            self.logger.info(f"Collected {len(db_logs)} logs in batch")
            return db_logs

        except Exception as e:
            self.logger.error(f"Failed to collect logs batch: {str(e)}")
            db.rollback()
            raise

    def _write_to_file(self, log_data: LogCreate):
        """写入日志文件"""
        app_log_dir = self.log_dir / f"app_{log_data.app_id}"
        app_log_dir.mkdir(exist_ok=True)

        log_file = app_log_dir / f"{log_data.service_type}.log"

        # 检查文件大小并轮转
        if (
            log_file.exists()
            and log_file.stat().st_size > self.rotation_config.max_size
        ):
            self._rotate_log_file(log_file)

        # 写入日志
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_line = f"[{timestamp}] [{log_data.level}] {log_data.message}\n"

        with open(log_file, "a", encoding="utf-8") as f:
            f.write(log_line)

    def _rotate_log_file(self, log_file: Path):
        """轮转日志文件"""
        try:
            # 移动现有备份文件
            for i in range(self.rotation_config.backup_count - 1, 0, -1):
                old_backup = log_file.with_suffix(f".log.{i}")
                new_backup = log_file.with_suffix(f".log.{i + 1}")

                if old_backup.exists():
                    if new_backup.exists():
                        new_backup.unlink()
                    old_backup.rename(new_backup)

            # 移动当前文件为第一个备份
            backup_file = log_file.with_suffix(".log.1")
            if backup_file.exists():
                backup_file.unlink()
            log_file.rename(backup_file)

            # 压缩备份文件（如果启用）
            if self.rotation_config.compress:
                self._compress_backup(backup_file)

            self.logger.info(f"Rotated log file: {log_file}")

        except Exception as e:
            self.logger.error(f"Failed to rotate log file {log_file}: {str(e)}")

    def _compress_backup(self, backup_file: Path):
        """压缩备份文件"""
        try:
            import gzip
            import shutil

            compressed_file = backup_file.with_suffix(backup_file.suffix + ".gz")

            with open(backup_file, "rb") as f_in:
                with gzip.open(compressed_file, "wb") as f_out:
                    shutil.copyfileobj(f_in, f_out)

            backup_file.unlink()
            self.logger.info(f"Compressed backup file: {compressed_file}")

        except Exception as e:
            self.logger.error(f"Failed to compress backup file {backup_file}: {str(e)}")

    def _check_alert_rules(self, db: Session, log: AppLog):
        """检查告警规则"""
        try:
            # 获取该应用的活跃告警规则
            alert_rules = (
                db.query(LogAlert)
                .filter(
                    and_(
                        LogAlert.app_id == log.app_id,
                        LogAlert.is_active == True,
                        LogAlert.level == log.level,
                    )
                )
                .all()
            )

            for rule in alert_rules:
                # 检查模式匹配
                if self._match_pattern(log.message, rule.pattern):
                    # 检查时间窗口内的日志数量
                    time_threshold = datetime.now() - timedelta(
                        minutes=rule.time_window
                    )

                    log_count = (
                        db.query(func.count(AppLog.id))
                        .filter(
                            and_(
                                AppLog.app_id == log.app_id,
                                AppLog.level == rule.level,
                                AppLog.timestamp >= time_threshold,
                                AppLog.message.contains(rule.pattern),
                            )
                        )
                        .scalar()
                    )

                    if log_count >= rule.threshold:
                        self._trigger_alert(db, rule, log_count)

        except Exception as e:
            self.logger.error(f"Failed to check alert rules: {str(e)}")

    def _match_pattern(self, message: str, pattern: str) -> bool:
        """匹配日志模式"""
        try:
            # 支持正则表达式和简单字符串匹配
            if pattern.startswith("regex:"):
                regex_pattern = pattern[6:]
                return bool(re.search(regex_pattern, message, re.IGNORECASE))
            else:
                return pattern.lower() in message.lower()
        except Exception:
            return False

    def _trigger_alert(self, db: Session, rule: LogAlert, log_count: int):
        """触发告警"""
        try:
            # 检查是否已经有未解决的告警记录
            existing_alert = (
                db.query(LogAlertRecord)
                .filter(
                    and_(
                        LogAlertRecord.alert_rule_id == rule.id,
                        LogAlertRecord.is_resolved == False,
                    )
                )
                .first()
            )

            if not existing_alert:
                # 创建新的告警记录
                alert_record = LogAlertRecord(
                    alert_rule_id=rule.id,
                    app_id=rule.app_id,
                    log_count=log_count,
                    message=f"规则 '{rule.rule_name}' 触发: {log_count} 条 {rule.level} 级别日志在 {rule.time_window} 分钟内匹配模式 '{rule.pattern}'",
                )

                db.add(alert_record)
                db.commit()

                self.logger.warning(
                    f"Alert triggered for rule {rule.rule_name}: {alert_record.message}"
                )

                # 发送通知
                self._send_alert_notification(db, alert_record)

        except Exception as e:
            self.logger.error(f"Failed to trigger alert: {str(e)}")

    def _send_alert_notification(self, db: Session, alert_record: LogAlertRecord):
        """发送告警通知"""
        try:
            from app.services.notification import notification_service

            # 这里可以从配置或用户设置中获取通知接收者
            # 暂时使用硬编码的邮箱地址作为示例
            recipients = ["<EMAIL>"]  # 实际应用中应该从配置获取

            notification_service.send_alert_notification(alert_record, recipients)

        except Exception as e:
            self.logger.error(f"Failed to send alert notification: {str(e)}")

    def get_log_stats(self, db: Session, app_id: int, hours: int = 24) -> LogStats:
        """获取日志统计信息"""
        time_threshold = datetime.now() - timedelta(hours=hours)

        # 总日志数
        total_logs = (
            db.query(func.count(AppLog.id))
            .filter(and_(AppLog.app_id == app_id, AppLog.timestamp >= time_threshold))
            .scalar()
        )

        # 各级别日志数
        level_counts = (
            db.query(AppLog.level, func.count(AppLog.id))
            .filter(and_(AppLog.app_id == app_id, AppLog.timestamp >= time_threshold))
            .group_by(AppLog.level)
            .all()
        )

        level_dict = {level: count for level, count in level_counts}

        return LogStats(
            total_logs=total_logs,
            error_logs=level_dict.get("ERROR", 0),
            warning_logs=level_dict.get("WARNING", 0),
            info_logs=level_dict.get("INFO", 0),
            debug_logs=level_dict.get("DEBUG", 0),
        )

    def cleanup_old_logs(self, db: Session, days: int = 30):
        """清理旧日志"""
        try:
            time_threshold = datetime.now() - timedelta(days=days)

            # 删除数据库中的旧日志
            deleted_count = (
                db.query(AppLog).filter(AppLog.timestamp < time_threshold).delete()
            )

            db.commit()

            # 清理文件系统中的旧日志文件
            self._cleanup_old_log_files(days)

            self.logger.info(f"Cleaned up {deleted_count} old log records")

        except Exception as e:
            self.logger.error(f"Failed to cleanup old logs: {str(e)}")
            db.rollback()

    def _cleanup_old_log_files(self, days: int):
        """清理旧的日志文件"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)

            for app_dir in self.log_dir.iterdir():
                if app_dir.is_dir() and app_dir.name.startswith("app_"):
                    for log_file in app_dir.iterdir():
                        if log_file.is_file():
                            file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                            if file_time < cutoff_time:
                                log_file.unlink()
                                self.logger.info(f"Deleted old log file: {log_file}")

        except Exception as e:
            self.logger.error(f"Failed to cleanup old log files: {str(e)}")


class LogQueryService:
    """日志查询服务"""

    def __init__(self):
        self.log_dir = Path("logs")

    def query_logs(self, db: Session, query: LogQuery) -> List[AppLog]:
        """查询日志"""
        try:
            # 构建查询条件
            conditions = []

            if query.app_id:
                conditions.append(AppLog.app_id == query.app_id)

            if query.service_type:
                conditions.append(AppLog.service_type == query.service_type)

            if query.level:
                conditions.append(AppLog.level == query.level)

            if query.keyword:
                conditions.append(AppLog.message.contains(query.keyword))

            if query.start_time:
                conditions.append(AppLog.timestamp >= query.start_time)

            if query.end_time:
                conditions.append(AppLog.timestamp <= query.end_time)

            # 执行查询
            query_obj = db.query(AppLog)

            if conditions:
                query_obj = query_obj.filter(and_(*conditions))

            # 排序和分页
            logs = (
                query_obj.order_by(desc(AppLog.timestamp))
                .offset(query.skip)
                .limit(query.limit)
                .all()
            )

            return logs

        except Exception as e:
            logging.error(f"Failed to query logs: {str(e)}")
            raise

    def count_logs(self, db: Session, query: LogQuery) -> int:
        """统计日志数量"""
        try:
            # 构建查询条件
            conditions = []

            if query.app_id:
                conditions.append(AppLog.app_id == query.app_id)

            if query.service_type:
                conditions.append(AppLog.service_type == query.service_type)

            if query.level:
                conditions.append(AppLog.level == query.level)

            if query.keyword:
                conditions.append(AppLog.message.contains(query.keyword))

            if query.start_time:
                conditions.append(AppLog.timestamp >= query.start_time)

            if query.end_time:
                conditions.append(AppLog.timestamp <= query.end_time)

            # 执行统计查询
            query_obj = db.query(func.count(AppLog.id))

            if conditions:
                query_obj = query_obj.filter(and_(*conditions))

            return query_obj.scalar()

        except Exception as e:
            logging.error(f"Failed to count logs: {str(e)}")
            raise

    def get_log_levels(self, db: Session, app_id: Optional[int] = None) -> List[str]:
        """获取日志级别列表"""
        try:
            query_obj = db.query(AppLog.level).distinct()

            if app_id:
                query_obj = query_obj.filter(AppLog.app_id == app_id)

            levels = [level[0] for level in query_obj.all()]
            return levels

        except Exception as e:
            logging.error(f"Failed to get log levels: {str(e)}")
            raise

    def export_logs(self, db: Session, query: LogQuery, format: str = "csv") -> str:
        """导出日志"""
        try:
            # 获取所有匹配的日志（不分页）
            export_query = LogQuery(
                app_id=query.app_id,
                service_type=query.service_type,
                level=query.level,
                keyword=query.keyword,
                start_time=query.start_time,
                end_time=query.end_time,
                skip=0,
                limit=10000,  # 设置一个合理的导出上限
            )

            logs = self.query_logs(db, export_query)

            if format.lower() == "csv":
                return self._export_to_csv(logs)
            elif format.lower() == "json":
                return self._export_to_json(logs)
            elif format.lower() == "txt":
                return self._export_to_txt(logs)
            else:
                raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            logging.error(f"Failed to export logs: {str(e)}")
            raise

    def _export_to_csv(self, logs: List[AppLog]) -> str:
        """导出为CSV格式"""
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # 写入标题行
        writer.writerow(
            ["ID", "App ID", "Service Type", "Level", "Message", "Timestamp"]
        )

        # 写入数据行
        for log in logs:
            writer.writerow(
                [
                    log.id,
                    log.app_id,
                    log.service_type,
                    log.level,
                    log.message,
                    log.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                ]
            )

        return output.getvalue()

    def _export_to_json(self, logs: List[AppLog]) -> str:
        """导出为JSON格式"""
        import json

        data = []
        for log in logs:
            data.append(
                {
                    "id": log.id,
                    "app_id": log.app_id,
                    "service_type": log.service_type,
                    "level": log.level,
                    "message": log.message,
                    "timestamp": log.timestamp.isoformat(),
                }
            )

        return json.dumps(data, ensure_ascii=False, indent=2)

    def _export_to_txt(self, logs: List[AppLog]) -> str:
        """导出为TXT格式"""
        lines = []
        for log in logs:
            timestamp = log.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            line = f"[{timestamp}] [App:{log.app_id}] [{log.service_type}] [{log.level}] {log.message}"
            lines.append(line)

        return "\n".join(lines)

    def get_log_by_id(self, db: Session, log_id: int) -> Optional[AppLog]:
        """根据ID获取日志"""
        try:
            return db.query(AppLog).filter(AppLog.id == log_id).first()
        except Exception as e:
            logging.error(f"Failed to get log by id {log_id}: {str(e)}")
            raise

    def delete_logs(self, db: Session, query: LogQuery) -> int:
        """删除日志"""
        try:
            # 构建查询条件
            conditions = []

            if query.app_id:
                conditions.append(AppLog.app_id == query.app_id)

            if query.service_type:
                conditions.append(AppLog.service_type == query.service_type)

            if query.level:
                conditions.append(AppLog.level == query.level)

            if query.keyword:
                conditions.append(AppLog.message.contains(query.keyword))

            if query.start_time:
                conditions.append(AppLog.timestamp >= query.start_time)

            if query.end_time:
                conditions.append(AppLog.timestamp <= query.end_time)

            # 执行删除
            query_obj = db.query(AppLog)

            if conditions:
                query_obj = query_obj.filter(and_(*conditions))

            deleted_count = query_obj.delete()
            db.commit()

            return deleted_count

        except Exception as e:
            logging.error(f"Failed to delete logs: {str(e)}")
            db.rollback()
            raise


# 全局服务实例
log_collection_service = LogCollectionService()
log_query_service = LogQueryService()
