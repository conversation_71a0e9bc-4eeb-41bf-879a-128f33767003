"""
任务相关模型
"""
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Task(Base):
    """任务模型"""

    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True, comment="任务ID")
    name = Column(String(100), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=True, comment="关联应用ID")

    # 任务配置
    command = Column(Text, nullable=True, comment="执行命令")
    parameters = Column(JSON, nullable=True, comment="任务参数")
    working_directory = Column(String(500), nullable=True, comment="工作目录")
    environment_vars = Column(JSON, nullable=True, comment="环境变量")

    # 调度配置
    schedule_type = Column(
        String(20), default="manual", comment="调度类型(manual/cron/interval)"
    )
    cron_expression = Column(String(100), nullable=True, comment="Cron表达式")
    interval_seconds = Column(Integer, nullable=True, comment="间隔秒数")

    # 状态和控制
    is_active = Column(Boolean, default=True, comment="是否激活")
    max_retries = Column(Integer, default=0, comment="最大重试次数")
    timeout_seconds = Column(Integer, default=3600, comment="超时时间(秒)")

    # 时间戳
    created_by = Column(
        Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID"
    )
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )
    last_run_at = Column(DateTime(timezone=True), nullable=True, comment="最后执行时间")
    next_run_at = Column(DateTime(timezone=True), nullable=True, comment="下次执行时间")

    # 关系
    creator = relationship("User", back_populates="created_tasks")
    app = relationship("App", back_populates="tasks")
    executions = relationship(
        "TaskExecution", back_populates="task", cascade="all, delete-orphan"
    )


class TaskExecution(Base):
    """任务执行记录模型"""

    __tablename__ = "task_executions"

    id = Column(Integer, primary_key=True, index=True, comment="执行记录ID")
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False, comment="任务ID")

    # 执行信息
    status = Column(
        String(20),
        default="pending",
        comment="执行状态(pending/running/success/failed/timeout)",
    )
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    finished_at = Column(DateTime(timezone=True), nullable=True, comment="结束时间")
    duration_seconds = Column(Integer, nullable=True, comment="执行时长(秒)")

    # 执行结果
    exit_code = Column(Integer, nullable=True, comment="退出码")
    stdout = Column(Text, nullable=True, comment="标准输出")
    stderr = Column(Text, nullable=True, comment="错误输出")
    error_message = Column(Text, nullable=True, comment="错误消息")

    # 执行环境
    executor_host = Column(String(100), nullable=True, comment="执行主机")
    process_id = Column(Integer, nullable=True, comment="进程ID")

    # 重试信息
    retry_count = Column(Integer, default=0, comment="重试次数")
    is_retry = Column(Boolean, default=False, comment="是否为重试执行")
    parent_execution_id = Column(
        Integer, ForeignKey("task_executions.id"), nullable=True, comment="父执行记录ID"
    )

    # 时间戳
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    task = relationship("Task", back_populates="executions")
    parent_execution = relationship(
        "TaskExecution", remote_side=[id], back_populates="retry_executions"
    )
    retry_executions = relationship("TaskExecution", back_populates="parent_execution")


class TaskTemplate(Base):
    """任务模板模型"""

    __tablename__ = "task_templates"

    id = Column(Integer, primary_key=True, index=True, comment="模板ID")
    name = Column(String(100), nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    category = Column(String(50), nullable=True, comment="模板分类")

    # 模板配置
    task_type = Column(String(50), nullable=False, comment="任务类型")
    command_template = Column(Text, nullable=True, comment="命令模板")
    parameters_schema = Column(JSON, nullable=True, comment="参数模式")
    default_parameters = Column(JSON, nullable=True, comment="默认参数")

    # 模板元数据
    is_system = Column(Boolean, default=False, comment="是否为系统模板")
    is_public = Column(Boolean, default=True, comment="是否为公共模板")
    usage_count = Column(Integer, default=0, comment="使用次数")

    # 时间戳
    created_by = Column(
        Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID"
    )
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    creator = relationship("User", back_populates="created_task_templates")


class TaskDependency(Base):
    """任务依赖关系模型"""

    __tablename__ = "task_dependencies"

    id = Column(Integer, primary_key=True, index=True, comment="依赖关系ID")
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False, comment="任务ID")
    depends_on_task_id = Column(
        Integer, ForeignKey("tasks.id"), nullable=False, comment="依赖的任务ID"
    )

    # 依赖配置
    dependency_type = Column(
        String(20), default="success", comment="依赖类型(success/failure/always)"
    )
    is_active = Column(Boolean, default=True, comment="是否激活")

    # 时间戳
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )

    # 关系
    task = relationship("Task", foreign_keys=[task_id])
    depends_on_task = relationship("Task", foreign_keys=[depends_on_task_id])
