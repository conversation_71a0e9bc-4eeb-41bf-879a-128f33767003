"""
用户服务单元测试
"""
from unittest.mock import patch

import pytest
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.user import UserService


class TestUserService:
    """用户服务测试类"""

    def test_get_users(self, db_session: Session):
        """测试获取用户列表"""
        service = UserService(db_session)

        # 创建测试用户
        user1 = User(username="user1", email="<EMAIL>", hashed_password="hash1")
        user2 = User(username="user2", email="<EMAIL>", hashed_password="hash2")
        db_session.add_all([user1, user2])
        db_session.commit()

        # 测试获取所有用户
        users = service.get_users()
        assert len(users) == 2

        # 测试分页
        users = service.get_users(skip=1, limit=1)
        assert len(users) == 1

    def test_get_user_by_id(self, db_session: Session):
        """测试根据ID获取用户"""
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        # 测试获取存在的用户
        result = service.get_user_by_id(user.id)
        assert result is not None
        assert result.username == "testuser"

        # 测试获取不存在的用户
        result = service.get_user_by_id(999)
        assert result is None

    def test_get_user_by_username(self, db_session: Session):
        """测试根据用户名获取用户"""
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        # 测试获取存在的用户
        result = service.get_user_by_username("testuser")
        assert result is not None
        assert result.username == "testuser"

        # 测试获取不存在的用户
        result = service.get_user_by_username("nonexistent")
        assert result is None

    def test_get_user_by_email(self, db_session: Session):
        """测试根据邮箱获取用户"""
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        # 测试获取存在的用户
        result = service.get_user_by_email("<EMAIL>")
        assert result is not None
        assert result.email == "<EMAIL>"

        # 测试获取不存在的用户
        result = service.get_user_by_email("<EMAIL>")
        assert result is None

    @patch("app.core.security.get_password_hash")
    def test_create_user_success(self, mock_hash, db_session: Session):
        """测试成功创建用户"""
        mock_hash.return_value = "hashed_password"
        service = UserService(db_session)

        user_data = UserCreate(
            username="newuser", email="<EMAIL>", password="password123"
        )

        result = service.create_user(user_data)

        assert result.username == "newuser"
        assert result.email == "<EMAIL>"
        assert result.hashed_password == "hashed_password"
        assert result.is_active is True

        mock_hash.assert_called_once_with("password123")

    def test_create_user_duplicate_username(self, db_session: Session):
        """测试创建重复用户名的用户"""
        service = UserService(db_session)

        # 创建已存在的用户
        existing_user = User(
            username="existing", email="<EMAIL>", hashed_password="hash"
        )
        db_session.add(existing_user)
        db_session.commit()

        user_data = UserCreate(
            username="existing", email="<EMAIL>", password="password123"
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_user(user_data)

        assert exc_info.value.status_code == 400
        assert "用户名已存在" in str(exc_info.value.detail)

    def test_create_user_duplicate_email(self, db_session: Session):
        """测试创建重复邮箱的用户"""
        service = UserService(db_session)

        # 创建已存在的用户
        existing_user = User(
            username="existing", email="<EMAIL>", hashed_password="hash"
        )
        db_session.add(existing_user)
        db_session.commit()

        user_data = UserCreate(
            username="newuser", email="<EMAIL>", password="password123"
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_user(user_data)

        assert exc_info.value.status_code == 400
        assert "邮箱已存在" in str(exc_info.value.detail)

    def test_update_user_success(self, db_session: Session):
        """测试成功更新用户"""
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        update_data = UserUpdate(email="<EMAIL>", is_active=False)

        result = service.update_user(user.id, update_data)

        assert result.email == "<EMAIL>"
        assert result.is_active is False
        assert result.username == "testuser"  # 未更新的字段保持不变

    def test_update_user_not_found(self, db_session: Session):
        """测试更新不存在的用户"""
        service = UserService(db_session)

        update_data = UserUpdate(email="<EMAIL>")

        with pytest.raises(HTTPException) as exc_info:
            service.update_user(999, update_data)

        assert exc_info.value.status_code == 404
        assert "用户不存在" in str(exc_info.value.detail)

    def test_delete_user_success(self, db_session: Session):
        """测试成功删除用户"""
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        result = service.delete_user(user.id)
        assert result is True

        # 验证用户已删除
        deleted_user = db_session.query(User).filter(User.id == user.id).first()
        assert deleted_user is None

    def test_delete_user_not_found(self, db_session: Session):
        """测试删除不存在的用户"""
        service = UserService(db_session)

        with pytest.raises(HTTPException) as exc_info:
            service.delete_user(999)

        assert exc_info.value.status_code == 404
        assert "用户不存在" in str(exc_info.value.detail)

    @patch("app.core.security.verify_password")
    def test_authenticate_user_success(self, mock_verify, db_session: Session):
        """测试成功认证用户"""
        mock_verify.return_value = True
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        result = service.authenticate_user("testuser", "password")

        assert result is not None
        assert result.username == "testuser"
        mock_verify.assert_called_once_with("password", "hash")

    @patch("app.core.security.verify_password")
    def test_authenticate_user_wrong_password(self, mock_verify, db_session: Session):
        """测试错误密码认证"""
        mock_verify.return_value = False
        service = UserService(db_session)

        user = User(username="testuser", email="<EMAIL>", hashed_password="hash")
        db_session.add(user)
        db_session.commit()

        result = service.authenticate_user("testuser", "wrongpassword")

        assert result is None
        mock_verify.assert_called_once_with("wrongpassword", "hash")

    def test_authenticate_user_not_found(self, db_session: Session):
        """测试认证不存在的用户"""
        service = UserService(db_session)

        result = service.authenticate_user("nonexistent", "password")
        assert result is None

    def test_authenticate_user_inactive(self, db_session: Session):
        """测试认证未激活的用户"""
        service = UserService(db_session)

        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=False,
        )
        db_session.add(user)
        db_session.commit()

        result = service.authenticate_user("testuser", "password")
        assert result is None

    @patch("app.core.security.get_password_hash")
    def test_change_password_success(self, mock_hash, db_session: Session):
        """测试成功修改密码"""
        mock_hash.return_value = "new_hashed_password"
        service = UserService(db_session)

        user = User(
            username="testuser", email="<EMAIL>", hashed_password="old_hash"
        )
        db_session.add(user)
        db_session.commit()

        result = service.change_password(user.id, "newpassword")

        assert result is True
        assert user.hashed_password == "new_hashed_password"
        mock_hash.assert_called_once_with("newpassword")

    def test_change_password_user_not_found(self, db_session: Session):
        """测试修改不存在用户的密码"""
        service = UserService(db_session)

        with pytest.raises(HTTPException) as exc_info:
            service.change_password(999, "newpassword")

        assert exc_info.value.status_code == 404
        assert "用户不存在" in str(exc_info.value.detail)

    def test_get_user_stats(self, db_session: Session):
        """测试获取用户统计信息"""
        service = UserService(db_session)

        # 创建测试用户
        users = [
            User(
                username="user1",
                email="<EMAIL>",
                hashed_password="hash",
                is_active=True,
            ),
            User(
                username="user2",
                email="<EMAIL>",
                hashed_password="hash",
                is_active=True,
            ),
            User(
                username="user3",
                email="<EMAIL>",
                hashed_password="hash",
                is_active=False,
            ),
        ]
        db_session.add_all(users)
        db_session.commit()

        stats = service.get_user_stats()

        assert stats["total_users"] == 3
        assert stats["active_users"] == 2
        assert stats["inactive_users"] == 1
