"""
配置历史记录模型
"""
from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class AppConfigHistory(Base):
    """应用配置历史记录模型"""

    __tablename__ = "app_config_history"

    id = Column(Integer, primary_key=True, index=True, comment="历史记录ID")
    config_id = Column(
        Integer, ForeignKey("app_configs.id"), nullable=False, comment="配置ID"
    )
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    key = Column(String(100), nullable=False, comment="配置键")
    old_value = Column(Text, nullable=True, comment="旧值")
    new_value = Column(Text, nullable=True, comment="新值")
    operation = Column(String(20), nullable=False, comment="操作类型(create/update/delete)")
    changed_by = Column(
        Integer, ForeignKey("users.id"), nullable=False, comment="修改者ID"
    )
    changed_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="修改时间"
    )

    # 关系
    config = relationship("AppConfig")
    app = relationship("App")
    user = relationship("User")
