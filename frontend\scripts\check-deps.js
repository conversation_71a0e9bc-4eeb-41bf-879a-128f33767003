#!/usr/bin/env node
/**
 * 前端依赖检查脚本
 */
const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 检查package.json
function checkPackageJson() {
  console.log('🔍 检查 package.json...')
  
  const packagePath = path.join(__dirname, '..', 'package.json')
  if (!fs.existsSync(packagePath)) {
    console.error('❌ package.json 不存在')
    return false
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  // 检查必要字段
  const requiredFields = ['name', 'version', 'description', 'scripts']
  const missingFields = requiredFields.filter(field => !packageJson[field])
  
  if (missingFields.length > 0) {
    console.warn(`⚠️  缺少字段: ${missingFields.join(', ')}`)
  }
  
  console.log('✅ package.json 检查完成')
  return true
}

// 检查依赖版本
function checkDependencyVersions() {
  console.log('\n🔍 检查依赖版本...')
  
  try {
    // 检查过时的依赖
    const outdated = execSync('npm outdated --json', { 
      encoding: 'utf8',
      stdio: 'pipe'
    })
    
    if (outdated.trim()) {
      const outdatedDeps = JSON.parse(outdated)
      console.log('📦 发现过时的依赖:')
      Object.entries(outdatedDeps).forEach(([name, info]) => {
        console.log(`  - ${name}: ${info.current} → ${info.latest}`)
      })
    } else {
      console.log('✅ 所有依赖都是最新的')
    }
  } catch (error) {
    if (error.status === 1) {
      // npm outdated 返回状态码1表示有过时依赖，这是正常的
      console.log('✅ 依赖版本检查完成')
    } else {
      console.error('❌ 检查依赖版本时出错:', error.message)
      return false
    }
  }
  
  return true
}

// 检查未使用的依赖
function checkUnusedDependencies() {
  console.log('\n🔍 检查未使用的依赖...')
  
  const packagePath = path.join(__dirname, '..', 'package.json')
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  const allDeps = {
    ...packageJson.dependencies || {},
    ...packageJson.devDependencies || {}
  }
  
  const srcDir = path.join(__dirname, '..', 'src')
  const unusedDeps = []
  
  // 简单检查：在src目录中搜索import语句
  Object.keys(allDeps).forEach(dep => {
    try {
      const result = execSync(`grep -r "from '${dep}'" "${srcDir}" || grep -r "import.*'${dep}'" "${srcDir}"`, {
        encoding: 'utf8',
        stdio: 'pipe'
      })
      
      if (!result.trim()) {
        // 检查一些特殊情况
        const specialCases = [
          'typescript', 'vite', '@vitejs/plugin-vue',
          'eslint', 'prettier', '@types/',
          'unplugin-auto-import', 'unplugin-vue-components'
        ]
        
        const isSpecialCase = specialCases.some(special => dep.includes(special))
        if (!isSpecialCase) {
          unusedDeps.push(dep)
        }
      }
    } catch (error) {
      // grep 没找到匹配项会返回非0状态码，这里忽略
    }
  })
  
  if (unusedDeps.length > 0) {
    console.log('📦 可能未使用的依赖:')
    unusedDeps.forEach(dep => {
      console.log(`  - ${dep}`)
    })
    console.log('💡 请手动确认这些依赖是否真的未使用')
  } else {
    console.log('✅ 未发现明显未使用的依赖')
  }
  
  return true
}

// 检查依赖冲突
function checkDependencyConflicts() {
  console.log('\n🔍 检查依赖冲突...')
  
  try {
    execSync('npm ls', { 
      encoding: 'utf8',
      stdio: 'pipe'
    })
    console.log('✅ 未发现依赖冲突')
    return true
  } catch (error) {
    console.log('❌ 发现依赖冲突:')
    console.log(error.stdout)
    return false
  }
}

// 检查安全漏洞
function checkSecurityVulnerabilities() {
  console.log('\n🔍 检查安全漏洞...')
  
  try {
    const auditResult = execSync('npm audit --audit-level=moderate --json', {
      encoding: 'utf8',
      stdio: 'pipe'
    })
    
    const audit = JSON.parse(auditResult)
    
    if (audit.metadata.vulnerabilities.total === 0) {
      console.log('✅ 未发现安全漏洞')
      return true
    } else {
      console.log('❌ 发现安全漏洞:')
      console.log(`  - 高危: ${audit.metadata.vulnerabilities.high}`)
      console.log(`  - 中危: ${audit.metadata.vulnerabilities.moderate}`)
      console.log(`  - 低危: ${audit.metadata.vulnerabilities.low}`)
      console.log('💡 运行 npm audit fix 尝试自动修复')
      return false
    }
  } catch (error) {
    if (error.message.includes('not implemented')) {
      console.log('⚠️  当前npm镜像不支持安全审计')
      return true
    } else {
      console.error('❌ 安全检查失败:', error.message)
      return false
    }
  }
}

// 生成依赖报告
function generateDependencyReport() {
  console.log('\n📊 生成依赖报告...')
  
  const packagePath = path.join(__dirname, '..', 'package.json')
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  const report = {
    timestamp: new Date().toISOString(),
    project: {
      name: packageJson.name,
      version: packageJson.version
    },
    dependencies: {
      production: Object.keys(packageJson.dependencies || {}),
      development: Object.keys(packageJson.devDependencies || {})
    },
    stats: {
      totalDependencies: Object.keys({
        ...packageJson.dependencies || {},
        ...packageJson.devDependencies || {}
      }).length,
      productionDependencies: Object.keys(packageJson.dependencies || {}).length,
      developmentDependencies: Object.keys(packageJson.devDependencies || {}).length
    }
  }
  
  const reportPath = path.join(__dirname, '..', 'dependency-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  
  console.log(`📄 依赖报告已保存到: ${reportPath}`)
  console.log(`📈 统计信息:`)
  console.log(`  - 总依赖数: ${report.stats.totalDependencies}`)
  console.log(`  - 生产依赖: ${report.stats.productionDependencies}`)
  console.log(`  - 开发依赖: ${report.stats.developmentDependencies}`)
  
  return true
}

// 主函数
function main() {
  console.log('📦 开始前端依赖检查...')
  console.log('=' * 50)
  
  const checks = [
    checkPackageJson,
    checkDependencyVersions,
    checkUnusedDependencies,
    checkDependencyConflicts,
    checkSecurityVulnerabilities,
    generateDependencyReport
  ]
  
  let allPassed = true
  
  for (const check of checks) {
    try {
      const result = check()
      if (!result) {
        allPassed = false
      }
    } catch (error) {
      console.error(`❌ 检查失败: ${error.message}`)
      allPassed = false
    }
  }
  
  console.log('=' * 50)
  if (allPassed) {
    console.log('✅ 所有依赖检查都通过了！')
    process.exit(0)
  } else {
    console.log('⚠️  存在需要关注的问题')
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  checkPackageJson,
  checkDependencyVersions,
  checkUnusedDependencies,
  checkDependencyConflicts,
  checkSecurityVulnerabilities,
  generateDependencyReport
}
