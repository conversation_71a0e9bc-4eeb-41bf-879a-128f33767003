"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    apps,
    auth,
    departments,
    log_alerts,
    logs,
    menus,
    roles,
    tasks,
    users,
)

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
api_router.include_router(departments.router, prefix="/departments", tags=["部门管理"])
api_router.include_router(menus.router, prefix="/menus", tags=["菜单管理"])
api_router.include_router(apps.router, prefix="/apps", tags=["应用管理"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(logs.router, prefix="/logs", tags=["日志管理"])
api_router.include_router(log_alerts.router, prefix="/log-alerts", tags=["日志告警"])
