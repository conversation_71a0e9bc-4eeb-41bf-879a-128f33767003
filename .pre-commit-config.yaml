# Pre-commit hooks configuration
# See https://pre-commit.com for more information

repos:
  # 通用检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        exclude: '\.md$'
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: debug-statements
      - id: detect-private-key
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Python 代码格式化和检查
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3
        files: ^backend/

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black"]
        files: ^backend/

  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        files: ^backend/
        additional_dependencies: [
          flake8-docstrings,
          flake8-bugbear,
          flake8-comprehensions,
          flake8-simplify
        ]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.13.0
    hooks:
      - id: mypy
        files: ^backend/
        additional_dependencies: [
          types-redis,
          types-requests,
          pydantic,
          sqlalchemy
        ]

  # Python 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.0
    hooks:
      - id: bandit
        files: ^backend/
        args: ['-r', '-ll']
        exclude: ^backend/tests/

  # 前端代码格式化和检查
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.15.0
    hooks:
      - id: eslint
        files: ^frontend/.*\.(js|ts|vue)$
        additional_dependencies:
          - eslint@^8.54.0
          - '@vue/eslint-config-typescript@^12.0.0'
          - '@typescript-eslint/eslint-plugin@^6.12.0'
          - '@typescript-eslint/parser@^6.12.0'
          - 'eslint-plugin-vue@^9.18.1'

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        files: ^frontend/.*\.(js|ts|vue|json|css|scss|md)$
        additional_dependencies:
          - prettier@^3.0.0

  # TypeScript 类型检查
  - repo: local
    hooks:
      - id: vue-tsc
        name: Vue TypeScript Check
        entry: bash -c 'cd frontend && npm run type-check'
        language: system
        files: ^frontend/.*\.(vue|ts)$
        pass_filenames: false

  # 文档检查
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.42.0
    hooks:
      - id: markdownlint
        args: ['--fix']

  # 提交信息检查
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.29.1
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # 密钥检查
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json

# 配置选项
default_language_version:
  python: python3.10
  node: '18.17.0'

# CI 配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
