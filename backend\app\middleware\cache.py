"""
API响应缓存中间件
"""
import json
import hashlib
from typing import Any, Dict, Optional, Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import redis
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("cache")


class CacheMiddleware:
    """API响应缓存中间件"""
    
    def __init__(self, redis_url: str = None, default_ttl: int = 300):
        self.redis_url = redis_url or settings.REDIS_URL
        self.default_ttl = default_ttl
        self.redis_client = None
        self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Redis cache: {e}")
            self.redis_client = None
    
    def _generate_cache_key(self, request: Request) -> str:
        """生成缓存键"""
        # 包含路径、查询参数和用户信息
        key_data = {
            "path": request.url.path,
            "query": str(request.url.query),
            "method": request.method,
        }
        
        # 如果有用户认证信息，包含用户ID
        if hasattr(request.state, "user") and request.state.user:
            key_data["user_id"] = getattr(request.state.user, "id", None)
        
        key_string = json.dumps(key_data, sort_keys=True)
        return f"api_cache:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _should_cache(self, request: Request, response: Response) -> bool:
        """判断是否应该缓存"""
        # 只缓存GET请求
        if request.method != "GET":
            return False
        
        # 只缓存成功响应
        if response.status_code != 200:
            return False
        
        # 检查是否有no-cache头
        cache_control = request.headers.get("cache-control", "")
        if "no-cache" in cache_control.lower():
            return False
        
        # 检查路径是否在缓存白名单中
        cacheable_paths = [
            "/api/v1/users",
            "/api/v1/roles",
            "/api/v1/departments",
            "/api/v1/menus",
            "/api/v1/apps",
            "/api/v1/system/metrics"
        ]
        
        return any(request.url.path.startswith(path) for path in cacheable_paths)
    
    def get_cached_response(self, request: Request) -> Optional[Response]:
        """获取缓存的响应"""
        if not self.redis_client:
            return None
        
        try:
            cache_key = self._generate_cache_key(request)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                logger.debug(f"Cache hit for key: {cache_key}")
                data = json.loads(cached_data)
                
                response = JSONResponse(
                    content=data["content"],
                    status_code=data["status_code"],
                    headers=data.get("headers", {})
                )
                response.headers["X-Cache"] = "HIT"
                return response
            
            logger.debug(f"Cache miss for key: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached response: {e}")
            return None
    
    def cache_response(
        self, 
        request: Request, 
        response: Response, 
        ttl: Optional[int] = None
    ):
        """缓存响应"""
        if not self.redis_client or not self._should_cache(request, response):
            return
        
        try:
            cache_key = self._generate_cache_key(request)
            ttl = ttl or self.default_ttl
            
            # 获取响应内容
            if hasattr(response, "body"):
                content = json.loads(response.body.decode())
            else:
                # 对于JSONResponse
                content = response.__dict__.get("content", {})
            
            cache_data = {
                "content": content,
                "status_code": response.status_code,
                "headers": dict(response.headers)
            }
            
            self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, ensure_ascii=False)
            )
            
            logger.debug(f"Response cached for key: {cache_key}, TTL: {ttl}s")
            
        except Exception as e:
            logger.error(f"Error caching response: {e}")
    
    def invalidate_cache(self, pattern: str = None):
        """清除缓存"""
        if not self.redis_client:
            return
        
        try:
            if pattern:
                keys = self.redis_client.keys(f"api_cache:*{pattern}*")
            else:
                keys = self.redis_client.keys("api_cache:*")
            
            if keys:
                self.redis_client.delete(*keys)
                logger.info(f"Invalidated {len(keys)} cache entries")
            
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")


# 全局缓存中间件实例
cache_middleware = CacheMiddleware()


def cache_response(ttl: int = 300):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            # 查找Request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # 如果没有Request对象，直接执行函数
                return await func(*args, **kwargs)
            
            # 尝试从缓存获取
            cached_response = cache_middleware.get_cached_response(request)
            if cached_response:
                return cached_response
            
            # 执行函数
            response = await func(*args, **kwargs)
            
            # 缓存响应
            if isinstance(response, (Response, JSONResponse)):
                cache_middleware.cache_response(request, response, ttl)
            
            return response
        
        return wrapper
    return decorator


def invalidate_cache_on_change(cache_pattern: str):
    """数据变更时清除缓存的装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            # 清除相关缓存
            cache_middleware.invalidate_cache(cache_pattern)
            
            return result
        
        return wrapper
    return decorator


class CacheStats:
    """缓存统计"""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if not self.redis_client:
            return {"error": "Redis not available"}
        
        try:
            info = self.redis_client.info()
            cache_keys = self.redis_client.keys("api_cache:*")
            
            return {
                "redis_info": {
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "total_commands_processed": info.get("total_commands_processed"),
                    "keyspace_hits": info.get("keyspace_hits"),
                    "keyspace_misses": info.get("keyspace_misses")
                },
                "cache_info": {
                    "total_cache_keys": len(cache_keys),
                    "hit_rate": self._calculate_hit_rate(info)
                }
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """计算缓存命中率"""
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return round((hits / total) * 100, 2)


# 缓存统计实例
cache_stats = CacheStats(cache_middleware.redis_client)
