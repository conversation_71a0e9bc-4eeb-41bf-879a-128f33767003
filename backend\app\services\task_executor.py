"""
任务执行器服务
"""
import logging
import os
import signal
import subprocess
import threading
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy.orm import Session

from app.models.task import Task, TaskExecution
from app.schemas.task import TaskExecutionCreate, TaskExecutionUpdate


class TaskExecutor:
    """任务执行器"""

    def __init__(self):
        self.logger = logging.getLogger("task_executor")
        self.running_processes: Dict[int, subprocess.Popen] = {}
        self.execution_threads: Dict[int, threading.Thread] = {}

    def execute_task(
        self, db: Session, task_id: int, parameters: Optional[Dict[str, Any]] = None
    ) -> TaskExecution:
        """执行任务"""
        try:
            # 获取任务信息
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")

            if not task.is_active:
                raise ValueError(f"任务 {task.name} 未激活")

            # 创建执行记录
            execution_data = TaskExecutionCreate(task_id=task_id, status="pending")

            execution = TaskExecution(**execution_data.dict())
            db.add(execution)
            db.commit()
            db.refresh(execution)

            # 在后台线程中执行任务
            thread = threading.Thread(
                target=self._execute_task_async, args=(execution.id, task, parameters)
            )
            thread.daemon = True
            thread.start()

            self.execution_threads[execution.id] = thread

            self.logger.info(f"Started execution {execution.id} for task {task.name}")
            return execution

        except Exception as e:
            self.logger.error(f"Failed to execute task {task_id}: {str(e)}")
            raise

    def _execute_task_async(
        self, execution_id: int, task: Task, parameters: Optional[Dict[str, Any]] = None
    ):
        """异步执行任务"""
        db = None
        try:
            from app.core.database import SessionLocal

            db = SessionLocal()

            execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not execution:
                return

            # 更新执行状态为运行中
            execution.status = "running"
            execution.started_at = datetime.now()
            execution.executor_host = (
                os.uname().nodename if hasattr(os, "uname") else "localhost"
            )
            db.commit()

            # 根据任务类型执行不同的逻辑
            if task.task_type == "command":
                self._execute_command_task(db, execution, task, parameters)
            elif task.task_type == "script":
                self._execute_script_task(db, execution, task, parameters)
            elif task.task_type == "http_request":
                self._execute_http_request_task(db, execution, task, parameters)
            elif task.task_type == "database":
                self._execute_database_task(db, execution, task, parameters)
            elif task.task_type == "file_operation":
                self._execute_file_operation_task(db, execution, task, parameters)
            else:
                raise ValueError(f"不支持的任务类型: {task.task_type}")

        except Exception as e:
            self.logger.error(f"Task execution {execution_id} failed: {str(e)}")
            if db and execution:
                execution.status = "failed"
                execution.error_message = str(e)
                execution.finished_at = datetime.now()
                if execution.started_at:
                    execution.duration_seconds = int(
                        (execution.finished_at - execution.started_at).total_seconds()
                    )
                db.commit()
        finally:
            if db:
                db.close()

            # 清理执行线程记录
            if execution_id in self.execution_threads:
                del self.execution_threads[execution_id]

    def _execute_command_task(
        self,
        db: Session,
        execution: TaskExecution,
        task: Task,
        parameters: Optional[Dict[str, Any]],
    ):
        """执行命令任务"""
        try:
            # 构建命令
            command = task.command
            if not command:
                raise ValueError("命令任务缺少执行命令")

            # 替换参数
            if parameters:
                for key, value in parameters.items():
                    command = command.replace(f"{{{key}}}", str(value))

            # 如果任务有默认参数，也进行替换
            if task.parameters:
                for key, value in task.parameters.items():
                    if f"{{{key}}}" in command:
                        command = command.replace(f"{{{key}}}", str(value))

            # 设置工作目录
            cwd = task.working_directory or os.getcwd()

            # 设置环境变量
            env = os.environ.copy()
            if task.environment_vars:
                env.update(task.environment_vars)

            # 执行命令
            self.logger.info(f"Executing command: {command}")

            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=cwd,
                env=env,
                universal_newlines=True,
            )

            # 记录进程ID
            execution.process_id = process.pid
            self.running_processes[execution.id] = process
            db.commit()

            # 等待执行完成（带超时）
            try:
                stdout, stderr = process.communicate(timeout=task.timeout_seconds)
                exit_code = process.returncode

                # 更新执行结果
                execution.exit_code = exit_code
                execution.stdout = stdout
                execution.stderr = stderr
                execution.status = "success" if exit_code == 0 else "failed"

                if exit_code != 0:
                    execution.error_message = f"命令执行失败，退出码: {exit_code}"

            except subprocess.TimeoutExpired:
                # 超时，终止进程
                process.kill()
                process.communicate()

                execution.status = "timeout"
                execution.error_message = f"任务执行超时 ({task.timeout_seconds}秒)"

            # 更新完成时间和执行时长
            execution.finished_at = datetime.now()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.finished_at - execution.started_at).total_seconds()
                )

            db.commit()

            # 清理进程记录
            if execution.id in self.running_processes:
                del self.running_processes[execution.id]

            self.logger.info(f"Command execution completed: {execution.status}")

        except Exception as e:
            self.logger.error(f"Command execution failed: {str(e)}")
            raise

    def _execute_script_task(
        self,
        db: Session,
        execution: TaskExecution,
        task: Task,
        parameters: Optional[Dict[str, Any]],
    ):
        """执行脚本任务"""
        try:
            # 脚本任务类似于命令任务，但可能需要特殊处理
            # 这里简化为调用命令执行
            self._execute_command_task(db, execution, task, parameters)

        except Exception as e:
            self.logger.error(f"Script execution failed: {str(e)}")
            raise

    def _execute_http_request_task(
        self,
        db: Session,
        execution: TaskExecution,
        task: Task,
        parameters: Optional[Dict[str, Any]],
    ):
        """执行HTTP请求任务"""
        try:
            import requests

            # 从任务参数中获取HTTP请求配置
            if not task.parameters:
                raise ValueError("HTTP请求任务缺少请求配置")

            url = task.parameters.get("url")
            method = task.parameters.get("method", "GET").upper()
            headers = task.parameters.get("headers", {})
            data = task.parameters.get("data")
            timeout = task.parameters.get("timeout", 30)

            if not url:
                raise ValueError("HTTP请求任务缺少URL")

            # 替换参数
            if parameters:
                for key, value in parameters.items():
                    url = url.replace(f"{{{key}}}", str(value))
                    if isinstance(data, str):
                        data = data.replace(f"{{{key}}}", str(value))

            # 发送HTTP请求
            self.logger.info(f"Sending HTTP {method} request to: {url}")

            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                json=data if isinstance(data, dict) else None,
                data=data if isinstance(data, str) else None,
                timeout=timeout,
            )

            # 更新执行结果
            execution.exit_code = response.status_code
            execution.stdout = response.text
            execution.status = "success" if response.ok else "failed"

            if not response.ok:
                execution.error_message = f"HTTP请求失败，状态码: {response.status_code}"

            # 更新完成时间和执行时长
            execution.finished_at = datetime.now()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.finished_at - execution.started_at).total_seconds()
                )

            db.commit()

            self.logger.info(f"HTTP request completed: {execution.status}")

        except Exception as e:
            self.logger.error(f"HTTP request execution failed: {str(e)}")
            raise

    def _execute_database_task(
        self,
        db: Session,
        execution: TaskExecution,
        task: Task,
        parameters: Optional[Dict[str, Any]],
    ):
        """执行数据库任务"""
        try:
            # 数据库任务的具体实现
            # 这里可以根据需要实现SQL执行、数据备份等功能
            raise NotImplementedError("数据库任务类型尚未实现")

        except Exception as e:
            self.logger.error(f"Database task execution failed: {str(e)}")
            raise

    def _execute_file_operation_task(
        self,
        db: Session,
        execution: TaskExecution,
        task: Task,
        parameters: Optional[Dict[str, Any]],
    ):
        """执行文件操作任务"""
        try:
            # 文件操作任务的具体实现
            # 这里可以根据需要实现文件复制、移动、删除等功能
            raise NotImplementedError("文件操作任务类型尚未实现")

        except Exception as e:
            self.logger.error(f"File operation task execution failed: {str(e)}")
            raise

    def cancel_execution(self, db: Session, execution_id: int) -> bool:
        """取消任务执行"""
        try:
            execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not execution:
                return False

            if execution.status not in ["pending", "running"]:
                return False

            # 如果有正在运行的进程，终止它
            if execution_id in self.running_processes:
                process = self.running_processes[execution_id]
                try:
                    process.terminate()
                    # 等待一段时间后强制杀死
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                except Exception as e:
                    self.logger.error(f"Failed to terminate process: {str(e)}")

                del self.running_processes[execution_id]

            # 更新执行状态
            execution.status = "cancelled"
            execution.finished_at = datetime.now()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.finished_at - execution.started_at).total_seconds()
                )

            db.commit()

            self.logger.info(f"Cancelled execution {execution_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to cancel execution {execution_id}: {str(e)}")
            return False

    def get_running_executions(self) -> Dict[int, Dict[str, Any]]:
        """获取正在运行的执行"""
        return {
            execution_id: {"process_id": process.pid, "started": True}
            for execution_id, process in self.running_processes.items()
        }

    def cleanup_finished_executions(self, db: Session):
        """清理已完成的执行记录"""
        try:
            # 清理已完成但仍在内存中的进程记录
            finished_executions = []

            for execution_id, process in self.running_processes.items():
                if process.poll() is not None:  # 进程已结束
                    finished_executions.append(execution_id)

            for execution_id in finished_executions:
                del self.running_processes[execution_id]

            # 清理已完成的线程记录
            finished_threads = []

            for execution_id, thread in self.execution_threads.items():
                if not thread.is_alive():
                    finished_threads.append(execution_id)

            for execution_id in finished_threads:
                del self.execution_threads[execution_id]

            self.logger.debug(
                f"Cleaned up {len(finished_executions)} finished executions"
            )

        except Exception as e:
            self.logger.error(f"Failed to cleanup finished executions: {str(e)}")


# 全局任务执行器实例
task_executor = TaskExecutor()
