{"name": "app-project-manager-backend", "version": "1.0.0", "description": "应用项目管理系统后端", "scripts": {"start": "python main.py", "dev": "python start_server.py", "test": "python -m pytest", "test:coverage": "python -m pytest --cov=app --cov-report=html", "lint": "python -m flake8 app/", "format": "python -m black app/", "migrate": "alembic upgrade head", "migrate:create": "alembic revision --autogenerate"}, "keywords": ["<PERSON><PERSON><PERSON>", "python", "api", "management"], "author": "Your Name", "license": "MIT"}