"""
日志监控服务
"""
import asyncio
import subprocess
import threading
from datetime import datetime
from pathlib import Path
from typing import Callable, Dict, Optional

from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.models.app import App, AppService
from app.schemas.log import LogCreate
from app.services.log import log_collection_service


class LogMonitor:
    """日志监控器"""

    def __init__(
        self, app_id: int, service_type: str, log_file_path: Optional[str] = None
    ):
        self.app_id = app_id
        self.service_type = service_type
        self.log_file_path = log_file_path
        self.is_monitoring = False
        self.monitor_thread = None
        self.process = None

    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_logs)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        if self.process:
            self.process.terminate()

    def _monitor_logs(self):
        """监控日志文件"""
        if self.log_file_path and Path(self.log_file_path).exists():
            self._monitor_file_logs()
        else:
            self._monitor_process_logs()

    def _monitor_file_logs(self):
        """监控文件日志"""
        try:
            with open(self.log_file_path, "r", encoding="utf-8") as f:
                # 移动到文件末尾
                f.seek(0, 2)

                while self.is_monitoring:
                    line = f.readline()
                    if line:
                        self._process_log_line(line.strip())
                    else:
                        # 没有新行，等待一下
                        threading.Event().wait(0.1)
        except Exception as e:
            print(f"Error monitoring file logs: {e}")

    def _monitor_process_logs(self):
        """监控进程日志输出"""
        # 这里可以根据具体需求实现进程日志监控
        # 例如通过 subprocess 启动进程并监控其输出
        pass

    def _process_log_line(self, line: str):
        """处理日志行"""
        if not line:
            return

        try:
            # 解析日志级别
            level = self._extract_log_level(line)

            # 创建日志记录
            log_data = LogCreate(
                app_id=self.app_id,
                service_type=self.service_type,
                level=level,
                message=line,
            )

            # 收集日志
            db = SessionLocal()
            try:
                log_collection_service.collect_log(db, log_data)
            finally:
                db.close()

        except Exception as e:
            print(f"Error processing log line: {e}")

    def _extract_log_level(self, line: str) -> str:
        """从日志行中提取日志级别"""
        line_upper = line.upper()

        if "ERROR" in line_upper or "ERR" in line_upper:
            return "ERROR"
        elif "WARN" in line_upper:
            return "WARNING"
        elif "INFO" in line_upper:
            return "INFO"
        elif "DEBUG" in line_upper:
            return "DEBUG"
        else:
            return "INFO"  # 默认级别


class LogMonitorService:
    """日志监控服务"""

    def __init__(self):
        self.monitors: Dict[str, LogMonitor] = {}

    def start_app_monitoring(
        self, app_id: int, service_type: str, log_file_path: Optional[str] = None
    ):
        """开始监控应用日志"""
        monitor_key = f"{app_id}_{service_type}"

        if monitor_key in self.monitors:
            self.stop_app_monitoring(app_id, service_type)

        monitor = LogMonitor(app_id, service_type, log_file_path)
        self.monitors[monitor_key] = monitor
        monitor.start_monitoring()

    def stop_app_monitoring(self, app_id: int, service_type: str):
        """停止监控应用日志"""
        monitor_key = f"{app_id}_{service_type}"

        if monitor_key in self.monitors:
            self.monitors[monitor_key].stop_monitoring()
            del self.monitors[monitor_key]

    def stop_all_monitoring(self):
        """停止所有监控"""
        for monitor in self.monitors.values():
            monitor.stop_monitoring()
        self.monitors.clear()

    def get_monitoring_status(self, app_id: int, service_type: str) -> bool:
        """获取监控状态"""
        monitor_key = f"{app_id}_{service_type}"
        return monitor_key in self.monitors and self.monitors[monitor_key].is_monitoring


class ProcessLogCollector:
    """进程日志收集器"""

    @staticmethod
    def collect_from_command(
        app_id: int,
        service_type: str,
        command: str,
        callback: Optional[Callable[[str], None]] = None,
    ):
        """从命令输出收集日志"""
        try:
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
            )

            db = SessionLocal()
            try:
                for line in iter(process.stdout.readline, ""):
                    if line:
                        line = line.strip()

                        # 提取日志级别
                        level = ProcessLogCollector._extract_log_level(line)

                        # 创建日志记录
                        log_data = LogCreate(
                            app_id=app_id,
                            service_type=service_type,
                            level=level,
                            message=line,
                        )

                        # 收集日志
                        log_collection_service.collect_log(db, log_data)

                        # 调用回调函数
                        if callback:
                            callback(line)

            finally:
                db.close()
                process.stdout.close()
                process.wait()

        except Exception as e:
            print(f"Error collecting logs from command: {e}")

    @staticmethod
    def _extract_log_level(line: str) -> str:
        """从日志行中提取日志级别"""
        line_upper = line.upper()

        if "ERROR" in line_upper or "ERR" in line_upper:
            return "ERROR"
        elif "WARN" in line_upper:
            return "WARNING"
        elif "INFO" in line_upper:
            return "INFO"
        elif "DEBUG" in line_upper:
            return "DEBUG"
        else:
            return "INFO"


# 全局日志监控服务实例
log_monitor_service = LogMonitorService()
