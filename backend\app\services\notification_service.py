"""
通知服务
"""
import asyncio
import json
import smtplib
from datetime import datetime
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Any, Dict, List, Optional

import aiohttp

from app.core.config import settings


class NotificationService:
    """通知服务"""

    def __init__(self):
        self.session = None

    async def _get_session(self):
        """获取HTTP会话"""
        if self.session is None:
            self.session = aiohttp.ClientSession()
        return self.session

    async def close(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None

    async def send_email_notification(
        self,
        recipients: List[str],
        subject: str,
        content: str,
        data: Optional[Dict[str, Any]] = None,
    ):
        """发送邮件通知"""
        try:
            if not recipients:
                return

            # 邮件服务器配置
            smtp_server = getattr(settings, "SMTP_SERVER", "localhost")
            smtp_port = getattr(settings, "SMTP_PORT", 587)
            smtp_username = getattr(settings, "SMTP_USERNAME", "")
            smtp_password = getattr(settings, "SMTP_PASSWORD", "")
            smtp_from = getattr(settings, "SMTP_FROM", "<EMAIL>")

            if not smtp_server or not smtp_username:
                print("邮件服务器配置不完整，跳过邮件通知")
                return

            # 创建邮件
            msg = MIMEMultipart()
            msg["From"] = smtp_from
            msg["To"] = ", ".join(recipients)
            msg["Subject"] = Header(subject, "utf-8")

            # 邮件内容
            if data:
                # 如果有结构化数据，生成更详细的邮件内容
                html_content = self._generate_email_html(data, content)
                msg.attach(MIMEText(html_content, "html", "utf-8"))
            else:
                msg.attach(MIMEText(content, "plain", "utf-8"))

            # 发送邮件
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()

            print(f"邮件通知已发送给: {', '.join(recipients)}")

        except Exception as e:
            print(f"发送邮件通知失败: {e}")

    async def send_webhook_notification(self, url: str, data: Dict[str, Any]):
        """发送Webhook通知"""
        try:
            if not url:
                return

            session = await self._get_session()

            # 添加时间戳
            payload = {
                **data,
                "timestamp": datetime.now().isoformat(),
                "source": "app-project-manager",
            }

            async with session.post(
                url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                if response.status == 200:
                    print(f"Webhook通知发送成功: {url}")
                else:
                    print(f"Webhook通知发送失败: {url}, 状态码: {response.status}")

        except Exception as e:
            print(f"发送Webhook通知失败: {e}")

    async def send_wechat_notification(
        self, webhook_url: str, message: str, data: Optional[Dict[str, Any]] = None
    ):
        """发送企业微信通知"""
        try:
            if not webhook_url:
                return

            session = await self._get_session()

            # 企业微信消息格式
            payload = {"msgtype": "text", "text": {"content": message}}

            # 如果是告警消息，使用markdown格式
            if data and data.get("type") == "alert":
                severity_colors = {
                    "low": "🟢",
                    "medium": "🟡",
                    "high": "🟠",
                    "critical": "🔴",
                }

                severity_icon = severity_colors.get(data.get("severity", "medium"), "⚪")

                markdown_content = f"""
{severity_icon} **告警通知**

**告警标题:** {data.get('title', '')}
**严重程度:** {data.get('severity', '').upper()}
**指标名称:** {data.get('metric_name', '')}
**当前值:** {data.get('current_value', '')}
**阈值:** {data.get('threshold_value', '')}
**触发时间:** {data.get('triggered_at', '')}

**详细信息:** {message}
"""

                payload = {
                    "msgtype": "markdown",
                    "markdown": {"content": markdown_content},
                }

            async with session.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("errcode") == 0:
                        print("企业微信通知发送成功")
                    else:
                        print(f"企业微信通知发送失败: {result.get('errmsg')}")
                else:
                    print(f"企业微信通知发送失败，状态码: {response.status}")

        except Exception as e:
            print(f"发送企业微信通知失败: {e}")

    async def send_slack_notification(
        self, webhook_url: str, message: str, data: Optional[Dict[str, Any]] = None
    ):
        """发送Slack通知"""
        try:
            if not webhook_url:
                return

            session = await self._get_session()

            # Slack消息格式
            payload = {"text": message}

            # 如果是告警消息，使用富文本格式
            if data and data.get("type") == "alert":
                severity_colors = {
                    "low": "good",
                    "medium": "warning",
                    "high": "warning",
                    "critical": "danger",
                }

                color = severity_colors.get(data.get("severity", "medium"), "warning")

                payload = {
                    "attachments": [
                        {
                            "color": color,
                            "title": data.get("title", ""),
                            "fields": [
                                {
                                    "title": "严重程度",
                                    "value": data.get("severity", "").upper(),
                                    "short": True,
                                },
                                {
                                    "title": "指标名称",
                                    "value": data.get("metric_name", ""),
                                    "short": True,
                                },
                                {
                                    "title": "当前值",
                                    "value": str(data.get("current_value", "")),
                                    "short": True,
                                },
                                {
                                    "title": "阈值",
                                    "value": str(data.get("threshold_value", "")),
                                    "short": True,
                                },
                            ],
                            "footer": "App Project Manager",
                            "ts": int(datetime.now().timestamp()),
                        }
                    ]
                }

            async with session.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                if response.status == 200:
                    print("Slack通知发送成功")
                else:
                    print(f"Slack通知发送失败，状态码: {response.status}")

        except Exception as e:
            print(f"发送Slack通知失败: {e}")

    async def send_dingtalk_notification(
        self, webhook_url: str, message: str, data: Optional[Dict[str, Any]] = None
    ):
        """发送钉钉通知"""
        try:
            if not webhook_url:
                return

            session = await self._get_session()

            # 钉钉消息格式
            payload = {"msgtype": "text", "text": {"content": message}}

            # 如果是告警消息，使用markdown格式
            if data and data.get("type") == "alert":
                severity_emojis = {
                    "low": "🟢",
                    "medium": "🟡",
                    "high": "🟠",
                    "critical": "🔴",
                }

                severity_emoji = severity_emojis.get(
                    data.get("severity", "medium"), "⚪"
                )

                markdown_content = f"""
# {severity_emoji} 告警通知

**告警标题:** {data.get('title', '')}

**严重程度:** {data.get('severity', '').upper()}

**指标名称:** {data.get('metric_name', '')}

**当前值:** {data.get('current_value', '')}

**阈值:** {data.get('threshold_value', '')}

**触发时间:** {data.get('triggered_at', '')}

---

{message}
"""

                payload = {
                    "msgtype": "markdown",
                    "markdown": {"title": "告警通知", "text": markdown_content},
                }

            async with session.post(
                webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10),
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("errcode") == 0:
                        print("钉钉通知发送成功")
                    else:
                        print(f"钉钉通知发送失败: {result.get('errmsg')}")
                else:
                    print(f"钉钉通知发送失败，状态码: {response.status}")

        except Exception as e:
            print(f"发送钉钉通知失败: {e}")

    def _generate_email_html(self, data: Dict[str, Any], content: str) -> str:
        """生成邮件HTML内容"""
        if data.get("type") == "alert":
            severity_colors = {
                "low": "#52c41a",
                "medium": "#faad14",
                "high": "#fa8c16",
                "critical": "#ff4d4f",
            }

            color = severity_colors.get(data.get("severity", "medium"), "#faad14")

            html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>告警通知</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }}
        .header {{ background-color: {color}; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; }}
        .info-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .info-table th, .info-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #eee; }}
        .info-table th {{ background-color: #f8f9fa; font-weight: bold; }}
        .footer {{ background-color: #f8f9fa; padding: 15px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 告警通知</h1>
            <p>{data.get('title', '')}</p>
        </div>
        <div class="content">
            <p>{content}</p>
            
            <table class="info-table">
                <tr><th>严重程度</th><td>{data.get('severity', '').upper()}</td></tr>
                <tr><th>指标名称</th><td>{data.get('metric_name', '')}</td></tr>
                <tr><th>当前值</th><td>{data.get('current_value', '')}</td></tr>
                <tr><th>阈值</th><td>{data.get('threshold_value', '')}</td></tr>
                <tr><th>触发时间</th><td>{data.get('triggered_at', '')}</td></tr>
            </table>
        </div>
        <div class="footer">
            <p>此邮件由 App Project Manager 系统自动发送</p>
            <p>发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
"""
            return html
        else:
            # 普通消息的HTML格式
            return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统通知</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }}
        .footer {{ margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <p>{content}</p>
        </div>
        <div class="footer">
            <p>此邮件由 App Project Manager 系统自动发送</p>
            <p>发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
"""

    async def test_notification_channel(
        self, channel_type: str, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """测试通知渠道"""
        try:
            test_data = {
                "type": "test",
                "title": "测试通知",
                "message": "这是一条测试通知消息，用于验证通知渠道配置是否正确。",
                "severity": "medium",
                "triggered_at": datetime.now().isoformat(),
            }

            if channel_type == "email":
                await self.send_email_notification(
                    recipients=config.get("recipients", []),
                    subject="[测试] 通知渠道测试",
                    content=test_data["message"],
                    data=test_data,
                )

            elif channel_type == "webhook":
                await self.send_webhook_notification(
                    url=config.get("url", ""), data=test_data
                )

            elif channel_type == "wechat":
                await self.send_wechat_notification(
                    webhook_url=config.get("webhook_url", ""),
                    message=test_data["message"],
                    data=test_data,
                )

            elif channel_type == "slack":
                await self.send_slack_notification(
                    webhook_url=config.get("webhook_url", ""),
                    message=test_data["message"],
                    data=test_data,
                )

            elif channel_type == "dingtalk":
                await self.send_dingtalk_notification(
                    webhook_url=config.get("webhook_url", ""),
                    message=test_data["message"],
                    data=test_data,
                )

            else:
                return {"success": False, "message": f"不支持的通知渠道类型: {channel_type}"}

            return {"success": True, "message": "测试通知发送成功"}

        except Exception as e:
            return {"success": False, "message": f"测试通知发送失败: {str(e)}"}


# 全局通知服务实例
notification_service = NotificationService()
