"""
服务控制服务
"""
import asyncio
import os
import signal
import subprocess
import time
from datetime import datetime
from typing import Any, Dict, Optional

import psutil
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.app import App, AppLog, AppService
from app.schemas.app import ServiceControlRequest
from app.schemas.log import LogCreate
from app.services.log import log_collection_service
from app.services.log_monitor import log_monitor_service


class ServiceControlService:
    """服务控制服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_app_service(self, app_id: int, service_type: str) -> Optional[AppService]:
        """
        获取应用服务记录

        Args:
            app_id: 应用ID
            service_type: 服务类型（frontend/backend）

        Returns:
            服务记录或None
        """
        return (
            self.db.query(AppService)
            .filter(
                AppService.app_id == app_id, AppService.service_type == service_type
            )
            .first()
        )

    def create_or_update_service(
        self, app_id: int, service_type: str, **kwargs
    ) -> AppService:
        """
        创建或更新服务记录

        Args:
            app_id: 应用ID
            service_type: 服务类型
            **kwargs: 其他字段

        Returns:
            服务记录
        """
        service = self.get_app_service(app_id, service_type)

        if service:
            # 更新现有记录
            for key, value in kwargs.items():
                setattr(service, key, value)
        else:
            # 创建新记录
            service = AppService(app_id=app_id, service_type=service_type, **kwargs)
            self.db.add(service)

        self.db.commit()
        self.db.refresh(service)
        return service

    def log_service_event(
        self, app_id: int, service_type: str, level: str, message: str
    ):
        """
        记录服务事件日志

        Args:
            app_id: 应用ID
            service_type: 服务类型
            level: 日志级别
            message: 日志消息
        """
        # 使用日志收集服务记录日志
        log_data = LogCreate(
            app_id=app_id, service_type=service_type, level=level, message=message
        )
        log_collection_service.collect_log(self.db, log_data)

    def start_service(self, app_id: int, service_type: str) -> Dict[str, Any]:
        """
        启动服务

        Args:
            app_id: 应用ID
            service_type: 服务类型（frontend/backend）

        Returns:
            操作结果

        Raises:
            HTTPException: 应用不存在或服务已在运行时抛出异常
        """
        # 获取应用信息
        app = self.db.query(App).filter(App.id == app_id).first()
        if not app:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

        # 检查服务是否已在运行
        service = self.get_app_service(app_id, service_type)
        if service and service.status == "running":
            # 检查进程是否真的在运行
            if service.pid and psutil.pid_exists(service.pid):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{service_type}服务已在运行",
                )
            else:
                # 进程不存在，更新状态
                service.status = "stopped"
                service.pid = None
                self.db.commit()

        # 获取启动命令和工作目录
        if service_type == "frontend":
            start_cmd = app.frontend_start_cmd
            work_dir = app.frontend_dir
            port = app.frontend_port
        else:  # backend
            start_cmd = app.backend_start_cmd
            work_dir = app.backend_dir
            port = app.backend_port

        if not start_cmd or not work_dir:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{service_type}服务未配置启动命令或工作目录",
            )

        if not os.path.exists(work_dir):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{service_type}工作目录不存在: {work_dir}",
            )

        try:
            # 启动进程
            process = subprocess.Popen(
                start_cmd,
                shell=True,
                cwd=work_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid if os.name != "nt" else None,
            )

            # 等待一小段时间确保进程启动
            time.sleep(1)

            # 检查进程是否成功启动
            if process.poll() is None:
                # 进程正在运行
                self.create_or_update_service(
                    app_id=app_id,
                    service_type=service_type,
                    status="running",
                    pid=process.pid,
                    port=port,
                    started_at=datetime.utcnow(),
                    stopped_at=None,
                )

                self.log_service_event(
                    app_id,
                    service_type,
                    "INFO",
                    f"{service_type}服务启动成功，PID: {process.pid}",
                )

                # 开始监控日志
                log_monitor_service.start_app_monitoring(app_id, service_type)

                # 发送WebSocket通知
                asyncio.create_task(
                    self._notify_service_status_change(app_id, service_type, "running")
                )

                return {
                    "status": "success",
                    "message": f"{service_type}服务启动成功",
                    "pid": process.pid,
                    "port": port,
                }
            else:
                # 进程启动失败
                stdout, stderr = process.communicate()
                error_msg = stderr.decode() if stderr else "未知错误"

                self.create_or_update_service(
                    app_id=app_id,
                    service_type=service_type,
                    status="error",
                    pid=None,
                    stopped_at=datetime.utcnow(),
                )

                self.log_service_event(
                    app_id, service_type, "ERROR", f"{service_type}服务启动失败: {error_msg}"
                )

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"{service_type}服务启动失败: {error_msg}",
                )

        except Exception as e:
            self.create_or_update_service(
                app_id=app_id,
                service_type=service_type,
                status="error",
                pid=None,
                stopped_at=datetime.utcnow(),
            )

            self.log_service_event(
                app_id, service_type, "ERROR", f"{service_type}服务启动异常: {str(e)}"
            )

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"{service_type}服务启动异常: {str(e)}",
            )

    def stop_service(self, app_id: int, service_type: str) -> Dict[str, Any]:
        """
        停止服务

        Args:
            app_id: 应用ID
            service_type: 服务类型（frontend/backend）

        Returns:
            操作结果

        Raises:
            HTTPException: 应用不存在或服务未运行时抛出异常
        """
        # 获取应用信息
        app = self.db.query(App).filter(App.id == app_id).first()
        if not app:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

        # 获取服务记录
        service = self.get_app_service(app_id, service_type)
        if not service or service.status != "running":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=f"{service_type}服务未运行"
            )

        if not service.pid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{service_type}服务PID不存在",
            )

        try:
            # 检查进程是否存在
            if not psutil.pid_exists(service.pid):
                # 进程不存在，直接更新状态
                service.status = "stopped"
                service.pid = None
                service.stopped_at = datetime.utcnow()
                self.db.commit()

                self.log_service_event(
                    app_id, service_type, "INFO", f"{service_type}服务进程不存在，状态已更新为停止"
                )

                return {"status": "success", "message": f"{service_type}服务已停止"}

            # 尝试优雅停止
            try:
                # 获取进程及其子进程
                parent = psutil.Process(service.pid)
                children = parent.children(recursive=True)

                # 先尝试SIGTERM
                if os.name != "nt":
                    os.killpg(os.getpgid(service.pid), signal.SIGTERM)
                else:
                    parent.terminate()

                # 等待进程结束
                gone, alive = psutil.wait_procs(children + [parent], timeout=10)

                # 如果还有进程存活，强制杀死
                for p in alive:
                    try:
                        p.kill()
                    except psutil.NoSuchProcess:
                        pass

            except psutil.NoSuchProcess:
                # 进程已经不存在
                pass
            except Exception as e:
                # 其他异常，尝试强制杀死
                try:
                    if os.name != "nt":
                        os.killpg(os.getpgid(service.pid), signal.SIGKILL)
                    else:
                        psutil.Process(service.pid).kill()
                except:
                    pass

            # 更新服务状态
            service.status = "stopped"
            service.pid = None
            service.stopped_at = datetime.utcnow()
            self.db.commit()

            self.log_service_event(
                app_id, service_type, "INFO", f"{service_type}服务停止成功"
            )

            # 停止日志监控
            log_monitor_service.stop_app_monitoring(app_id, service_type)

            # 发送WebSocket通知
            asyncio.create_task(
                self._notify_service_status_change(app_id, service_type, "stopped")
            )

            return {"status": "success", "message": f"{service_type}服务停止成功"}

        except Exception as e:
            self.log_service_event(
                app_id, service_type, "ERROR", f"{service_type}服务停止异常: {str(e)}"
            )

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"{service_type}服务停止异常: {str(e)}",
            )

    def restart_service(self, app_id: int, service_type: str) -> Dict[str, Any]:
        """
        重启服务

        Args:
            app_id: 应用ID
            service_type: 服务类型（frontend/backend）

        Returns:
            操作结果
        """
        try:
            # 先停止服务
            self.stop_service(app_id, service_type)

            # 等待一小段时间
            time.sleep(2)

            # 再启动服务
            return self.start_service(app_id, service_type)

        except HTTPException as e:
            if "未运行" in str(e.detail):
                # 服务未运行，直接启动
                return self.start_service(app_id, service_type)
            else:
                raise e

    def get_service_status(self, app_id: int, service_type: str) -> Dict[str, Any]:
        """
        获取服务状态

        Args:
            app_id: 应用ID
            service_type: 服务类型（frontend/backend）

        Returns:
            服务状态信息
        """
        service = self.get_app_service(app_id, service_type)

        if not service:
            return {"status": "not_configured", "message": f"{service_type}服务未配置"}

        # 如果服务状态为运行中，检查进程是否真的存在
        if service.status == "running" and service.pid:
            if not psutil.pid_exists(service.pid):
                # 进程不存在，更新状态
                service.status = "stopped"
                service.pid = None
                service.stopped_at = datetime.utcnow()
                self.db.commit()

        return {
            "status": service.status,
            "pid": service.pid,
            "port": service.port,
            "started_at": service.started_at,
            "stopped_at": service.stopped_at,
            "updated_at": service.updated_at,
        }

    def control_service(
        self, app_id: int, request: ServiceControlRequest
    ) -> Dict[str, Any]:
        """
        控制服务（统一入口）

        Args:
            app_id: 应用ID
            request: 服务控制请求

        Returns:
            操作结果
        """
        if request.action == "start":
            return self.start_service(app_id, request.service_type)
        elif request.action == "stop":
            return self.stop_service(app_id, request.service_type)
        elif request.action == "restart":
            return self.restart_service(app_id, request.service_type)
        elif request.action == "status":
            return self.get_service_status(app_id, request.service_type)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作: {request.action}",
            )

    async def _notify_service_status_change(
        self, app_id: int, service_type: str, status: str
    ):
        """通知服务状态变化"""
        try:
            from app.websocket.connection_manager import send_service_status_update

            await send_service_status_update(app_id, service_type, status)
        except Exception as e:
            print(f"Failed to send WebSocket notification: {e}")
