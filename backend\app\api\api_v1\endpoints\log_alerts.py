"""
日志告警管理API端点
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_user
from app.models.log import Log<PERSON>lert, LogAlertRecord
from app.models.user import User
from app.schemas.log import LogAlert as LogAlertSchema
from app.schemas.log import LogAlertCreate, LogAlertUpdate

router = APIRouter()


@router.get("/", response_model=List[LogAlertSchema])
def get_log_alerts(
    app_id: Optional[int] = Query(None, description="应用ID"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取日志告警规则列表"""
    try:
        query = db.query(LogAlert)

        if app_id is not None:
            query = query.filter(LogAlert.app_id == app_id)

        if is_active is not None:
            query = query.filter(LogAlert.is_active == is_active)

        alerts = query.offset(skip).limit(limit).all()
        return alerts

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get log alerts: {str(e)}"
        )


@router.post("/", response_model=LogAlertSchema)
def create_log_alert(
    alert_data: LogAlertCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建日志告警规则"""
    try:
        db_alert = LogAlert(**alert_data.dict())
        db.add(db_alert)
        db.commit()
        db.refresh(db_alert)

        return db_alert

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to create log alert: {str(e)}"
        )


@router.get("/{alert_id}", response_model=LogAlertSchema)
def get_log_alert(
    alert_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单个日志告警规则"""
    try:
        alert = db.query(LogAlert).filter(LogAlert.id == alert_id).first()
        if not alert:
            raise HTTPException(status_code=404, detail="Log alert not found")

        return alert

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get log alert: {str(e)}"
        )


@router.put("/{alert_id}", response_model=LogAlertSchema)
def update_log_alert(
    alert_id: int,
    alert_data: LogAlertUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """更新日志告警规则"""
    try:
        alert = db.query(LogAlert).filter(LogAlert.id == alert_id).first()
        if not alert:
            raise HTTPException(status_code=404, detail="Log alert not found")

        # 更新字段
        update_data = alert_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(alert, field, value)

        db.commit()
        db.refresh(alert)

        return alert

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update log alert: {str(e)}"
        )


@router.delete("/{alert_id}")
def delete_log_alert(
    alert_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """删除日志告警规则"""
    try:
        alert = db.query(LogAlert).filter(LogAlert.id == alert_id).first()
        if not alert:
            raise HTTPException(status_code=404, detail="Log alert not found")

        db.delete(alert)
        db.commit()

        return {"message": "Log alert deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to delete log alert: {str(e)}"
        )


@router.post("/{alert_id}/toggle")
def toggle_log_alert(
    alert_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """切换日志告警规则的激活状态"""
    try:
        alert = db.query(LogAlert).filter(LogAlert.id == alert_id).first()
        if not alert:
            raise HTTPException(status_code=404, detail="Log alert not found")

        alert.is_active = not alert.is_active
        db.commit()
        db.refresh(alert)

        status = "activated" if alert.is_active else "deactivated"
        return {
            "message": f"Log alert {status} successfully",
            "is_active": alert.is_active,
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to toggle log alert: {str(e)}"
        )


# 告警记录相关端点
@router.get("/records/", response_model=List[dict])
def get_alert_records(
    app_id: Optional[int] = Query(None, description="应用ID"),
    alert_rule_id: Optional[int] = Query(None, description="告警规则ID"),
    is_resolved: Optional[bool] = Query(None, description="是否已解决"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取告警记录列表"""
    try:
        query = db.query(LogAlertRecord)

        if app_id is not None:
            query = query.filter(LogAlertRecord.app_id == app_id)

        if alert_rule_id is not None:
            query = query.filter(LogAlertRecord.alert_rule_id == alert_rule_id)

        if is_resolved is not None:
            query = query.filter(LogAlertRecord.is_resolved == is_resolved)

        records = (
            query.order_by(LogAlertRecord.triggered_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        # 转换为字典格式，包含告警规则信息
        result = []
        for record in records:
            record_dict = {
                "id": record.id,
                "alert_rule_id": record.alert_rule_id,
                "app_id": record.app_id,
                "triggered_at": record.triggered_at,
                "log_count": record.log_count,
                "message": record.message,
                "is_resolved": record.is_resolved,
                "resolved_at": record.resolved_at,
                "alert_rule": {
                    "rule_name": record.alert_rule.rule_name,
                    "level": record.alert_rule.level,
                    "pattern": record.alert_rule.pattern,
                }
                if record.alert_rule
                else None,
            }
            result.append(record_dict)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert records: {str(e)}"
        )


@router.post("/records/{record_id}/resolve")
def resolve_alert_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """解决告警记录"""
    try:
        record = db.query(LogAlertRecord).filter(LogAlertRecord.id == record_id).first()
        if not record:
            raise HTTPException(status_code=404, detail="Alert record not found")

        if record.is_resolved:
            raise HTTPException(
                status_code=400, detail="Alert record is already resolved"
            )

        from datetime import datetime

        record.is_resolved = True
        record.resolved_at = datetime.now()

        db.commit()
        db.refresh(record)

        return {"message": "Alert record resolved successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to resolve alert record: {str(e)}"
        )


@router.get("/stats/")
def get_alert_stats(
    app_id: Optional[int] = Query(None, description="应用ID"),
    hours: int = Query(24, ge=1, le=168, description="统计时间范围(小时)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取告警统计信息"""
    try:
        from datetime import datetime, timedelta

        from sqlalchemy import func

        time_threshold = datetime.now() - timedelta(hours=hours)

        query = db.query(LogAlertRecord).filter(
            LogAlertRecord.triggered_at >= time_threshold
        )

        if app_id is not None:
            query = query.filter(LogAlertRecord.app_id == app_id)

        # 总告警数
        total_alerts = query.count()

        # 未解决告警数
        unresolved_alerts = query.filter(LogAlertRecord.is_resolved == False).count()

        # 已解决告警数
        resolved_alerts = query.filter(LogAlertRecord.is_resolved == True).count()

        # 按应用统计
        app_stats = db.query(
            LogAlertRecord.app_id, func.count(LogAlertRecord.id).label("count")
        ).filter(LogAlertRecord.triggered_at >= time_threshold)

        if app_id is not None:
            app_stats = app_stats.filter(LogAlertRecord.app_id == app_id)

        app_stats = app_stats.group_by(LogAlertRecord.app_id).all()

        return {
            "total_alerts": total_alerts,
            "unresolved_alerts": unresolved_alerts,
            "resolved_alerts": resolved_alerts,
            "by_app": [{"app_id": stat[0], "count": stat[1]} for stat in app_stats],
            "time_range_hours": hours,
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert stats: {str(e)}"
        )


# 通知相关端点
@router.post("/test-notification")
def test_notification(
    recipients: List[str],
    notification_type: str = "email",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """测试通知功能"""
    try:
        from app.services.notification import notification_service

        result = notification_service.test_notification(recipients, notification_type)

        if result["success"]:
            return {"message": result["message"]}
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to test notification: {str(e)}"
        )
