"""
自定义异常类
"""
from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class AppException(Exception):
    """应用基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationException(AppException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(message, error_code="AUTH_FAILED", **kwargs)


class AuthorizationException(AppException):
    """授权异常"""

    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(message, error_code="PERMISSION_DENIED", **kwargs)


class ValidationException(AppException):
    """验证异常"""

    def __init__(self, message: str = "数据验证失败", **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)


class DatabaseException(AppException):
    """数据库异常"""

    def __init__(self, message: str = "数据库操作失败", **kwargs):
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)


class BusinessException(AppException):
    """业务逻辑异常"""

    def __init__(self, message: str = "业务处理失败", **kwargs):
        super().__init__(message, error_code="BUSINESS_ERROR", **kwargs)


class ExternalServiceException(AppException):
    """外部服务异常"""

    def __init__(self, message: str = "外部服务调用失败", **kwargs):
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR", **kwargs)


class ResourceNotFoundException(AppException):
    """资源未找到异常"""

    def __init__(self, message: str = "资源未找到", **kwargs):
        super().__init__(message, error_code="RESOURCE_NOT_FOUND", **kwargs)


class ResourceConflictException(AppException):
    """资源冲突异常"""

    def __init__(self, message: str = "资源冲突", **kwargs):
        super().__init__(message, error_code="RESOURCE_CONFLICT", **kwargs)


class RateLimitException(AppException):
    """频率限制异常"""

    def __init__(self, message: str = "请求频率过高", **kwargs):
        super().__init__(message, error_code="RATE_LIMIT_EXCEEDED", **kwargs)


# HTTP异常映射
def app_exception_to_http_exception(exc: AppException) -> HTTPException:
    """将应用异常转换为HTTP异常"""

    status_code_mapping = {
        "AUTH_FAILED": status.HTTP_401_UNAUTHORIZED,
        "PERMISSION_DENIED": status.HTTP_403_FORBIDDEN,
        "VALIDATION_ERROR": status.HTTP_422_UNPROCESSABLE_ENTITY,
        "RESOURCE_NOT_FOUND": status.HTTP_404_NOT_FOUND,
        "RESOURCE_CONFLICT": status.HTTP_409_CONFLICT,
        "RATE_LIMIT_EXCEEDED": status.HTTP_429_TOO_MANY_REQUESTS,
        "DATABASE_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "BUSINESS_ERROR": status.HTTP_400_BAD_REQUEST,
        "EXTERNAL_SERVICE_ERROR": status.HTTP_502_BAD_GATEWAY,
    }

    status_code = status_code_mapping.get(
        exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR
    )

    return HTTPException(
        status_code=status_code,
        detail={
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
        },
    )
