"""
系统资源监控服务
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import psutil
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.system import SystemMetrics
from app.schemas.system import SystemMetricsCreate


class SystemMonitorService:
    """系统监控服务"""

    def __init__(self):
        self.monitoring = False
        self.monitor_task = None
        self.metrics_history = []

    async def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self.monitoring:
            return

        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))

    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

    async def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self.collect_system_metrics()

                # 保存到数据库
                db = next(get_db())
                try:
                    self.save_metrics(db, metrics)
                finally:
                    db.close()

                # 保存到内存历史记录
                self.metrics_history.append({**metrics, "timestamp": datetime.now()})

                # 限制历史记录数量
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]

                await asyncio.sleep(interval)

            except Exception as e:
                print(f"监控循环错误: {e}")
                await asyncio.sleep(interval)

    def collect_system_metrics(self) -> Dict:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()

            # 内存使用情况
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            # 磁盘使用情况
            disk_usage = psutil.disk_usage("/")
            disk_io = psutil.disk_io_counters()

            # 网络使用情况
            network_io = psutil.net_io_counters()

            # 进程信息
            process_count = len(psutil.pids())

            # 系统负载（仅Linux/Unix）
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                load_avg = (0, 0, 0)

            # 系统启动时间
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time

            return {
                "cpu_percent": cpu_percent,
                "cpu_count": cpu_count,
                "cpu_freq_current": cpu_freq.current if cpu_freq else 0,
                "cpu_freq_max": cpu_freq.max if cpu_freq else 0,
                "memory_total": memory.total,
                "memory_available": memory.available,
                "memory_used": memory.used,
                "memory_percent": memory.percent,
                "swap_total": swap.total,
                "swap_used": swap.used,
                "swap_percent": swap.percent,
                "disk_total": disk_usage.total,
                "disk_used": disk_usage.used,
                "disk_free": disk_usage.free,
                "disk_percent": (disk_usage.used / disk_usage.total) * 100,
                "disk_read_bytes": disk_io.read_bytes if disk_io else 0,
                "disk_write_bytes": disk_io.write_bytes if disk_io else 0,
                "disk_read_count": disk_io.read_count if disk_io else 0,
                "disk_write_count": disk_io.write_count if disk_io else 0,
                "network_bytes_sent": network_io.bytes_sent if network_io else 0,
                "network_bytes_recv": network_io.bytes_recv if network_io else 0,
                "network_packets_sent": network_io.packets_sent if network_io else 0,
                "network_packets_recv": network_io.packets_recv if network_io else 0,
                "process_count": process_count,
                "load_avg_1": load_avg[0],
                "load_avg_5": load_avg[1],
                "load_avg_15": load_avg[2],
                "uptime": uptime,
            }

        except Exception as e:
            print(f"收集系统指标错误: {e}")
            return {}

    def save_metrics(self, db: Session, metrics: Dict):
        """保存指标到数据库"""
        try:
            metrics_obj = SystemMetrics(**metrics)
            db.add(metrics_obj)
            db.commit()
        except Exception as e:
            print(f"保存系统指标错误: {e}")
            db.rollback()

    def get_current_metrics(self) -> Dict:
        """获取当前系统指标"""
        return self.collect_system_metrics()

    def get_metrics_history(self, hours: int = 24) -> List[Dict]:
        """获取历史指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            metrics
            for metrics in self.metrics_history
            if metrics["timestamp"] > cutoff_time
        ]

    def get_process_list(self) -> List[Dict]:
        """获取进程列表"""
        try:
            processes = []
            for proc in psutil.process_iter(
                ["pid", "name", "cpu_percent", "memory_percent", "status"]
            ):
                try:
                    proc_info = proc.info
                    processes.append(
                        {
                            "pid": proc_info["pid"],
                            "name": proc_info["name"],
                            "cpu_percent": proc_info["cpu_percent"],
                            "memory_percent": proc_info["memory_percent"],
                            "status": proc_info["status"],
                        }
                    )
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按CPU使用率排序
            processes.sort(key=lambda x: x["cpu_percent"] or 0, reverse=True)
            return processes[:50]  # 返回前50个进程

        except Exception as e:
            print(f"获取进程列表错误: {e}")
            return []

    def get_network_connections(self) -> List[Dict]:
        """获取网络连接"""
        try:
            connections = []
            for conn in psutil.net_connections():
                if conn.status == psutil.CONN_ESTABLISHED:
                    connections.append(
                        {
                            "local_address": f"{conn.laddr.ip}:{conn.laddr.port}"
                            if conn.laddr
                            else "",
                            "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}"
                            if conn.raddr
                            else "",
                            "status": conn.status,
                            "pid": conn.pid,
                        }
                    )
            return connections

        except Exception as e:
            print(f"获取网络连接错误: {e}")
            return []

    def get_disk_partitions(self) -> List[Dict]:
        """获取磁盘分区信息"""
        try:
            partitions = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    partitions.append(
                        {
                            "device": partition.device,
                            "mountpoint": partition.mountpoint,
                            "fstype": partition.fstype,
                            "total": usage.total,
                            "used": usage.used,
                            "free": usage.free,
                            "percent": (usage.used / usage.total) * 100,
                        }
                    )
                except PermissionError:
                    continue

            return partitions

        except Exception as e:
            print(f"获取磁盘分区错误: {e}")
            return []

    def analyze_resource_usage(self) -> Dict:
        """分析资源使用情况"""
        try:
            current_metrics = self.get_current_metrics()

            # 分析CPU使用情况
            cpu_status = "normal"
            if current_metrics.get("cpu_percent", 0) > 80:
                cpu_status = "high"
            elif current_metrics.get("cpu_percent", 0) > 90:
                cpu_status = "critical"

            # 分析内存使用情况
            memory_status = "normal"
            memory_percent = current_metrics.get("memory_percent", 0)
            if memory_percent > 80:
                memory_status = "high"
            elif memory_percent > 90:
                memory_status = "critical"

            # 分析磁盘使用情况
            disk_status = "normal"
            disk_percent = current_metrics.get("disk_percent", 0)
            if disk_percent > 80:
                disk_status = "high"
            elif disk_percent > 90:
                disk_status = "critical"

            return {
                "cpu_status": cpu_status,
                "memory_status": memory_status,
                "disk_status": disk_status,
                "overall_status": max(
                    [cpu_status, memory_status, disk_status],
                    key=lambda x: ["normal", "high", "critical"].index(x),
                ),
                "recommendations": self._generate_recommendations(current_metrics),
            }

        except Exception as e:
            print(f"分析资源使用错误: {e}")
            return {}

    def _generate_recommendations(self, metrics: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []

        cpu_percent = metrics.get("cpu_percent", 0)
        memory_percent = metrics.get("memory_percent", 0)
        disk_percent = metrics.get("disk_percent", 0)

        if cpu_percent > 80:
            recommendations.append("CPU使用率过高，建议检查高CPU占用的进程")

        if memory_percent > 80:
            recommendations.append("内存使用率过高，建议释放不必要的内存或增加内存")

        if disk_percent > 80:
            recommendations.append("磁盘空间不足，建议清理不必要的文件或扩展磁盘")

        if len(recommendations) == 0:
            recommendations.append("系统资源使用正常")

        return recommendations


# 全局监控服务实例
system_monitor = SystemMonitorService()
