"""
日志收集服务测试
"""
import shutil
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.database import Base
from app.models.app import App, AppLog
from app.models.log import Log<PERSON>lert, LogAlertRecord
from app.models.user import User
from app.schemas.log import LogCreate, LogQuery, LogRotationConfig
from app.services.log import LogCollectionService


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()

    # 创建测试用户
    user = User(
        username="testuser", email="<EMAIL>", password_hash="hashed_password"
    )
    session.add(user)
    session.commit()

    # 创建测试应用
    app = App(name="Test App", description="Test application", created_by=user.id)
    session.add(app)
    session.commit()

    yield session
    session.close()


@pytest.fixture
def log_service():
    """创建日志收集服务实例"""
    with tempfile.TemporaryDirectory() as temp_dir:
        service = LogCollectionService()
        service.log_dir = Path(temp_dir)
        yield service


class TestLogCollectionService:
    """日志收集服务测试类"""

    def test_collect_single_log(self, db_session, log_service):
        """测试收集单条日志"""
        # 准备测试数据
        log_data = LogCreate(
            app_id=1, service_type="backend", level="INFO", message="Test log message"
        )

        # 执行测试
        result = log_service.collect_log(db_session, log_data)

        # 验证结果
        assert result.app_id == 1
        assert result.service_type == "backend"
        assert result.level == "INFO"
        assert result.message == "Test log message"
        assert result.id is not None

        # 验证数据库记录
        db_log = db_session.query(AppLog).filter(AppLog.id == result.id).first()
        assert db_log is not None
        assert db_log.message == "Test log message"

    def test_collect_logs_batch(self, db_session, log_service):
        """测试批量收集日志"""
        # 准备测试数据
        logs_data = [
            LogCreate(app_id=1, service_type="backend", level="INFO", message="Log 1"),
            LogCreate(app_id=1, service_type="backend", level="ERROR", message="Log 2"),
            LogCreate(
                app_id=1, service_type="frontend", level="WARNING", message="Log 3"
            ),
        ]

        # 执行测试
        results = log_service.collect_logs_batch(db_session, logs_data)

        # 验证结果
        assert len(results) == 3
        assert all(log.id is not None for log in results)

        # 验证数据库记录
        db_logs = db_session.query(AppLog).all()
        assert len(db_logs) == 3

    def test_write_to_file(self, log_service):
        """测试写入日志文件"""
        # 准备测试数据
        log_data = LogCreate(
            app_id=1, service_type="backend", level="INFO", message="Test file log"
        )

        # 执行测试
        log_service._write_to_file(log_data)

        # 验证文件创建
        log_file = log_service.log_dir / "app_1" / "backend.log"
        assert log_file.exists()

        # 验证文件内容
        content = log_file.read_text(encoding="utf-8")
        assert "Test file log" in content
        assert "[INFO]" in content

    def test_log_rotation(self, log_service):
        """测试日志轮转"""
        # 创建测试日志文件
        app_log_dir = log_service.log_dir / "app_1"
        app_log_dir.mkdir(exist_ok=True)
        log_file = app_log_dir / "backend.log"

        # 写入大量数据模拟大文件
        large_content = "x" * (log_service.rotation_config.max_size + 1000)
        log_file.write_text(large_content, encoding="utf-8")

        # 执行轮转
        log_service._rotate_log_file(log_file)

        # 验证轮转结果
        backup_file = log_file.with_suffix(".log.1")
        assert backup_file.exists()
        assert not log_file.exists() or log_file.stat().st_size == 0

    def test_match_pattern(self, log_service):
        """测试模式匹配"""
        # 测试简单字符串匹配
        assert log_service._match_pattern("Error occurred", "error")
        assert log_service._match_pattern("ERROR: Something went wrong", "error")
        assert not log_service._match_pattern("Info message", "error")

        # 测试正则表达式匹配
        assert log_service._match_pattern(
            "User 123 logged in", "regex:User \\d+ logged"
        )
        assert not log_service._match_pattern(
            "User logged in", "regex:User \\d+ logged"
        )

    def test_alert_rule_trigger(self, db_session, log_service):
        """测试告警规则触发"""
        # 创建告警规则
        alert_rule = LogAlert(
            app_id=1,
            rule_name="Error Alert",
            level="ERROR",
            pattern="error",
            threshold=2,
            time_window=5,
            is_active=True,
        )
        db_session.add(alert_rule)
        db_session.commit()

        # 创建触发告警的日志
        log1 = AppLog(
            app_id=1, service_type="backend", level="ERROR", message="Error occurred 1"
        )
        log2 = AppLog(
            app_id=1, service_type="backend", level="ERROR", message="Error occurred 2"
        )

        db_session.add_all([log1, log2])
        db_session.commit()

        # 检查告警规则
        log_service._check_alert_rules(db_session, log2)

        # 验证告警记录
        alert_record = db_session.query(LogAlertRecord).first()
        assert alert_record is not None
        assert alert_record.alert_rule_id == alert_rule.id
        assert alert_record.log_count >= 2

    def test_get_log_stats(self, db_session, log_service):
        """测试获取日志统计"""
        # 创建测试日志
        logs = [
            AppLog(app_id=1, service_type="backend", level="INFO", message="Info 1"),
            AppLog(app_id=1, service_type="backend", level="INFO", message="Info 2"),
            AppLog(app_id=1, service_type="backend", level="ERROR", message="Error 1"),
            AppLog(
                app_id=1, service_type="backend", level="WARNING", message="Warning 1"
            ),
        ]

        db_session.add_all(logs)
        db_session.commit()

        # 获取统计信息
        stats = log_service.get_log_stats(db_session, app_id=1)

        # 验证统计结果
        assert stats.total_logs == 4
        assert stats.info_logs == 2
        assert stats.error_logs == 1
        assert stats.warning_logs == 1
        assert stats.debug_logs == 0

    def test_cleanup_old_logs(self, db_session, log_service):
        """测试清理旧日志"""
        # 创建新旧日志
        old_time = datetime.now() - timedelta(days=35)
        recent_time = datetime.now() - timedelta(days=1)

        old_log = AppLog(
            app_id=1,
            service_type="backend",
            level="INFO",
            message="Old log",
            timestamp=old_time,
        )
        recent_log = AppLog(
            app_id=1,
            service_type="backend",
            level="INFO",
            message="Recent log",
            timestamp=recent_time,
        )

        db_session.add_all([old_log, recent_log])
        db_session.commit()

        # 执行清理
        log_service.cleanup_old_logs(db_session, days=30)

        # 验证清理结果
        remaining_logs = db_session.query(AppLog).all()
        assert len(remaining_logs) == 1
        assert remaining_logs[0].message == "Recent log"

    def test_rotation_config(self):
        """测试日志轮转配置"""
        config = LogRotationConfig()

        # 验证默认配置
        assert config.max_size == 100 * 1024 * 1024  # 100MB
        assert config.backup_count == 5
        assert config.compress == True

        # 测试自定义配置
        custom_config = LogRotationConfig(
            max_size=50 * 1024 * 1024, backup_count=3, compress=False
        )

        assert custom_config.max_size == 50 * 1024 * 1024
        assert custom_config.backup_count == 3
        assert custom_config.compress == False


if __name__ == "__main__":
    pytest.main([__file__])
