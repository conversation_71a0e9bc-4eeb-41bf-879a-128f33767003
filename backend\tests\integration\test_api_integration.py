"""
API集成测试
"""
import asyncio

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.auth import create_access_token
from app.main import app
from app.models.app import App, AppService
from app.models.task import Task, TaskExecution
from app.models.user import User


@pytest.mark.integration
class TestAuthIntegration:
    """认证API集成测试"""

    def test_login_flow(self, client: TestClient, db_session: Session):
        """测试完整登录流程"""
        # 创建测试用户
        from app.core.security import get_password_hash

        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=get_password_hash("testpassword"),
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        # 测试登录
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "testuser", "password": "testpassword"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

        # 使用token访问受保护的端点
        token = data["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        user_data = response.json()
        assert user_data["username"] == "testuser"
        assert user_data["email"] == "<EMAIL>"

    def test_login_invalid_credentials(self, client: TestClient):
        """测试无效凭据登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "nonexistent", "password": "wrongpassword"},
        )

        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]

    def test_protected_endpoint_without_token(self, client: TestClient):
        """测试未授权访问受保护端点"""
        response = client.get("/api/v1/users/me")
        assert response.status_code == 401


@pytest.mark.integration
class TestAppManagementIntegration:
    """应用管理API集成测试"""

    def test_app_crud_flow(self, client: TestClient, db_session: Session):
        """测试应用CRUD完整流程"""
        # 创建测试用户并获取token
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 创建应用
        app_data = {
            "name": "Test App",
            "description": "Test application",
            "frontend_dir": "/tmp/frontend",
            "backend_dir": "/tmp/backend",
            "frontend_start_cmd": "npm start",
            "backend_start_cmd": "python main.py",
            "frontend_port": 3000,
            "backend_port": 8000,
            "is_active": True,
        }

        response = client.post("/api/v1/apps/", json=app_data, headers=headers)
        assert response.status_code == 201
        created_app = response.json()
        app_id = created_app["id"]
        assert created_app["name"] == "Test App"

        # 获取应用列表
        response = client.get("/api/v1/apps/", headers=headers)
        assert response.status_code == 200
        apps = response.json()
        assert len(apps) == 1
        assert apps[0]["name"] == "Test App"

        # 获取单个应用
        response = client.get(f"/api/v1/apps/{app_id}", headers=headers)
        assert response.status_code == 200
        app = response.json()
        assert app["name"] == "Test App"

        # 更新应用
        update_data = {"name": "Updated Test App", "description": "Updated description"}
        response = client.put(
            f"/api/v1/apps/{app_id}", json=update_data, headers=headers
        )
        assert response.status_code == 200
        updated_app = response.json()
        assert updated_app["name"] == "Updated Test App"

        # 删除应用
        response = client.delete(f"/api/v1/apps/{app_id}", headers=headers)
        assert response.status_code == 200

        # 验证应用已删除
        response = client.get(f"/api/v1/apps/{app_id}", headers=headers)
        assert response.status_code == 404

    def test_service_control_integration(self, client: TestClient, db_session: Session):
        """测试服务控制集成"""
        # 创建测试用户和应用
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        app = App(
            name="Test App",
            frontend_dir="/tmp/frontend",
            backend_dir="/tmp/backend",
            frontend_start_cmd="echo 'frontend'",
            backend_start_cmd="echo 'backend'",
            created_by=user.id,
        )
        db_session.add(app)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 测试启动前端服务
        with pytest.raises(Exception):  # 预期会失败，因为目录不存在
            response = client.post(
                f"/api/v1/apps/{app.id}/services/control",
                json={"service_type": "frontend", "action": "start"},
                headers=headers,
            )

        # 测试获取服务状态
        response = client.get(f"/api/v1/apps/{app.id}/services/status", headers=headers)
        assert response.status_code == 200


@pytest.mark.integration
class TestTaskManagementIntegration:
    """任务管理API集成测试"""

    def test_task_execution_flow(self, client: TestClient, db_session: Session):
        """测试任务执行完整流程"""
        # 创建测试用户和应用
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        app = App(name="Test App", created_by=user.id)
        db_session.add(app)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 创建任务
        task_data = {
            "name": "Test Task",
            "description": "Test task description",
            "task_type": "command",
            "command": "echo 'Hello World'",
            "app_id": app.id,
            "is_active": True,
        }

        response = client.post("/api/v1/tasks/", json=task_data, headers=headers)
        assert response.status_code == 201
        created_task = response.json()
        task_id = created_task["id"]

        # 执行任务
        response = client.post(f"/api/v1/tasks/{task_id}/execute", headers=headers)
        assert response.status_code == 200
        execution = response.json()
        execution_id = execution["id"]

        # 获取执行记录
        response = client.get(
            f"/api/v1/tasks/executions/{execution_id}", headers=headers
        )
        assert response.status_code == 200
        execution_detail = response.json()
        assert execution_detail["status"] in ["success", "running", "pending"]

        # 获取任务执行历史
        response = client.get(f"/api/v1/tasks/{task_id}/executions", headers=headers)
        assert response.status_code == 200
        executions = response.json()
        assert len(executions) >= 1


@pytest.mark.integration
class TestSystemMonitoringIntegration:
    """系统监控API集成测试"""

    def test_system_metrics_flow(self, client: TestClient, db_session: Session):
        """测试系统监控完整流程"""
        # 创建测试用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 获取系统指标
        response = client.get("/api/v1/system/metrics", headers=headers)
        assert response.status_code == 200
        metrics = response.json()
        assert "cpu_percent" in metrics
        assert "memory_percent" in metrics
        assert "disk_percent" in metrics

        # 获取系统状态
        response = client.get("/api/v1/system/status", headers=headers)
        assert response.status_code == 200
        status = response.json()
        assert "status" in status

        # 获取进程列表
        response = client.get("/api/v1/system/processes", headers=headers)
        assert response.status_code == 200
        processes = response.json()
        assert isinstance(processes, list)


@pytest.mark.integration
class TestLogManagementIntegration:
    """日志管理API集成测试"""

    def test_log_collection_and_query(self, client: TestClient, db_session: Session):
        """测试日志收集和查询"""
        # 创建测试用户和应用
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        app = App(name="Test App", created_by=user.id)
        db_session.add(app)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 收集日志
        log_data = {
            "app_id": app.id,
            "service_type": "frontend",
            "level": "INFO",
            "message": "Test log message",
        }

        response = client.post("/api/v1/logs/collect", json=log_data, headers=headers)
        assert response.status_code == 201

        # 查询日志
        response = client.get(f"/api/v1/logs/?app_id={app.id}", headers=headers)
        assert response.status_code == 200
        logs = response.json()
        assert len(logs) >= 1
        assert logs[0]["message"] == "Test log message"


@pytest.mark.integration
class TestWebSocketIntegration:
    """WebSocket集成测试"""

    @pytest.mark.asyncio
    async def test_websocket_connection(self, db_session: Session):
        """测试WebSocket连接"""
        # 创建测试应用
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        # 这里需要使用WebSocket测试客户端
        # 由于复杂性，这里只做基本的连接测试
        from fastapi.testclient import TestClient

        from app.main import app as fastapi_app

        with TestClient(fastapi_app) as client:
            # 测试WebSocket端点是否存在
            # 实际的WebSocket测试需要专门的测试工具
            pass


@pytest.mark.integration
class TestEndToEndWorkflow:
    """端到端工作流测试"""

    def test_complete_app_lifecycle(self, client: TestClient, db_session: Session):
        """测试完整的应用生命周期"""
        # 1. 用户注册和登录
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 2. 创建应用
        app_data = {
            "name": "E2E Test App",
            "description": "End-to-end test application",
            "is_active": True,
        }

        response = client.post("/api/v1/apps/", json=app_data, headers=headers)
        assert response.status_code == 201
        app = response.json()
        app_id = app["id"]

        # 3. 创建任务
        task_data = {
            "name": "E2E Test Task",
            "description": "End-to-end test task",
            "task_type": "command",
            "command": "echo 'E2E Test'",
            "app_id": app_id,
            "is_active": True,
        }

        response = client.post("/api/v1/tasks/", json=task_data, headers=headers)
        assert response.status_code == 201
        task = response.json()
        task_id = task["id"]

        # 4. 执行任务
        response = client.post(f"/api/v1/tasks/{task_id}/execute", headers=headers)
        assert response.status_code == 200
        execution = response.json()

        # 5. 检查执行结果
        response = client.get(
            f"/api/v1/tasks/executions/{execution['id']}", headers=headers
        )
        assert response.status_code == 200
        execution_detail = response.json()
        assert execution_detail["task_id"] == task_id

        # 6. 收集日志
        log_data = {
            "app_id": app_id,
            "service_type": "system",
            "level": "INFO",
            "message": "E2E test completed",
        }

        response = client.post("/api/v1/logs/collect", json=log_data, headers=headers)
        assert response.status_code == 201

        # 7. 查询日志
        response = client.get(f"/api/v1/logs/?app_id={app_id}", headers=headers)
        assert response.status_code == 200
        logs = response.json()
        assert len(logs) >= 1

        # 8. 获取系统指标
        response = client.get("/api/v1/system/metrics", headers=headers)
        assert response.status_code == 200
        metrics = response.json()
        assert "cpu_percent" in metrics

        # 9. 清理 - 删除应用
        response = client.delete(f"/api/v1/apps/{app_id}", headers=headers)
        assert response.status_code == 200

    def test_error_handling_workflow(self, client: TestClient, db_session: Session):
        """测试错误处理工作流"""
        # 创建测试用户
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 测试访问不存在的应用
        response = client.get("/api/v1/apps/999", headers=headers)
        assert response.status_code == 404

        # 测试创建重复名称的应用
        app_data = {"name": "Duplicate App", "is_active": True}

        response = client.post("/api/v1/apps/", json=app_data, headers=headers)
        assert response.status_code == 201

        # 尝试创建同名应用
        response = client.post("/api/v1/apps/", json=app_data, headers=headers)
        assert response.status_code == 400
        assert "已存在" in response.json()["detail"]

        # 测试无效的任务执行
        response = client.post("/api/v1/tasks/999/execute", headers=headers)
        assert response.status_code == 404

        # 测试无效的服务控制
        response = client.post(
            "/api/v1/apps/999/services/control",
            json={"service_type": "frontend", "action": "start"},
            headers=headers,
        )
        assert response.status_code == 404
