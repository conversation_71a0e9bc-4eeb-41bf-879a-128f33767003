"""
实时状态推送服务
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List

from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.app import App, AppLog, AppService
from app.models.task import TaskExecution
from app.services.system_monitor import SystemMonitorService
from app.websocket.connection_manager import (
    connection_manager,
    send_alert_triggered,
    send_log_message,
    send_system_metrics,
    send_task_execution_update,
)

logger = logging.getLogger(__name__)


class RealtimeService:
    """实时状态推送服务"""

    def __init__(self):
        self.is_running = False
        self.tasks: List[asyncio.Task] = []
        self.system_monitor = SystemMonitorService()

    async def start(self):
        """启动实时服务"""
        if self.is_running:
            return

        self.is_running = True
        logger.info("Starting realtime service...")

        # 启动各种监控任务
        self.tasks = [
            asyncio.create_task(self._monitor_system_metrics()),
            asyncio.create_task(self._monitor_app_status()),
            asyncio.create_task(self._monitor_task_executions()),
            asyncio.create_task(self._cleanup_connections()),
        ]

        logger.info("Realtime service started")

    async def stop(self):
        """停止实时服务"""
        if not self.is_running:
            return

        self.is_running = False
        logger.info("Stopping realtime service...")

        # 取消所有任务
        for task in self.tasks:
            task.cancel()

        # 等待任务完成
        await asyncio.gather(*self.tasks, return_exceptions=True)
        self.tasks.clear()

        logger.info("Realtime service stopped")

    async def _monitor_system_metrics(self):
        """监控系统指标"""
        while self.is_running:
            try:
                # 获取有活跃连接的应用
                active_apps = connection_manager.get_active_apps()

                if active_apps:
                    # 获取系统指标
                    metrics = await self.system_monitor.get_system_metrics()

                    # 向所有活跃应用推送系统指标
                    for app_id in active_apps:
                        await send_system_metrics(app_id, metrics)

                # 每30秒推送一次
                await asyncio.sleep(30)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in system metrics monitoring: {e}")
                await asyncio.sleep(5)

    async def _monitor_app_status(self):
        """监控应用状态"""
        while self.is_running:
            try:
                # 获取有活跃连接的应用
                active_apps = connection_manager.get_active_apps()

                if active_apps:
                    db = next(get_db())

                    for app_id in active_apps:
                        # 检查应用服务状态
                        services = (
                            db.query(AppService)
                            .filter(AppService.app_id == app_id)
                            .all()
                        )

                        for service in services:
                            # 检查进程是否还在运行
                            if service.status == "running" and service.pid:
                                import psutil

                                if not psutil.pid_exists(service.pid):
                                    # 进程已死，更新状态
                                    service.status = "stopped"
                                    service.pid = None
                                    service.stopped_at = datetime.utcnow()
                                    db.commit()

                                    # 发送状态更新
                                    from app.websocket.connection_manager import (
                                        send_service_status_update,
                                    )

                                    await send_service_status_update(
                                        app_id, service.service_type, "stopped"
                                    )

                    db.close()

                # 每10秒检查一次
                await asyncio.sleep(10)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in app status monitoring: {e}")
                await asyncio.sleep(5)

    async def _monitor_task_executions(self):
        """监控任务执行状态"""
        last_check_time = datetime.utcnow()

        while self.is_running:
            try:
                db = next(get_db())

                # 查询最近的任务执行记录
                recent_executions = (
                    db.query(TaskExecution)
                    .filter(TaskExecution.updated_at > last_check_time)
                    .all()
                )

                for execution in recent_executions:
                    # 获取任务关联的应用ID
                    if execution.task and execution.task.app_id:
                        app_id = execution.task.app_id

                        # 发送任务执行更新
                        await send_task_execution_update(
                            app_id,
                            {
                                "execution_id": execution.id,
                                "task_id": execution.task_id,
                                "task_name": execution.task.name
                                if execution.task
                                else None,
                                "status": execution.status,
                                "start_time": execution.start_time.isoformat()
                                if execution.start_time
                                else None,
                                "end_time": execution.end_time.isoformat()
                                if execution.end_time
                                else None,
                                "result": execution.result,
                                "error_message": execution.error_message,
                            },
                        )

                last_check_time = datetime.utcnow()
                db.close()

                # 每5秒检查一次
                await asyncio.sleep(5)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in task execution monitoring: {e}")
                await asyncio.sleep(5)

    async def _cleanup_connections(self):
        """清理断开的连接"""
        while self.is_running:
            try:
                # 每分钟清理一次
                await asyncio.sleep(60)

                # 这里可以添加连接清理逻辑
                # 例如：检查长时间无响应的连接并断开

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
                await asyncio.sleep(5)

    async def push_log_message(self, app_id: int, log_data: Dict[str, Any]):
        """推送日志消息"""
        try:
            await send_log_message(app_id, log_data)
        except Exception as e:
            logger.error(f"Failed to push log message: {e}")

    async def push_alert(self, app_id: int, alert_data: Dict[str, Any]):
        """推送告警消息"""
        try:
            await send_alert_triggered(app_id, alert_data)
        except Exception as e:
            logger.error(f"Failed to push alert: {e}")

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        active_apps = connection_manager.get_active_apps()
        total_connections = connection_manager.get_connection_count()

        app_connections = {}
        for app_id in active_apps:
            app_connections[app_id] = connection_manager.get_connection_count(app_id)

        return {
            "total_connections": total_connections,
            "active_apps": len(active_apps),
            "app_connections": app_connections,
            "is_running": self.is_running,
        }


# 全局实时服务实例
realtime_service = RealtimeService()


async def start_realtime_service():
    """启动实时服务"""
    await realtime_service.start()


async def stop_realtime_service():
    """停止实时服务"""
    await realtime_service.stop()


def get_realtime_service() -> RealtimeService:
    """获取实时服务实例"""
    return realtime_service
