"""
系统监控API端点
"""
from datetime import datetime, timedelta
from typing import List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.auth import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.system import (
    AlertQuery,
    AlertRule,
    AlertRuleCreate,
    AlertRuleUpdate,
    DiskPartition,
    NetworkConnection,
    ProcessInfo,
    ResourceAnalysis,
    SystemAlert,
    SystemAlertCreate,
    SystemAlertUpdate,
    SystemMetrics,
    SystemMetricsQuery,
    SystemOverview,
    SystemStats,
)
from app.services.system_monitor import system_monitor

router = APIRouter()


# 系统指标相关端点
@router.get("/metrics/current", response_model=SystemMetrics)
def get_current_metrics(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取当前系统指标"""
    try:
        metrics = system_monitor.get_current_metrics()
        if not metrics:
            raise HTTPException(
                status_code=500, detail="Failed to collect system metrics"
            )

        # 添加时间戳
        metrics["created_at"] = datetime.now()
        metrics["id"] = 0  # 临时ID，因为这是实时数据

        return SystemMetrics(**metrics)

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get current metrics: {str(e)}"
        )


@router.get("/metrics/history", response_model=List[SystemMetrics])
def get_metrics_history(
    hours: int = Query(24, ge=1, le=168, description="历史小时数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取历史系统指标"""
    try:
        history = system_monitor.get_metrics_history(hours)

        # 转换为SystemMetrics格式
        metrics_list = []
        for i, metrics in enumerate(history):
            metrics_data = {**metrics}
            metrics_data["id"] = i
            metrics_data["created_at"] = metrics_data.pop("timestamp")
            metrics_list.append(SystemMetrics(**metrics_data))

        return metrics_list

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get metrics history: {str(e)}"
        )


@router.get("/processes", response_model=List[ProcessInfo])
def get_process_list(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取进程列表"""
    try:
        processes = system_monitor.get_process_list()
        return [ProcessInfo(**proc) for proc in processes]

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get process list: {str(e)}"
        )


@router.get("/network/connections", response_model=List[NetworkConnection])
def get_network_connections(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取网络连接"""
    try:
        connections = system_monitor.get_network_connections()
        return [NetworkConnection(**conn) for conn in connections]

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get network connections: {str(e)}"
        )


@router.get("/disk/partitions", response_model=List[DiskPartition])
def get_disk_partitions(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取磁盘分区信息"""
    try:
        partitions = system_monitor.get_disk_partitions()
        return [DiskPartition(**part) for part in partitions]

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get disk partitions: {str(e)}"
        )


@router.get("/analysis", response_model=ResourceAnalysis)
def get_resource_analysis(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取资源使用分析"""
    try:
        analysis = system_monitor.analyze_resource_usage()
        return ResourceAnalysis(**analysis)

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to analyze resource usage: {str(e)}"
        )


@router.get("/overview", response_model=SystemOverview)
def get_system_overview(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取系统概览"""
    try:
        # 获取当前指标
        current_metrics_data = system_monitor.get_current_metrics()
        current_metrics_data["created_at"] = datetime.now()
        current_metrics_data["id"] = 0
        current_metrics = SystemMetrics(**current_metrics_data)

        # 获取资源分析
        analysis_data = system_monitor.analyze_resource_usage()
        resource_analysis = ResourceAnalysis(**analysis_data)

        # 获取活跃告警（模拟数据）
        active_alerts = []

        # 获取进程列表
        processes_data = system_monitor.get_process_list()
        top_processes = [ProcessInfo(**proc) for proc in processes_data[:10]]

        # 获取磁盘分区
        partitions_data = system_monitor.get_disk_partitions()
        disk_partitions = [DiskPartition(**part) for part in partitions_data]

        # 获取网络连接
        connections_data = system_monitor.get_network_connections()
        network_connections = [
            NetworkConnection(**conn) for conn in connections_data[:20]
        ]

        # 系统统计
        system_stats = SystemStats(
            total_alerts=0,
            active_alerts=0,
            critical_alerts=0,
            alert_rules_count=0,
            avg_cpu_usage=current_metrics_data.get("cpu_percent"),
            avg_memory_usage=current_metrics_data.get("memory_percent"),
            avg_disk_usage=current_metrics_data.get("disk_percent"),
        )

        return SystemOverview(
            current_metrics=current_metrics,
            resource_analysis=resource_analysis,
            active_alerts=active_alerts,
            top_processes=top_processes,
            disk_partitions=disk_partitions,
            network_connections=network_connections,
            system_stats=system_stats,
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get system overview: {str(e)}"
        )


# 监控控制端点
@router.post("/monitoring/start")
def start_monitoring(
    interval: int = Query(60, ge=10, le=3600, description="监控间隔(秒)"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """启动系统监控"""
    try:
        background_tasks.add_task(system_monitor.start_monitoring, interval)
        return {"message": f"System monitoring started with {interval}s interval"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start monitoring: {str(e)}"
        )


@router.post("/monitoring/stop")
def stop_monitoring(
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """停止系统监控"""
    try:
        background_tasks.add_task(system_monitor.stop_monitoring)
        return {"message": "System monitoring stopped"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop monitoring: {str(e)}"
        )


@router.get("/monitoring/status")
def get_monitoring_status(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取监控状态"""
    try:
        return {
            "monitoring": system_monitor.monitoring,
            "metrics_count": len(system_monitor.metrics_history),
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get monitoring status: {str(e)}"
        )


# 告警规则管理端点
@router.get("/alerts/rules", response_model=List[AlertRule])
def get_alert_rules(
    is_active: Optional[bool] = Query(None, description="是否激活"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取告警规则列表"""
    try:
        from app.services.alert_manager import alert_manager

        rules = alert_manager.get_alert_rules(db, is_active)
        return rules

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert rules: {str(e)}"
        )


@router.post("/alerts/rules", response_model=AlertRule)
def create_alert_rule(
    rule_data: AlertRuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建告警规则"""
    try:
        from app.services.alert_manager import alert_manager

        rule = alert_manager.create_alert_rule(db, rule_data, current_user.username)
        return rule

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create alert rule: {str(e)}"
        )


@router.get("/alerts/rules/{rule_id}", response_model=AlertRule)
def get_alert_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单个告警规则"""
    try:
        from app.services.alert_manager import alert_manager

        rule = alert_manager.get_alert_rule(db, rule_id)
        if not rule:
            raise HTTPException(status_code=404, detail="Alert rule not found")
        return rule

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert rule: {str(e)}"
        )


@router.put("/alerts/rules/{rule_id}", response_model=AlertRule)
def update_alert_rule(
    rule_id: int,
    rule_data: AlertRuleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """更新告警规则"""
    try:
        from app.services.alert_manager import alert_manager

        rule = alert_manager.update_alert_rule(db, rule_id, rule_data)
        if not rule:
            raise HTTPException(status_code=404, detail="Alert rule not found")
        return rule

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to update alert rule: {str(e)}"
        )


@router.delete("/alerts/rules/{rule_id}")
def delete_alert_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """删除告警规则"""
    try:
        from app.services.alert_manager import alert_manager

        success = alert_manager.delete_alert_rule(db, rule_id)
        if not success:
            raise HTTPException(status_code=404, detail="Alert rule not found")
        return {"message": "Alert rule deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete alert rule: {str(e)}"
        )


@router.get("/alerts/metrics", response_model=List[dict])
def get_available_metrics(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取可用的监控指标"""
    try:
        from app.services.alert_manager import alert_manager

        metrics = alert_manager.get_available_metrics()
        return metrics

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get available metrics: {str(e)}"
        )


@router.get("/alerts", response_model=List[SystemAlert])
def get_system_alerts(
    severity: Optional[str] = Query(None, description="严重程度"),
    status: Optional[str] = Query(None, description="告警状态"),
    alert_type: Optional[str] = Query(None, description="告警类型"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取系统告警列表"""
    try:
        from app.services.alert_manager import alert_manager

        query = AlertQuery(
            severity=severity,
            status=status,
            alert_type=alert_type,
            start_time=start_time,
            end_time=end_time,
            skip=skip,
            limit=limit,
        )

        alerts = alert_manager.get_system_alerts(db, query)
        return alerts

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get system alerts: {str(e)}"
        )


@router.get("/alerts/{alert_id}", response_model=SystemAlert)
def get_system_alert(
    alert_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单个系统告警"""
    try:
        from app.services.alert_manager import alert_manager

        alert = alert_manager.get_system_alert(db, alert_id)
        if not alert:
            raise HTTPException(status_code=404, detail="System alert not found")
        return alert

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get system alert: {str(e)}"
        )


@router.post("/alerts/{alert_id}/acknowledge")
def acknowledge_alert(
    alert_id: int,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """确认告警"""
    try:
        from app.services.alert_manager import alert_manager

        alert = alert_manager.acknowledge_alert(
            db, alert_id, current_user.username, notes
        )
        if not alert:
            raise HTTPException(status_code=404, detail="System alert not found")
        return {"message": "Alert acknowledged successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to acknowledge alert: {str(e)}"
        )


@router.post("/alerts/{alert_id}/resolve")
def resolve_alert(
    alert_id: int,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """解决告警"""
    try:
        from app.services.alert_manager import alert_manager

        alert = alert_manager.resolve_alert(db, alert_id, current_user.username, notes)
        if not alert:
            raise HTTPException(status_code=404, detail="System alert not found")
        return {"message": "Alert resolved successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to resolve alert: {str(e)}"
        )


@router.get("/alerts/statistics", response_model=dict)
def get_alert_statistics(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取告警统计"""
    try:
        from app.services.alert_manager import alert_manager

        stats = alert_manager.get_alert_statistics(db, days)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert statistics: {str(e)}"
        )


# 告警评估控制端点
@router.post("/alerts/evaluation/start")
def start_alert_evaluation(
    interval: int = Query(60, ge=10, le=3600, description="评估间隔(秒)"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """启动告警评估"""
    try:
        from app.services.alert_manager import alert_manager

        background_tasks.add_task(alert_manager.start_evaluation, interval)
        return {"message": f"Alert evaluation started with {interval}s interval"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start alert evaluation: {str(e)}"
        )


@router.post("/alerts/evaluation/stop")
def stop_alert_evaluation(
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """停止告警评估"""
    try:
        from app.services.alert_manager import alert_manager

        background_tasks.add_task(alert_manager.stop_evaluation)
        return {"message": "Alert evaluation stopped"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop alert evaluation: {str(e)}"
        )


@router.get("/alerts/evaluation/status")
def get_alert_evaluation_status(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取告警评估状态"""
    try:
        from app.services.alert_manager import alert_manager

        return {
            "evaluating": alert_manager.evaluating,
            "alert_history_count": len(alert_manager.alert_history),
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get alert evaluation status: {str(e)}"
        )


# 通知测试端点
@router.post("/notifications/test")
def test_notification_channel(
    channel_type: str,
    config: dict,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """测试通知渠道"""
    try:
        from app.services.notification_service import notification_service

        async def test_task():
            result = await notification_service.test_notification_channel(
                channel_type, config
            )
            return result

        background_tasks.add_task(test_task)
        return {"message": "Test notification sent"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to test notification channel: {str(e)}"
        )


@router.get("/stats", response_model=SystemStats)
def get_system_stats(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取系统统计信息"""
    try:
        from app.services.alert_manager import alert_manager

        # 获取告警统计
        alert_stats = alert_manager.get_alert_statistics(db, 30)

        # 获取当前系统指标
        current_metrics = system_monitor.get_current_metrics()

        return SystemStats(
            total_alerts=alert_stats.get("total_alerts", 0),
            active_alerts=alert_stats.get("active_alerts", 0),
            critical_alerts=alert_stats.get("critical_alerts", 0),
            alert_rules_count=alert_stats.get("alert_rules_count", 0),
            avg_cpu_usage=current_metrics.get("cpu_percent"),
            avg_memory_usage=current_metrics.get("memory_percent"),
            avg_disk_usage=current_metrics.get("disk_percent"),
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get system stats: {str(e)}"
        )


# 系统操作端点
@router.post("/cleanup/metrics")
def cleanup_old_metrics(
    days: int = Query(30, ge=1, le=365, description="清理多少天前的指标"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """清理旧的系统指标"""
    try:
        # TODO: 实现数据库清理
        cutoff_date = datetime.now() - timedelta(days=days)

        # 清理内存中的历史数据
        system_monitor.metrics_history = [
            metrics
            for metrics in system_monitor.metrics_history
            if metrics.get("timestamp", datetime.now()) > cutoff_date
        ]

        return {"message": f"Cleaned up metrics older than {days} days"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to cleanup old metrics: {str(e)}"
        )


@router.get("/health")
def system_health_check(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """系统健康检查"""
    try:
        current_metrics = system_monitor.get_current_metrics()
        analysis = system_monitor.analyze_resource_usage()

        # 判断系统健康状态
        overall_status = analysis.get("overall_status", "unknown")
        health_status = "healthy" if overall_status == "normal" else "unhealthy"

        return {
            "status": health_status,
            "overall_status": overall_status,
            "cpu_percent": current_metrics.get("cpu_percent", 0),
            "memory_percent": current_metrics.get("memory_percent", 0),
            "disk_percent": current_metrics.get("disk_percent", 0),
            "uptime": current_metrics.get("uptime", 0),
            "monitoring_active": system_monitor.monitoring,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
