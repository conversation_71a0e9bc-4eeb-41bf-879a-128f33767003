"""
WebSocket路由
"""
import asyncio
import json
import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.app import App
from app.services.app import AppManagementService
from app.services.system_monitor import SystemMonitorService
from app.websocket.connection_manager import MessageType, connection_manager

logger = logging.getLogger(__name__)

router = APIRouter()


@router.websocket("/ws/apps/{app_id}")
async def websocket_endpoint(
    websocket: WebSocket, app_id: int, db: Session = Depends(get_db)
):
    """应用WebSocket端点"""

    # 验证应用是否存在
    app_service = AppService(db)
    app = app_service.get_app(app_id)
    if not app:
        await websocket.close(code=4004, reason="App not found")
        return

    # 建立连接
    connection_id = await connection_manager.connect(websocket, app_id)

    try:
        # 发送初始状态
        await send_initial_status(websocket, app_id, db)

        # 启动后台任务
        monitor_task = asyncio.create_task(
            monitor_app_status(app_id, connection_id, db)
        )

        # 处理客户端消息
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_client_message(
                    websocket, app_id, connection_id, message, db
                )
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from connection {connection_id}")
            except Exception as e:
                logger.error(f"Error handling message from {connection_id}: {e}")

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {connection_id}")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # 清理资源
        monitor_task.cancel()
        connection_manager.disconnect(app_id, connection_id)


async def send_initial_status(websocket: WebSocket, app_id: int, db: Session):
    """发送初始状态信息"""
    try:
        app_service = AppService(db)
        app = app_service.get_app(app_id)

        if app:
            # 发送应用状态
            app_status = {
                "type": MessageType.APP_STATUS_UPDATE,
                "data": {
                    "app_id": app_id,
                    "frontend_status": app.frontend_status,
                    "backend_status": app.backend_status,
                    "is_active": app.is_active,
                },
                "timestamp": app.updated_at.isoformat() if app.updated_at else None,
            }
            await websocket.send_text(json.dumps(app_status))

            # 发送系统指标
            system_monitor = SystemMonitor()
            metrics = await system_monitor.get_system_metrics()

            metrics_message = {
                "type": MessageType.SYSTEM_METRICS,
                "data": metrics,
                "timestamp": asyncio.get_event_loop().time(),
            }
            await websocket.send_text(json.dumps(metrics_message))

    except Exception as e:
        logger.error(f"Failed to send initial status: {e}")


async def handle_client_message(
    websocket: WebSocket,
    app_id: int,
    connection_id: str,
    message: Dict[str, Any],
    db: Session,
):
    """处理客户端消息"""
    message_type = message.get("type")
    data = message.get("data", {})

    if message_type == "ping":
        # 响应心跳
        pong_message = {
            "type": MessageType.PONG,
            "data": {"timestamp": message.get("timestamp")},
            "timestamp": asyncio.get_event_loop().time(),
        }
        await websocket.send_text(json.dumps(pong_message))

    elif message_type == "subscribe":
        # 订阅消息类型
        types = data.get("types", [])
        connection_manager.subscribe(connection_id, types)

    elif message_type == "unsubscribe":
        # 取消订阅
        types = data.get("types", [])
        connection_manager.unsubscribe(connection_id, types)

    elif message_type == "get_status":
        # 请求当前状态
        await send_initial_status(websocket, app_id, db)

    else:
        logger.warning(f"Unknown message type: {message_type}")


async def monitor_app_status(app_id: int, connection_id: str, db: Session):
    """监控应用状态变化"""
    system_monitor = SystemMonitor()

    while True:
        try:
            # 每30秒发送一次系统指标
            await asyncio.sleep(30)

            # 检查连接是否还活跃
            if app_id not in connection_manager.active_connections:
                break
            if connection_id not in connection_manager.active_connections[app_id]:
                break

            # 获取系统指标
            metrics = await system_monitor.get_system_metrics()

            # 发送系统指标更新
            metrics_message = {
                "type": MessageType.SYSTEM_METRICS,
                "data": metrics,
                "timestamp": asyncio.get_event_loop().time(),
            }

            await connection_manager.send_to_app(
                app_id, metrics_message, MessageType.SYSTEM_METRICS
            )

        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in monitor_app_status: {e}")
            await asyncio.sleep(5)  # 错误后等待5秒再重试


@router.websocket("/ws/system")
async def system_websocket_endpoint(websocket: WebSocket):
    """系统级WebSocket端点"""
    await websocket.accept()

    try:
        while True:
            # 系统级实时更新逻辑
            await asyncio.sleep(1)

    except WebSocketDisconnect:
        pass
