"""
菜单管理API端点
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_active_user, require_permissions
from app.models.user import User as UserModel
from app.schemas.menu import Menu, MenuCreate, MenuTree, MenuUpdate, UserMenu
from app.services.menu import MenuService

router = APIRouter()


@router.get("/", response_model=List[Menu], summary="获取菜单列表")
async def get_menus(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:read")),
):
    """
    获取菜单列表

    需要权限: menu:read
    """
    menu_service = MenuService(db)
    menus = menu_service.get_menus(skip=skip, limit=limit)
    return menus


@router.get("/tree", response_model=List[MenuTree], summary="获取菜单树形结构")
async def get_menu_tree(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:read")),
):
    """
    获取菜单树形结构

    需要权限: menu:read
    """
    menu_service = MenuService(db)
    return menu_service.get_menu_tree()


@router.get("/user-menus", response_model=List[UserMenu], summary="获取当前用户菜单")
async def get_user_menus(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_active_user),
):
    """
    获取当前用户可访问的菜单（用于前端路由生成）
    """
    menu_service = MenuService(db)
    return menu_service.get_user_menus(current_user)


@router.get("/{menu_id}", response_model=Menu, summary="获取菜单详情")
async def get_menu(
    menu_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:read")),
):
    """
    根据ID获取菜单详情

    需要权限: menu:read
    """
    menu_service = MenuService(db)
    menu = menu_service.get_menu_by_id(menu_id)
    if not menu:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="菜单不存在")
    return menu


@router.post("/", response_model=Menu, summary="创建菜单")
async def create_menu(
    menu_data: MenuCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:create")),
):
    """
    创建新菜单

    需要权限: menu:create

    - **name**: 菜单名称（必填，唯一）
    - **path**: 菜单路径（可选）
    - **component**: 组件路径（可选）
    - **icon**: 菜单图标（可选）
    - **parent_id**: 父菜单ID（可选，不填表示根菜单）
    - **sort_order**: 排序（默认为0）
    - **is_hidden**: 是否隐藏（默认为False）
    - **is_external**: 是否外部链接（默认为False）
    """
    menu_service = MenuService(db)
    return menu_service.create_menu(menu_data)


@router.put("/{menu_id}", response_model=Menu, summary="更新菜单")
async def update_menu(
    menu_id: int,
    menu_data: MenuUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:update")),
):
    """
    更新菜单信息

    需要权限: menu:update
    """
    menu_service = MenuService(db)
    return menu_service.update_menu(menu_id, menu_data)


@router.delete("/{menu_id}", summary="删除菜单")
async def delete_menu(
    menu_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:delete")),
):
    """
    删除菜单

    需要权限: menu:delete

    注意：只能删除没有子菜单的菜单
    """
    menu_service = MenuService(db)
    menu_service.delete_menu(menu_id)
    return {"message": "菜单删除成功"}


@router.get("/{menu_id}/children", response_model=List[Menu], summary="获取子菜单")
async def get_menu_children(
    menu_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:read")),
):
    """
    获取菜单的直接子菜单

    需要权限: menu:read
    """
    menu_service = MenuService(db)
    # 先检查菜单是否存在
    menu = menu_service.get_menu_by_id(menu_id)
    if not menu:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="菜单不存在")

    return menu_service.get_menu_children(menu_id)


@router.get("/{menu_id}/descendants", response_model=List[Menu], summary="获取所有后代菜单")
async def get_menu_descendants(
    menu_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("menu:read")),
):
    """
    获取菜单的所有后代菜单

    需要权限: menu:read
    """
    menu_service = MenuService(db)
    # 先检查菜单是否存在
    menu = menu_service.get_menu_by_id(menu_id)
    if not menu:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="菜单不存在")

    return menu_service.get_menu_descendants(menu_id)
