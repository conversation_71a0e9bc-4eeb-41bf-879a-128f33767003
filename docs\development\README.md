# 开发指南

本文档为应用项目管理系统的开发者提供详细的开发指南，包括环境搭建、代码规范、测试指南等。

## 目录

- [开发环境搭建](#开发环境搭建)
- [项目架构](#项目架构)
- [代码规范](#代码规范)
- [开发工作流](#开发工作流)
- [测试指南](#测试指南)
- [调试技巧](#调试技巧)
- [性能优化](#性能优化)

## 开发环境搭建

### 前置要求

- **Python** 3.10+
- **Node.js** 18+
- **Git** 2.30+
- **PostgreSQL** 12+ (或 Docker)
- **Redis** 6+ (或 Docker)
- **VS Code** (推荐) 或其他 IDE

### 环境配置

1. **克隆项目**
```bash
git clone https://github.com/your-username/app-project-manager.git
cd app-project-manager
```

2. **后端环境**
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件
```

3. **前端环境**
```bash
cd frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件
```

4. **数据库初始化**
```bash
# 使用 Docker (推荐)
docker-compose up -d postgres redis

# 或手动安装 PostgreSQL 和 Redis
# 然后创建数据库
createdb app_manager

# 运行迁移
cd backend
alembic upgrade head
```

5. **安装开发工具**
```bash
# 安装 pre-commit
pip install pre-commit
pre-commit install

# 安装代码格式化工具
npm install -g prettier
pip install black isort flake8 mypy
```

### VS Code 配置

推荐的 VS Code 扩展：

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode-remote.remote-containers"
  ]
}
```

工作区设置：

```json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  }
}
```

## 项目架构

### 后端架构

```
backend/
├── app/
│   ├── api/                    # API 路由层
│   │   └── api_v1/
│   │       ├── endpoints/      # 具体端点实现
│   │       └── api.py         # 路由聚合
│   ├── core/                  # 核心配置
│   │   ├── config.py          # 应用配置
│   │   ├── database.py        # 数据库配置
│   │   ├── security.py        # 安全相关
│   │   ├── logging.py         # 日志配置
│   │   └── exceptions.py      # 异常定义
│   ├── models/                # 数据模型
│   ├── schemas/               # Pydantic 模式
│   ├── services/              # 业务逻辑层
│   ├── middleware/            # 中间件
│   ├── utils/                 # 工具函数
│   └── websocket/             # WebSocket 处理
├── tests/                     # 测试代码
├── scripts/                   # 脚本文件
└── alembic/                   # 数据库迁移
```

### 前端架构

```
frontend/
├── src/
│   ├── components/            # 可复用组件
│   │   ├── common/           # 通用组件
│   │   └── business/         # 业务组件
│   ├── views/                # 页面视图
│   ├── stores/               # Pinia 状态管理
│   ├── api/                  # API 调用
│   ├── utils/                # 工具函数
│   ├── types/                # TypeScript 类型
│   ├── styles/               # 样式文件
│   ├── router/               # 路由配置
│   └── composables/          # 组合式函数
├── tests/                    # 测试代码
└── public/                   # 静态资源
```

### 设计模式

1. **后端设计模式**
   - **Repository Pattern**: 数据访问层抽象
   - **Service Layer**: 业务逻辑封装
   - **Dependency Injection**: 依赖注入
   - **Factory Pattern**: 对象创建
   - **Observer Pattern**: 事件通知

2. **前端设计模式**
   - **Composition API**: Vue 3 组合式 API
   - **Store Pattern**: 状态管理
   - **Module Pattern**: 模块化开发
   - **Observer Pattern**: 响应式数据

## 代码规范

### Python 代码规范

遵循 PEP 8 规范，使用以下工具：

```bash
# 代码格式化
black app/ tests/

# 导入排序
isort app/ tests/

# 代码检查
flake8 app/ tests/

# 类型检查
mypy app/
```

**命名规范**:
- 类名: `PascalCase`
- 函数名: `snake_case`
- 变量名: `snake_case`
- 常量名: `UPPER_SNAKE_CASE`
- 私有成员: `_leading_underscore`

**文档字符串**:
```python
def create_user(user_data: UserCreate) -> User:
    """
    创建新用户
    
    Args:
        user_data: 用户创建数据
        
    Returns:
        创建的用户对象
        
    Raises:
        ValidationError: 数据验证失败
        DatabaseError: 数据库操作失败
    """
    pass
```

### TypeScript/Vue 代码规范

使用 ESLint + Prettier 进行代码检查和格式化：

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

**命名规范**:
- 组件名: `PascalCase`
- 函数名: `camelCase`
- 变量名: `camelCase`
- 常量名: `UPPER_SNAKE_CASE`
- 文件名: `kebab-case`

**组件规范**:
```vue
<template>
  <div class="user-form">
    <!-- 模板内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { User } from '@/types/user'

// Props 定义
interface Props {
  user?: User
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits 定义
interface Emits {
  (e: 'submit', user: User): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formData = ref<Partial<User>>({})

// 计算属性
const isValid = computed(() => {
  return formData.value.name && formData.value.email
})

// 方法
const handleSubmit = () => {
  if (isValid.value) {
    emit('submit', formData.value as User)
  }
}
</script>

<style scoped>
.user-form {
  /* 样式定义 */
}
</style>
```

### Git 提交规范

使用 Conventional Commits 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**:
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具、依赖更新

**示例**:
```
feat(auth): add JWT token refresh functionality

- Implement token refresh endpoint
- Add automatic token renewal in frontend
- Update authentication middleware

Closes #123
```

## 开发工作流

### 分支策略

使用 Git Flow 分支模型：

- `main`: 生产环境分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `release/*`: 发布分支
- `hotfix/*`: 热修复分支

### 开发流程

1. **创建功能分支**
```bash
git checkout develop
git pull origin develop
git checkout -b feature/user-management
```

2. **开发和测试**
```bash
# 编写代码
# 运行测试
npm run test  # 前端
pytest        # 后端

# 代码检查
npm run lint
python scripts/format_code.py
```

3. **提交代码**
```bash
git add .
git commit -m "feat(user): add user management functionality"
```

4. **推送和创建 PR**
```bash
git push origin feature/user-management
# 在 GitHub 创建 Pull Request
```

5. **代码审查和合并**
```bash
# 审查通过后合并到 develop
git checkout develop
git pull origin develop
git branch -d feature/user-management
```

### 代码审查清单

**后端代码审查**:
- [ ] 代码符合 PEP 8 规范
- [ ] 有适当的错误处理
- [ ] 有单元测试覆盖
- [ ] API 文档已更新
- [ ] 数据库迁移正确
- [ ] 安全性考虑
- [ ] 性能影响评估

**前端代码审查**:
- [ ] 代码符合 ESLint 规则
- [ ] 组件可复用性
- [ ] 类型定义完整
- [ ] 用户体验良好
- [ ] 响应式设计
- [ ] 无障碍性考虑
- [ ] 性能优化

## 测试指南

### 后端测试

**测试结构**:
```
tests/
├── conftest.py           # 测试配置和夹具
├── api/                  # API 测试
│   ├── test_auth.py
│   └── test_users.py
├── services/             # 服务测试
│   └── test_user_service.py
└── utils/                # 工具测试
    └── test_helpers.py
```

**测试示例**:
```python
import pytest
from fastapi.testclient import TestClient
from app.models.user import User

class TestUserAPI:
    def test_create_user(self, client: TestClient, auth_headers: dict):
        """测试创建用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post(
            "/api/v1/users/",
            json=user_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert "password" not in data
    
    def test_get_user_list(self, client: TestClient, auth_headers: dict):
        """测试获取用户列表"""
        response = client.get("/api/v1/users/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
```

**运行测试**:
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/api/test_users.py

# 生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行性能测试
pytest -m performance
```

### 前端测试

**测试结构**:
```
tests/
├── setup.ts             # 测试配置
├── utils.ts             # 测试工具
├── components/          # 组件测试
│   └── UserForm.test.ts
├── stores/              # 状态测试
│   └── auth.test.ts
└── api/                 # API 测试
    └── user.test.ts
```

**组件测试示例**:
```typescript
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserForm from '@/components/UserForm.vue'
import { createTestPinia } from '@/tests/utils'

describe('UserForm', () => {
  it('should render form fields', () => {
    const wrapper = mount(UserForm, {
      global: {
        plugins: [createTestPinia()]
      }
    })
    
    expect(wrapper.find('[data-testid="username-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="email-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="submit-button"]').exists()).toBe(true)
  })
  
  it('should validate required fields', async () => {
    const wrapper = mount(UserForm, {
      global: {
        plugins: [createTestPinia()]
      }
    })
    
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.find('.error-message').text()).toContain('用户名不能为空')
  })
})
```

**运行测试**:
```bash
# 运行所有测试
npm run test

# 运行特定测试
npm run test -- UserForm.test.ts

# 生成覆盖率报告
npm run test:coverage

# 运行 UI 测试
npm run test:ui
```

## 调试技巧

### 后端调试

1. **使用 Python 调试器**
```python
import pdb; pdb.set_trace()  # 设置断点
```

2. **日志调试**
```python
import logging
logger = logging.getLogger(__name__)

logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
```

3. **FastAPI 调试模式**
```python
# main.py
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开启热重载
        debug=True    # 开启调试模式
    )
```

### 前端调试

1. **Vue DevTools**
   - 安装 Vue DevTools 浏览器扩展
   - 查看组件状态和事件

2. **浏览器调试**
```typescript
console.log('调试信息', data)
console.warn('警告信息', warning)
console.error('错误信息', error)

// 设置断点
debugger
```

3. **网络调试**
   - 使用浏览器开发者工具的 Network 面板
   - 检查 API 请求和响应

## 性能优化

### 后端性能优化

1. **数据库查询优化**
```python
# 使用预加载避免 N+1 查询
from sqlalchemy.orm import selectinload

users = session.query(User).options(
    selectinload(User.roles)
).all()

# 使用索引
class User(Base):
    __tablename__ = "users"
    
    email = Column(String, index=True)  # 添加索引
```

2. **缓存优化**
```python
from app.utils.cache import cache_manager

@cache_manager.cached(ttl=300)
def get_user_list():
    return session.query(User).all()
```

3. **异步处理**
```python
from celery import Celery

@celery.task
def send_email(user_id: int, subject: str, content: str):
    # 异步发送邮件
    pass
```

### 前端性能优化

1. **组件懒加载**
```typescript
// 路由懒加载
const UserManagement = () => import('@/views/UserManagement.vue')

// 组件懒加载
import { defineAsyncComponent } from 'vue'
const AsyncComponent = defineAsyncComponent(() => import('./Component.vue'))
```

2. **虚拟滚动**
```vue
<template>
  <VirtualList
    :items="largeDataSet"
    :item-height="50"
    :container-height="400"
  >
    <template #default="{ item }">
      <div>{{ item.name }}</div>
    </template>
  </VirtualList>
</template>
```

3. **图片优化**
```vue
<template>
  <img
    v-lazy="imageSrc"
    :alt="imageAlt"
    loading="lazy"
  />
</template>
```

更多开发相关信息请参考：
- [API 文档](../api/README.md)
- [部署指南](../deployment/README.md)
- [故障排除](troubleshooting.md)
