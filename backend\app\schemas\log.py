"""
日志相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class LogBase(BaseModel):
    """日志基础模式"""

    service_type: str
    level: str
    message: str


class LogCreate(LogBase):
    """日志创建模式"""

    app_id: int


class LogQuery(BaseModel):
    """日志查询模式"""

    app_id: Optional[int] = None
    service_type: Optional[str] = None
    level: Optional[str] = None
    keyword: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    skip: int = 0
    limit: int = 100


class Log(LogBase):
    """日志响应模式"""

    id: int
    app_id: int
    timestamp: datetime

    model_config = {"from_attributes": True}


class LogStats(BaseModel):
    """日志统计模式"""

    total_logs: int
    error_logs: int
    warning_logs: int
    info_logs: int
    debug_logs: int


class LogAlert(BaseModel):
    """日志告警模式"""

    id: int
    app_id: int
    rule_name: str
    level: str
    pattern: str
    threshold: int
    time_window: int  # 分钟
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class LogAlertCreate(BaseModel):
    """日志告警创建模式"""

    app_id: int
    rule_name: str
    level: str
    pattern: str
    threshold: int = 1
    time_window: int = 5  # 分钟
    is_active: bool = True


class LogAlertUpdate(BaseModel):
    """日志告警更新模式"""

    rule_name: Optional[str] = None
    level: Optional[str] = None
    pattern: Optional[str] = None
    threshold: Optional[int] = None
    time_window: Optional[int] = None
    is_active: Optional[bool] = None


class LogRotationConfig(BaseModel):
    """日志轮转配置模式"""

    max_size: int = 100 * 1024 * 1024  # 100MB
    backup_count: int = 5
    compress: bool = True


class LogQueryResponse(BaseModel):
    """日志查询响应模式"""

    logs: List[Log]
    total: int
    skip: int
    limit: int


class LogExportRequest(BaseModel):
    """日志导出请求模式"""

    query: LogQuery
    format: str = "csv"  # csv, json, txt


class LogDeleteRequest(BaseModel):
    """日志删除请求模式"""

    query: LogQuery
