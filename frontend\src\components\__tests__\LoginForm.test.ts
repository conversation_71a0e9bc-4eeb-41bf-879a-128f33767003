/**
 * 登录表单组件测试
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ElMessage } from 'element-plus'
import LoginForm from '../LoginForm.vue'
import { useAuthStore } from '@/stores/auth'
import { 
  mountComponent, 
  userInput, 
  userClick, 
  submitForm,
  mockApiResponse,
  mockApiError,
  cleanupTest
} from '../../../tests/utils'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock API
vi.mock('@/api/auth', () => ({
  login: vi.fn()
}))

describe('LoginForm', () => {
  let wrapper: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    
    wrapper = mountComponent(LoginForm, {
      global: {
        stubs: {
          'el-form': true,
          'el-form-item': true,
          'el-input': true,
          'el-button': true,
          'el-checkbox': true
        }
      }
    })
  })

  afterEach(() => {
    cleanupTest()
  })

  describe('渲染测试', () => {
    it('应该正确渲染登录表单', () => {
      expect(wrapper.find('[data-testid="login-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="username-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="password-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="login-button"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="remember-checkbox"]').exists()).toBe(true)
    })

    it('应该显示正确的标题', () => {
      expect(wrapper.find('h2').text()).toBe('用户登录')
    })

    it('初始状态下登录按钮应该可用', () => {
      const loginButton = wrapper.find('[data-testid="login-button"]')
      expect(loginButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('表单验证测试', () => {
    it('用户名为空时应该显示错误', async () => {
      await userInput(wrapper, '[data-testid="username-input"]', '')
      await userInput(wrapper, '[data-testid="password-input"]', 'password')
      await submitForm(wrapper, '[data-testid="login-form"]')

      expect(wrapper.find('.el-form-item__error').text()).toContain('请输入用户名')
    })

    it('密码为空时应该显示错误', async () => {
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', '')
      await submitForm(wrapper, '[data-testid="login-form"]')

      expect(wrapper.find('.el-form-item__error').text()).toContain('请输入密码')
    })

    it('密码长度不足时应该显示错误', async () => {
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', '123')
      await submitForm(wrapper, '[data-testid="login-form"]')

      expect(wrapper.find('.el-form-item__error').text()).toContain('密码长度至少6位')
    })

    it('有效输入时不应该显示错误', async () => {
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', 'password123')

      expect(wrapper.find('.el-form-item__error').exists()).toBe(false)
    })
  })

  describe('登录功能测试', () => {
    it('成功登录时应该调用API并跳转', async () => {
      const { login } = await import('@/api/auth')
      const mockLogin = login as any
      mockLogin.mockResolvedValue({
        access_token: 'test-token',
        token_type: 'bearer'
      })

      // 填写表单
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', 'password123')
      
      // 提交表单
      await submitForm(wrapper, '[data-testid="login-form"]')

      // 验证API调用
      expect(mockLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123'
      })

      // 验证成功消息
      expect(ElMessage.success).toHaveBeenCalledWith('登录成功')
    })

    it('登录失败时应该显示错误消息', async () => {
      const { login } = await import('@/api/auth')
      const mockLogin = login as any
      mockLogin.mockRejectedValue({
        response: {
          data: { message: '用户名或密码错误' },
          status: 401
        }
      })

      // 填写表单
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', 'wrongpassword')
      
      // 提交表单
      await submitForm(wrapper, '[data-testid="login-form"]')

      // 验证错误消息
      expect(ElMessage.error).toHaveBeenCalledWith('用户名或密码错误')
    })

    it('登录过程中应该显示加载状态', async () => {
      const { login } = await import('@/api/auth')
      const mockLogin = login as any
      
      // 创建一个延迟的Promise
      let resolveLogin: any
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve
      })
      mockLogin.mockReturnValue(loginPromise)

      // 填写表单
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', 'password123')
      
      // 提交表单
      const submitPromise = submitForm(wrapper, '[data-testid="login-form"]')

      // 验证加载状态
      expect(wrapper.find('[data-testid="login-button"]').attributes('loading')).toBe('true')

      // 完成登录
      resolveLogin({ access_token: 'test-token', token_type: 'bearer' })
      await submitPromise

      // 验证加载状态结束
      expect(wrapper.find('[data-testid="login-button"]').attributes('loading')).toBeUndefined()
    })
  })

  describe('记住我功能测试', () => {
    it('勾选记住我时应该保存用户名', async () => {
      // 填写用户名
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      
      // 勾选记住我
      await userClick(wrapper, '[data-testid="remember-checkbox"]')
      
      // 模拟页面刷新
      wrapper.unmount()
      wrapper = mountComponent(LoginForm)

      // 验证用户名被记住
      expect(wrapper.find('[data-testid="username-input"]').element.value).toBe('testuser')
    })

    it('不勾选记住我时不应该保存用户名', async () => {
      // 填写用户名但不勾选记住我
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      
      // 模拟页面刷新
      wrapper.unmount()
      wrapper = mountComponent(LoginForm)

      // 验证用户名没有被记住
      expect(wrapper.find('[data-testid="username-input"]').element.value).toBe('')
    })
  })

  describe('键盘事件测试', () => {
    it('在密码框按回车应该提交表单', async () => {
      const { login } = await import('@/api/auth')
      const mockLogin = login as any
      mockLogin.mockResolvedValue({
        access_token: 'test-token',
        token_type: 'bearer'
      })

      // 填写表单
      await userInput(wrapper, '[data-testid="username-input"]', 'testuser')
      await userInput(wrapper, '[data-testid="password-input"]', 'password123')
      
      // 在密码框按回车
      await wrapper.find('[data-testid="password-input"]').trigger('keyup.enter')

      // 验证API调用
      expect(mockLogin).toHaveBeenCalled()
    })
  })

  describe('响应式测试', () => {
    it('在小屏幕上应该调整布局', async () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480,
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()

      // 验证响应式类名
      expect(wrapper.find('.login-form').classes()).toContain('mobile')
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      expect(wrapper.find('[data-testid="username-input"]').attributes('aria-label')).toBe('用户名')
      expect(wrapper.find('[data-testid="password-input"]').attributes('aria-label')).toBe('密码')
      expect(wrapper.find('[data-testid="login-button"]').attributes('aria-label')).toBe('登录')
    })

    it('表单应该有正确的role属性', () => {
      expect(wrapper.find('[data-testid="login-form"]').attributes('role')).toBe('form')
    })
  })
})
