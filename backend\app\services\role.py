"""
角色管理服务
"""
from typing import List, Optional

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.role import Permission, Role
from app.models.user import User
from app.schemas.role import PermissionCreate, PermissionUpdate, RoleCreate, RoleUpdate


class RoleService:
    """角色管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_roles(self, skip: int = 0, limit: int = 100) -> List[Role]:
        """
        获取角色列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            角色列表
        """
        return self.db.query(Role).offset(skip).limit(limit).all()

    def get_role_by_id(self, role_id: int) -> Optional[Role]:
        """
        根据ID获取角色

        Args:
            role_id: 角色ID

        Returns:
            角色对象或None
        """
        return self.db.query(Role).filter(Role.id == role_id).first()

    def get_role_by_name(self, name: str) -> Optional[Role]:
        """
        根据名称获取角色

        Args:
            name: 角色名称

        Returns:
            角色对象或None
        """
        return self.db.query(Role).filter(Role.name == name).first()

    def create_role(self, role_data: RoleCreate) -> Role:
        """
        创建角色

        Args:
            role_data: 角色创建数据

        Returns:
            创建的角色对象

        Raises:
            HTTPException: 角色名称已存在时抛出异常
        """
        # 检查角色名称是否已存在
        if self.get_role_by_name(role_data.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="角色名称已存在"
            )

        # 创建角色
        db_role = Role(name=role_data.name, description=role_data.description)

        # 分配权限
        if role_data.permission_ids:
            permissions = (
                self.db.query(Permission)
                .filter(Permission.id.in_(role_data.permission_ids))
                .all()
            )
            db_role.permissions = permissions

        self.db.add(db_role)
        self.db.commit()
        self.db.refresh(db_role)

        return db_role

    def update_role(self, role_id: int, role_data: RoleUpdate) -> Role:
        """
        更新角色信息

        Args:
            role_id: 角色ID
            role_data: 角色更新数据

        Returns:
            更新后的角色对象

        Raises:
            HTTPException: 角色不存在或名称已被其他角色使用时抛出异常
        """
        role = self.get_role_by_id(role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

        # 检查角色名称是否被其他角色使用
        if role_data.name and role_data.name != role.name:
            existing_role = self.get_role_by_name(role_data.name)
            if existing_role and existing_role.id != role_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="角色名称已被其他角色使用"
                )

        # 更新角色基本信息
        if role_data.name is not None:
            role.name = role_data.name
        if role_data.description is not None:
            role.description = role_data.description

        # 更新权限
        if role_data.permission_ids is not None:
            permissions = (
                self.db.query(Permission)
                .filter(Permission.id.in_(role_data.permission_ids))
                .all()
            )
            role.permissions = permissions

        self.db.commit()
        self.db.refresh(role)

        return role

    def delete_role(self, role_id: int) -> bool:
        """
        删除角色

        Args:
            role_id: 角色ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 角色不存在或角色下有用户时抛出异常
        """
        role = self.get_role_by_id(role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

        # 检查是否有用户使用此角色
        if role.users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="角色下有用户，无法删除"
            )

        self.db.delete(role)
        self.db.commit()

        return True

    def assign_permissions_to_role(
        self, role_id: int, permission_ids: List[int]
    ) -> Role:
        """
        为角色分配权限

        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表

        Returns:
            更新后的角色对象

        Raises:
            HTTPException: 角色不存在时抛出异常
        """
        role = self.get_role_by_id(role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")

        # 获取权限
        permissions = (
            self.db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
        )
        role.permissions = permissions

        self.db.commit()
        self.db.refresh(role)

        return role


class PermissionService:
    """权限管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_permissions(self, skip: int = 0, limit: int = 100) -> List[Permission]:
        """
        获取权限列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            权限列表
        """
        return self.db.query(Permission).offset(skip).limit(limit).all()

    def get_permission_by_id(self, permission_id: int) -> Optional[Permission]:
        """
        根据ID获取权限

        Args:
            permission_id: 权限ID

        Returns:
            权限对象或None
        """
        return self.db.query(Permission).filter(Permission.id == permission_id).first()

    def get_permission_by_code(self, code: str) -> Optional[Permission]:
        """
        根据代码获取权限

        Args:
            code: 权限代码

        Returns:
            权限对象或None
        """
        return self.db.query(Permission).filter(Permission.code == code).first()

    def create_permission(self, permission_data: PermissionCreate) -> Permission:
        """
        创建权限

        Args:
            permission_data: 权限创建数据

        Returns:
            创建的权限对象

        Raises:
            HTTPException: 权限代码已存在时抛出异常
        """
        # 检查权限代码是否已存在
        if self.get_permission_by_code(permission_data.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="权限代码已存在"
            )

        # 创建权限
        db_permission = Permission(
            code=permission_data.code,
            name=permission_data.name,
            description=permission_data.description,
        )

        self.db.add(db_permission)
        self.db.commit()
        self.db.refresh(db_permission)

        return db_permission

    def update_permission(
        self, permission_id: int, permission_data: PermissionUpdate
    ) -> Permission:
        """
        更新权限信息

        Args:
            permission_id: 权限ID
            permission_data: 权限更新数据

        Returns:
            更新后的权限对象

        Raises:
            HTTPException: 权限不存在或代码已被其他权限使用时抛出异常
        """
        permission = self.get_permission_by_id(permission_id)
        if not permission:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在")

        # 检查权限代码是否被其他权限使用
        if permission_data.code and permission_data.code != permission.code:
            existing_permission = self.get_permission_by_code(permission_data.code)
            if existing_permission and existing_permission.id != permission_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="权限代码已被其他权限使用"
                )

        # 更新权限信息
        update_data = permission_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(permission, field, value)

        self.db.commit()
        self.db.refresh(permission)

        return permission

    def delete_permission(self, permission_id: int) -> bool:
        """
        删除权限

        Args:
            permission_id: 权限ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 权限不存在或权限被角色使用时抛出异常
        """
        permission = self.get_permission_by_id(permission_id)
        if not permission:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在")

        # 检查是否有角色使用此权限
        if permission.roles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="权限被角色使用，无法删除"
            )

        self.db.delete(permission)
        self.db.commit()

        return True
