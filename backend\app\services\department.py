"""
部门管理服务
"""
from typing import List, Optional

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.department import Department
from app.schemas.department import DepartmentCreate, DepartmentTree, DepartmentUpdate


class DepartmentService:
    """部门管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_departments(self, skip: int = 0, limit: int = 100) -> List[Department]:
        """
        获取部门列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            部门列表
        """
        return self.db.query(Department).offset(skip).limit(limit).all()

    def get_department_by_id(self, department_id: int) -> Optional[Department]:
        """
        根据ID获取部门

        Args:
            department_id: 部门ID

        Returns:
            部门对象或None
        """
        return self.db.query(Department).filter(Department.id == department_id).first()

    def get_department_by_name(self, name: str) -> Optional[Department]:
        """
        根据名称获取部门

        Args:
            name: 部门名称

        Returns:
            部门对象或None
        """
        return self.db.query(Department).filter(Department.name == name).first()

    def create_department(self, department_data: DepartmentCreate) -> Department:
        """
        创建部门

        Args:
            department_data: 部门创建数据

        Returns:
            创建的部门对象

        Raises:
            HTTPException: 部门名称已存在或父部门不存在时抛出异常
        """
        # 检查部门名称是否已存在
        if self.get_department_by_name(department_data.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="部门名称已存在"
            )

        # 如果指定了父部门，检查父部门是否存在
        if department_data.parent_id:
            parent_department = self.get_department_by_id(department_data.parent_id)
            if not parent_department:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="父部门不存在"
                )

        # 创建部门
        db_department = Department(
            name=department_data.name,
            description=department_data.description,
            parent_id=department_data.parent_id,
            sort_order=department_data.sort_order,
        )

        self.db.add(db_department)
        self.db.commit()
        self.db.refresh(db_department)

        return db_department

    def update_department(
        self, department_id: int, department_data: DepartmentUpdate
    ) -> Department:
        """
        更新部门信息

        Args:
            department_id: 部门ID
            department_data: 部门更新数据

        Returns:
            更新后的部门对象

        Raises:
            HTTPException: 部门不存在、名称已被其他部门使用或父部门不存在时抛出异常
        """
        department = self.get_department_by_id(department_id)
        if not department:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

        # 检查部门名称是否被其他部门使用
        if department_data.name and department_data.name != department.name:
            existing_department = self.get_department_by_name(department_data.name)
            if existing_department and existing_department.id != department_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="部门名称已被其他部门使用"
                )

        # 如果要更新父部门，检查父部门是否存在
        if department_data.parent_id is not None:
            if department_data.parent_id == department_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="部门不能设置自己为父部门"
                )

            if department_data.parent_id != 0:  # 0表示根部门
                parent_department = self.get_department_by_id(department_data.parent_id)
                if not parent_department:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND, detail="父部门不存在"
                    )

                # 检查是否会形成循环引用
                if self._would_create_cycle(department_id, department_data.parent_id):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不能设置子部门为父部门，这会形成循环引用",
                    )

        # 更新部门信息
        update_data = department_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(department, field, value)

        self.db.commit()
        self.db.refresh(department)

        return department

    def delete_department(self, department_id: int) -> bool:
        """
        删除部门

        Args:
            department_id: 部门ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 部门不存在或部门下有子部门/用户时抛出异常
        """
        department = self.get_department_by_id(department_id)
        if not department:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

        # 检查是否有子部门
        child_departments = (
            self.db.query(Department)
            .filter(Department.parent_id == department_id)
            .first()
        )
        if child_departments:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="部门下有子部门，无法删除"
            )

        # 检查是否有用户
        if department.users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="部门下有用户，无法删除"
            )

        self.db.delete(department)
        self.db.commit()

        return True

    def get_department_tree(self) -> List[DepartmentTree]:
        """
        获取部门树形结构

        Returns:
            部门树形结构列表
        """
        # 获取所有部门
        all_departments = (
            self.db.query(Department).order_by(Department.sort_order).all()
        )

        # 构建部门字典
        department_dict = {
            dept.id: DepartmentTree.model_validate(dept) for dept in all_departments
        }

        # 构建树形结构
        root_departments = []
        for dept in all_departments:
            dept_tree = department_dict[dept.id]
            if dept.parent_id and dept.parent_id in department_dict:
                # 添加到父部门的children中
                parent_dept = department_dict[dept.parent_id]
                parent_dept.children.append(dept_tree)
            else:
                # 根部门
                root_departments.append(dept_tree)

        return root_departments

    def get_department_children(self, department_id: int) -> List[Department]:
        """
        获取部门的直接子部门

        Args:
            department_id: 部门ID

        Returns:
            子部门列表
        """
        return (
            self.db.query(Department)
            .filter(Department.parent_id == department_id)
            .order_by(Department.sort_order)
            .all()
        )

    def get_department_descendants(self, department_id: int) -> List[Department]:
        """
        获取部门的所有后代部门

        Args:
            department_id: 部门ID

        Returns:
            所有后代部门列表
        """
        descendants = []
        children = self.get_department_children(department_id)

        for child in children:
            descendants.append(child)
            descendants.extend(self.get_department_descendants(child.id))

        return descendants

    def _would_create_cycle(self, department_id: int, new_parent_id: int) -> bool:
        """
        检查设置新的父部门是否会创建循环引用

        Args:
            department_id: 当前部门ID
            new_parent_id: 新的父部门ID

        Returns:
            如果会创建循环引用返回True，否则返回False
        """
        # 获取所有后代部门
        descendants = self.get_department_descendants(department_id)
        descendant_ids = [dept.id for dept in descendants]

        # 如果新的父部门是当前部门的后代，则会形成循环
        return new_parent_id in descendant_ids
