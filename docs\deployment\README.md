# 部署指南

本文档详细介绍了应用项目管理系统的部署方法，包括开发环境、测试环境和生产环境的部署。

## 目录

- [环境要求](#环境要求)
- [Docker 部署](#docker-部署)
- [手动部署](#手动部署)
- [生产环境部署](#生产环境部署)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 环境要求

### 最低要求

- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置

- **CPU**: 4 核心或更多
- **内存**: 8GB RAM 或更多
- **存储**: 50GB SSD
- **网络**: 高速网络连接

### 软件依赖

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Python**: 3.10+ (手动部署)
- **Node.js**: 18+ (手动部署)
- **PostgreSQL**: 12+ (手动部署)
- **Redis**: 6+ (手动部署)
- **Nginx**: 1.20+ (生产环境)

## Docker 部署

### 快速开始

1. **克隆项目**
```bash
git clone https://github.com/your-username/app-project-manager.git
cd app-project-manager
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

3. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

4. **初始化数据库**
```bash
# 运行数据库迁移
docker-compose exec backend alembic upgrade head

# 创建超级用户
docker-compose exec backend python scripts/create_superuser.py
```

### Docker Compose 配置

```yaml
version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: app_manager
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/app_manager
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend

volumes:
  postgres_data:
  redis_data:
```

### 环境变量配置

```bash
# 数据库配置
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=********************************************************/app_manager

# Redis 配置
REDIS_URL=redis://redis:6379/0

# JWT 配置
SECRET_KEY=your_very_secure_secret_key_at_least_32_characters_long
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
ENVIRONMENT=production
DEBUG=false
PROJECT_NAME=应用项目管理系统

# CORS 配置
BACKEND_CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
```

## 手动部署

### 后端部署

1. **安装 Python 依赖**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **配置数据库**
```bash
# 创建数据库
createdb app_manager

# 运行迁移
alembic upgrade head
```

3. **启动后端服务**
```bash
# 开发环境
python main.py

# 生产环境
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
```

### 前端部署

1. **安装 Node.js 依赖**
```bash
cd frontend
npm install
```

2. **构建生产版本**
```bash
npm run build
```

3. **部署静态文件**
```bash
# 复制构建文件到 Web 服务器
cp -r dist/* /var/www/html/
```

## 生产环境部署

### Nginx 配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket
    location /ws {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### SSL 证书配置

```bash
# 使用 Let's Encrypt
certbot --nginx -d yourdomain.com

# 或者使用自签名证书（仅用于测试）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/key.pem \
    -out /etc/nginx/ssl/cert.pem
```

### 系统服务配置

创建 systemd 服务文件：

```ini
# /etc/systemd/system/app-manager-backend.service
[Unit]
Description=App Manager Backend
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/app-manager/backend
Environment=PATH=/opt/app-manager/backend/venv/bin
ExecStart=/opt/app-manager/backend/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable app-manager-backend
sudo systemctl start app-manager-backend
sudo systemctl status app-manager-backend
```

### 数据库优化

```sql
-- PostgreSQL 性能优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重启 PostgreSQL 使配置生效
SELECT pg_reload_conf();
```

### 备份策略

```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="app_manager"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# 删除 7 天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

# 应用文件备份
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /opt/app-manager

echo "Backup completed: $DATE"
```

设置定时备份：
```bash
# 添加到 crontab
0 2 * * * /opt/scripts/backup.sh
```

## 监控和维护

### 健康检查

```bash
#!/bin/bash
# health_check.sh

# 检查后端服务
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "Backend: OK"
else
    echo "Backend: FAILED"
    systemctl restart app-manager-backend
fi

# 检查数据库连接
if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "Database: OK"
else
    echo "Database: FAILED"
fi

# 检查 Redis
if redis-cli ping > /dev/null 2>&1; then
    echo "Redis: OK"
else
    echo "Redis: FAILED"
fi
```

### 日志管理

```bash
# 配置 logrotate
# /etc/logrotate.d/app-manager
/opt/app-manager/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload app-manager-backend
    endscript
}
```

### 性能监控

使用 Prometheus + Grafana 进行监控：

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'app-manager'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

## 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库状态
sudo systemctl status postgresql
sudo -u postgres psql -c "SELECT version();"

# 检查连接配置
grep -r "DATABASE_URL" /opt/app-manager/
```

2. **Redis 连接失败**
```bash
# 检查 Redis 状态
sudo systemctl status redis
redis-cli ping

# 检查配置
grep -r "REDIS_URL" /opt/app-manager/
```

3. **前端无法访问后端**
```bash
# 检查网络连接
curl -I http://localhost:8000/health

# 检查 Nginx 配置
nginx -t
sudo systemctl reload nginx
```

4. **SSL 证书问题**
```bash
# 检查证书有效期
openssl x509 -in /etc/nginx/ssl/cert.pem -text -noout

# 更新 Let's Encrypt 证书
certbot renew --dry-run
```

### 日志查看

```bash
# 应用日志
tail -f /opt/app-manager/backend/logs/app.log

# 系统日志
journalctl -u app-manager-backend -f

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 数据库日志
tail -f /var/log/postgresql/postgresql-15-main.log
```

### 性能调优

```bash
# 检查系统资源使用
htop
iotop
netstat -tulpn

# 数据库性能分析
sudo -u postgres psql app_manager -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

## 安全建议

1. **定期更新系统和依赖**
2. **使用强密码和密钥**
3. **启用防火墙**
4. **定期备份数据**
5. **监控异常访问**
6. **使用 HTTPS**
7. **限制数据库访问**
8. **定期安全审计**

更多详细信息请参考 [安全指南](security.md)。
