"""
应用管理API端点
"""
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_active_user, require_permissions
from app.models.user import User as UserModel
from app.schemas.app import (
    App,
    AppConfig,
    AppConfigCreate,
    AppConfigUpdate,
    AppCreate,
    AppStats,
    AppUpdate,
    AppWithServices,
)
from app.services.app import AppConfigService, AppManagementService

router = APIRouter()


@router.get("/", response_model=List[App], summary="获取应用列表")
async def get_apps(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    my_apps: bool = Query(False, description="是否只显示我创建的应用"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    获取应用列表

    需要权限: app:read
    """
    app_service = AppManagementService(db)
    user_id = current_user.id if my_apps else None
    apps = app_service.get_apps(skip=skip, limit=limit, user_id=user_id)
    return apps


@router.get("/stats", response_model=AppStats, summary="获取应用统计信息")
async def get_app_stats(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    获取应用统计信息

    需要权限: app:read
    """
    app_service = AppManagementService(db)
    return app_service.get_app_stats()


@router.get("/{app_id}", response_model=Dict[str, Any], summary="获取应用详情")
async def get_app(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    根据ID获取应用详情（包含服务状态）

    需要权限: app:read
    """
    app_service = AppManagementService(db)
    return app_service.get_app_with_services(app_id)


@router.post("/", response_model=App, summary="创建应用")
async def create_app(
    app_data: AppCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:create")),
):
    """
    创建新应用

    需要权限: app:create

    - **name**: 应用名称（必填，唯一）
    - **description**: 应用描述（可选）
    - **frontend_dir**: 前端目录路径（可选）
    - **backend_dir**: 后端目录路径（可选）
    - **frontend_start_cmd**: 前端启动命令（可选）
    - **backend_start_cmd**: 后端启动命令（可选）
    - **frontend_stop_cmd**: 前端停止命令（可选）
    - **backend_stop_cmd**: 后端停止命令（可选）
    - **frontend_port**: 前端端口（可选）
    - **backend_port**: 后端端口（可选）
    - **is_active**: 是否激活（默认为True）
    """
    app_service = AppManagementService(db)
    return app_service.create_app(app_data, current_user.id)


@router.put("/{app_id}", response_model=App, summary="更新应用")
async def update_app(
    app_id: int,
    app_data: AppUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:update")),
):
    """
    更新应用信息

    需要权限: app:update
    """
    app_service = AppManagementService(db)
    return app_service.update_app(app_id, app_data, current_user.id)


@router.delete("/{app_id}", summary="删除应用")
async def delete_app(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:delete")),
):
    """
    删除应用

    需要权限: app:delete

    注意：只能删除没有正在运行服务的应用
    """
    app_service = AppManagementService(db)
    app_service.delete_app(app_id, current_user.id)
    return {"message": "应用删除成功"}


# 应用配置管理端点
@router.get("/{app_id}/configs", response_model=List[AppConfig], summary="获取应用配置")
async def get_app_configs(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    获取应用配置列表

    需要权限: app:read
    """
    # 先检查应用是否存在
    app_service = AppManagementService(db)
    app = app_service.get_app_by_id(app_id)
    if not app:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

    config_service = AppConfigService(db)
    return config_service.get_app_configs(app_id)


@router.post("/{app_id}/configs", response_model=AppConfig, summary="创建应用配置")
async def create_app_config(
    app_id: int,
    config_data: AppConfigCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:update")),
):
    """
    创建应用配置

    需要权限: app:update
    """
    # 先检查应用是否存在
    app_service = AppManagementService(db)
    app = app_service.get_app_by_id(app_id)
    if not app:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

    config_service = AppConfigService(db)
    return config_service.create_config(app_id, config_data, current_user.id)


@router.put("/{app_id}/configs/{config_id}", response_model=AppConfig, summary="更新应用配置")
async def update_app_config(
    app_id: int,
    config_id: int,
    config_data: AppConfigUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:update")),
):
    """
    更新应用配置

    需要权限: app:update
    """
    config_service = AppConfigService(db)
    return config_service.update_config(config_id, config_data, current_user.id)


@router.delete("/{app_id}/configs/{config_id}", summary="删除应用配置")
async def delete_app_config(
    app_id: int,
    config_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:update")),
):
    """
    删除应用配置

    需要权限: app:update
    """
    config_service = AppConfigService(db)
    config_service.delete_config(config_id, current_user.id)
    return {"message": "配置删除成功"}


from app.schemas.app import ServiceControlRequest

# 服务控制端点
from app.services.service_control import ServiceControlService


@router.post("/{app_id}/services/control", summary="控制应用服务")
async def control_service(
    app_id: int,
    request: ServiceControlRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:control")),
):
    """
    控制应用服务（启动/停止/重启）

    需要权限: app:control

    - **action**: 操作类型（start/stop/restart/status）
    - **service_type**: 服务类型（frontend/backend）
    """
    service_control = ServiceControlService(db)
    return service_control.control_service(app_id, request)


@router.get("/{app_id}/services/{service_type}/status", summary="获取服务状态")
async def get_service_status(
    app_id: int,
    service_type: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    获取服务状态

    需要权限: app:read
    """
    service_control = ServiceControlService(db)
    return service_control.get_service_status(app_id, service_type)


@router.post("/{app_id}/services/{service_type}/start", summary="启动服务")
async def start_service(
    app_id: int,
    service_type: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:control")),
):
    """
    启动指定服务

    需要权限: app:control
    """
    service_control = ServiceControlService(db)
    return service_control.start_service(app_id, service_type)


@router.post("/{app_id}/services/{service_type}/stop", summary="停止服务")
async def stop_service(
    app_id: int,
    service_type: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:control")),
):
    """
    停止指定服务

    需要权限: app:control
    """
    service_control = ServiceControlService(db)
    return service_control.stop_service(app_id, service_type)


@router.post("/{app_id}/services/{service_type}/restart", summary="重启服务")
async def restart_service(
    app_id: int,
    service_type: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:control")),
):
    """
    重启指定服务

    需要权限: app:control
    """
    service_control = ServiceControlService(db)
    return service_control.restart_service(app_id, service_type)


router.get("/{app_id}/configs/history", summary="获取应用配置历史")


async def get_app_config_history(
    app_id: int,
    config_id: int = Query(None, description="配置ID，不指定则获取所有配置的历史"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:read")),
):
    """
    获取应用配置变更历史

    需要权限: app:read
    """
    # 先检查应用是否存在
    app_service = AppManagementService(db)
    app = app_service.get_app_by_id(app_id)
    if not app:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

    config_service = AppConfigService(db)
    return config_service.get_config_history(app_id, config_id)


@router.post(
    "/{app_id}/configs/{config_id}/rollback/{history_id}",
    response_model=AppConfig,
    summary="回滚配置",
)
async def rollback_app_config(
    app_id: int,
    config_id: int,
    history_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("app:update")),
):
    """
    回滚配置到指定历史版本

    需要权限: app:update
    """
    config_service = AppConfigService(db)
    return config_service.rollback_config(config_id, history_id, current_user.id)
