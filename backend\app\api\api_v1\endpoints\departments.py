"""
部门管理API端点
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_active_user, require_permissions
from app.models.user import User as UserModel
from app.schemas.department import (
    Department,
    DepartmentCreate,
    DepartmentTree,
    DepartmentUpdate,
)
from app.services.department import DepartmentService

router = APIRouter()


@router.get("/", response_model=List[Department], summary="获取部门列表")
async def get_departments(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:read")),
):
    """
    获取部门列表

    需要权限: department:read
    """
    department_service = DepartmentService(db)
    departments = department_service.get_departments(skip=skip, limit=limit)
    return departments


@router.get("/tree", response_model=List[DepartmentTree], summary="获取部门树形结构")
async def get_department_tree(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:read")),
):
    """
    获取部门树形结构

    需要权限: department:read
    """
    department_service = DepartmentService(db)
    return department_service.get_department_tree()


@router.get("/{department_id}", response_model=Department, summary="获取部门详情")
async def get_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:read")),
):
    """
    根据ID获取部门详情

    需要权限: department:read
    """
    department_service = DepartmentService(db)
    department = department_service.get_department_by_id(department_id)
    if not department:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")
    return department


@router.post("/", response_model=Department, summary="创建部门")
async def create_department(
    department_data: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:create")),
):
    """
    创建新部门

    需要权限: department:create

    - **name**: 部门名称（必填，唯一）
    - **description**: 部门描述（可选）
    - **parent_id**: 父部门ID（可选，不填表示根部门）
    - **sort_order**: 排序（默认为0）
    """
    department_service = DepartmentService(db)
    return department_service.create_department(department_data)


@router.put("/{department_id}", response_model=Department, summary="更新部门")
async def update_department(
    department_id: int,
    department_data: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:update")),
):
    """
    更新部门信息

    需要权限: department:update
    """
    department_service = DepartmentService(db)
    return department_service.update_department(department_id, department_data)


@router.delete("/{department_id}", summary="删除部门")
async def delete_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:delete")),
):
    """
    删除部门

    需要权限: department:delete

    注意：只能删除没有子部门和用户的部门
    """
    department_service = DepartmentService(db)
    department_service.delete_department(department_id)
    return {"message": "部门删除成功"}


@router.get(
    "/{department_id}/children", response_model=List[Department], summary="获取子部门"
)
async def get_department_children(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:read")),
):
    """
    获取部门的直接子部门

    需要权限: department:read
    """
    department_service = DepartmentService(db)
    # 先检查部门是否存在
    department = department_service.get_department_by_id(department_id)
    if not department:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

    return department_service.get_department_children(department_id)


@router.get(
    "/{department_id}/descendants", response_model=List[Department], summary="获取所有后代部门"
)
async def get_department_descendants(
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("department:read")),
):
    """
    获取部门的所有后代部门

    需要权限: department:read
    """
    department_service = DepartmentService(db)
    # 先检查部门是否存在
    department = department_service.get_department_by_id(department_id)
    if not department:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

    return department_service.get_department_descendants(department_id)
