"""
应用模型
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class App(Base):
    """应用模型"""

    __tablename__ = "apps"

    id = Column(Integer, primary_key=True, index=True, comment="应用ID")
    name = Column(String(100), nullable=False, comment="应用名称")
    description = Column(Text, nullable=True, comment="应用描述")
    frontend_dir = Column(String(500), nullable=True, comment="前端目录路径")
    backend_dir = Column(String(500), nullable=True, comment="后端目录路径")
    frontend_start_cmd = Column(String(500), nullable=True, comment="前端启动命令")
    backend_start_cmd = Column(String(500), nullable=True, comment="后端启动命令")
    frontend_stop_cmd = Column(String(500), nullable=True, comment="前端停止命令")
    backend_stop_cmd = Column(String(500), nullable=True, comment="后端停止命令")
    frontend_port = Column(Integer, nullable=True, comment="前端端口")
    backend_port = Column(Integer, nullable=True, comment="后端端口")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_by = Column(
        Integer, ForeignKey("users.id"), nullable=False, comment="创建者ID"
    )
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    creator = relationship("User", back_populates="created_apps")
    configs = relationship(
        "AppConfig", back_populates="app", cascade="all, delete-orphan"
    )
    logs = relationship("AppLog", back_populates="app", cascade="all, delete-orphan")
    services = relationship(
        "AppService", back_populates="app", cascade="all, delete-orphan"
    )
    log_alerts = relationship(
        "LogAlert", back_populates="app", cascade="all, delete-orphan"
    )
    alert_records = relationship(
        "LogAlertRecord", back_populates="app", cascade="all, delete-orphan"
    )
    tasks = relationship("Task", back_populates="app", cascade="all, delete-orphan")


class AppConfig(Base):
    """应用配置模型"""

    __tablename__ = "app_configs"

    id = Column(Integer, primary_key=True, index=True, comment="配置ID")
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    key = Column(String(100), nullable=False, comment="配置键")
    value = Column(Text, nullable=True, comment="配置值")
    description = Column(String(200), nullable=True, comment="配置描述")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    app = relationship("App", back_populates="configs")


class AppLog(Base):
    """应用日志模型"""

    __tablename__ = "app_logs"

    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    service_type = Column(String(20), nullable=False, comment="服务类型(frontend/backend)")
    level = Column(String(20), nullable=False, comment="日志级别")
    message = Column(Text, nullable=False, comment="日志消息")
    timestamp = Column(
        DateTime(timezone=True), server_default=func.now(), comment="时间戳"
    )

    # 关系
    app = relationship("App", back_populates="logs")


class AppService(Base):
    """应用服务状态模型"""

    __tablename__ = "app_services"

    id = Column(Integer, primary_key=True, index=True, comment="服务ID")
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    service_type = Column(String(20), nullable=False, comment="服务类型(frontend/backend)")
    status = Column(
        String(20), default="stopped", comment="服务状态(running/stopped/error)"
    )
    pid = Column(Integer, nullable=True, comment="进程ID")
    port = Column(Integer, nullable=True, comment="端口号")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="启动时间")
    stopped_at = Column(DateTime(timezone=True), nullable=True, comment="停止时间")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    app = relationship("App", back_populates="services")
