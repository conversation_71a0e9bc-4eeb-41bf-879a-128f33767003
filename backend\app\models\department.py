"""
部门模型
"""
from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Department(Base):
    """部门模型"""

    __tablename__ = "departments"

    id = Column(Integer, primary_key=True, index=True, comment="部门ID")
    name = Column(String(100), nullable=False, comment="部门名称")
    description = Column(String(200), nullable=True, comment="部门描述")
    parent_id = Column(
        Integer, ForeignKey("departments.id"), nullable=True, comment="父部门ID"
    )
    sort_order = Column(Integer, default=0, comment="排序")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    parent = relationship("Department", remote_side=[id], back_populates="children")
    children = relationship("Department", back_populates="parent")
    users = relationship(
        "User", secondary="user_departments", back_populates="departments"
    )
