"""
认证相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import verify_token
from app.models.user import User as UserModel
from app.schemas.auth import PasswordChange, Token, User, UserLogin, UserProfile
from app.services.auth import AuthService

router = APIRouter()
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
) -> UserModel:
    """获取当前用户依赖"""
    token = credentials.credentials
    try:
        username = verify_token(token)
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

    auth_service = AuthService(db)
    user = auth_service.get_current_user(username)
    return user


def get_current_active_user(
    current_user: UserModel = Depends(get_current_user),
) -> UserModel:
    """获取当前活跃用户依赖"""
    if not current_user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="用户账户已被禁用")
    return current_user


@router.post("/login", response_model=Token, summary="用户登录")
async def login(login_data: UserLogin, db: Session = Depends(get_db)):
    """
    用户登录接口

    - **username**: 用户名
    - **password**: 密码
    """
    auth_service = AuthService(db)
    return auth_service.login(login_data)


@router.post("/logout", summary="用户登出")
async def logout(current_user: UserModel = Depends(get_current_active_user)):
    """
    用户登出接口

    注意：由于使用JWT，实际的登出需要在客户端删除token
    """
    return {"message": "登出成功"}


@router.get("/me", response_model=User, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: UserModel = Depends(get_current_active_user),
):
    """
    获取当前用户信息
    """
    return current_user


@router.get("/profile", response_model=UserProfile, summary="获取用户详细信息")
async def get_user_profile(
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    获取用户详细信息，包括角色和权限
    """
    # 获取用户角色
    roles = [role.name for role in current_user.roles] if current_user.roles else []

    # 获取用户权限
    permissions = []
    if current_user.roles:
        for role in current_user.roles:
            if role.permissions:
                permissions.extend([perm.code for perm in role.permissions])

    # 获取用户部门
    department = None
    if current_user.departments:
        department = current_user.departments[0].name

    return UserProfile(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        is_superuser=current_user.is_superuser,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        roles=roles,
        permissions=list(set(permissions)),  # 去重
        department=department,
    )


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: PasswordChange,
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    修改用户密码

    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    auth_service = AuthService(db)
    auth_service.change_password(
        current_user, password_data.old_password, password_data.new_password
    )
    return {"message": "密码修改成功"}


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
):
    """
    刷新访问令牌
    """
    token = credentials.credentials
    try:
        payload = verify_token(token)
        username: str = payload.get("sub")
        token_type: str = payload.get("type")

        if username is None or token_type != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    auth_service = AuthService(db)
    user = auth_service.get_current_user(username)

    if not user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="用户账户已被禁用")

    # 创建新的访问令牌
    from datetime import timedelta

    from app.core.config import settings
    from app.core.security import create_access_token, create_refresh_token

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    refresh_token = create_refresh_token(data={"sub": user.username})

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )
