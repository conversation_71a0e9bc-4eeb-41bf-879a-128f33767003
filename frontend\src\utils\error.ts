/**
 * 错误处理工具
 */
import { ElMessage, ElNotification } from 'element-plus'
import type { ApiError } from '@/types/api'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  SERVER = 'SERVER',
  BUSINESS = 'BUSINESS',
  UNKNOWN = 'UNKNOWN'
}

// 自定义错误类
export class AppError extends Error {
  public readonly type: ErrorType
  public readonly code?: string
  public readonly details?: Record<string, any>

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    details?: Record<string, any>
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.code = code
    this.details = details
  }
}

// 错误处理器接口
export interface ErrorHandler {
  handle(error: Error | AppError | ApiError): void
}

// 默认错误处理器
export class DefaultErrorHandler implements ErrorHandler {
  handle(error: Error | AppError | ApiError): void {
    console.error('Error occurred:', error)

    if (error instanceof AppError) {
      this.handleAppError(error)
    } else if (this.isApiError(error)) {
      this.handleApiError(error as ApiError)
    } else {
      this.handleGenericError(error)
    }
  }

  private handleAppError(error: AppError): void {
    switch (error.type) {
      case ErrorType.VALIDATION:
        ElMessage.warning(error.message)
        break
      case ErrorType.AUTHENTICATION:
        ElMessage.error(error.message)
        // 可以在这里处理登录跳转
        break
      case ErrorType.AUTHORIZATION:
        ElMessage.error(error.message)
        break
      case ErrorType.NETWORK:
        ElNotification.error({
          title: '网络错误',
          message: error.message,
          duration: 5000
        })
        break
      case ErrorType.SERVER:
        ElNotification.error({
          title: '服务器错误',
          message: error.message,
          duration: 5000
        })
        break
      case ErrorType.BUSINESS:
        ElMessage.error(error.message)
        break
      default:
        ElMessage.error(error.message)
    }
  }

  private handleApiError(error: ApiError): void {
    ElMessage.error(error.message || '请求失败')
    
    if (error.details) {
      console.error('API Error details:', error.details)
    }
  }

  private handleGenericError(error: Error): void {
    ElMessage.error('发生未知错误')
    console.error('Generic error:', error)
  }

  private isApiError(error: any): error is ApiError {
    return error && typeof error.message === 'string'
  }
}

// 全局错误处理器实例
export const errorHandler = new DefaultErrorHandler()

// 错误处理装饰器
export function handleError(handler?: ErrorHandler) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (error) {
        const errorHandlerInstance = handler || errorHandler
        errorHandlerInstance.handle(error as Error)
        throw error
      }
    }

    return descriptor
  }
}

// 异步错误处理包装器
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  handler?: ErrorHandler
): Promise<T | null> {
  try {
    return await operation()
  } catch (error) {
    const errorHandlerInstance = handler || errorHandler
    errorHandlerInstance.handle(error as Error)
    return null
  }
}

// 错误边界组合式函数
export function useErrorBoundary() {
  const handleError = (error: Error | AppError | ApiError) => {
    errorHandler.handle(error)
  }

  const wrapAsync = <T extends (...args: any[]) => Promise<any>>(fn: T): T => {
    return (async (...args: any[]) => {
      try {
        return await fn(...args)
      } catch (error) {
        handleError(error as Error)
        throw error
      }
    }) as T
  }

  return {
    handleError,
    wrapAsync
  }
}

// 表单验证错误处理
export function handleValidationErrors(errors: Record<string, string[]>) {
  const messages = Object.entries(errors)
    .map(([field, fieldErrors]) => `${field}: ${fieldErrors.join(', ')}`)
    .join('\n')
  
  ElMessage.error({
    message: `验证失败:\n${messages}`,
    duration: 5000,
    showClose: true
  })
}

// HTTP状态码错误映射
export function mapHttpStatusToErrorType(status: number): ErrorType {
  switch (status) {
    case 400:
      return ErrorType.VALIDATION
    case 401:
      return ErrorType.AUTHENTICATION
    case 403:
      return ErrorType.AUTHORIZATION
    case 404:
      return ErrorType.BUSINESS
    case 422:
      return ErrorType.VALIDATION
    case 500:
    case 502:
    case 503:
    case 504:
      return ErrorType.SERVER
    default:
      return ErrorType.UNKNOWN
  }
}
