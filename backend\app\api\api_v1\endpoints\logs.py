"""
日志管理API端点
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_user
from app.models.user import User
from app.schemas.log import (
    Log,
    LogCreate,
    LogDeleteRequest,
    LogExportRequest,
    LogQuery,
    LogQueryResponse,
    LogStats,
)
from app.services.log import log_collection_service, log_query_service
from app.services.log_monitor import log_monitor_service

router = APIRouter()


@router.get("/", response_model=LogQueryResponse)
def query_logs(
    app_id: Optional[int] = Query(None, description="应用ID"),
    service_type: Optional[str] = Query(None, description="服务类型"),
    level: Optional[str] = Query(None, description="日志级别"),
    keyword: Optional[str] = Query(None, description="关键词"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """查询日志"""
    try:
        # 解析时间参数
        from datetime import datetime

        parsed_start_time = None
        parsed_end_time = None

        if start_time:
            try:
                parsed_start_time = datetime.fromisoformat(
                    start_time.replace("Z", "+00:00")
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_time format")

        if end_time:
            try:
                parsed_end_time = datetime.fromisoformat(
                    end_time.replace("Z", "+00:00")
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_time format")

        # 构建查询对象
        query = LogQuery(
            app_id=app_id,
            service_type=service_type,
            level=level,
            keyword=keyword,
            start_time=parsed_start_time,
            end_time=parsed_end_time,
            skip=skip,
            limit=limit,
        )

        # 查询日志
        logs = log_query_service.query_logs(db, query)
        total = log_query_service.count_logs(db, query)

        return LogQueryResponse(logs=logs, total=total, skip=skip, limit=limit)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to query logs: {str(e)}")


@router.get("/{log_id}", response_model=Log)
def get_log(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单条日志"""
    try:
        log = log_query_service.get_log_by_id(db, log_id)
        if not log:
            raise HTTPException(status_code=404, detail="Log not found")

        return log

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get log: {str(e)}")


@router.get("/levels/list")
def get_log_levels(
    app_id: Optional[int] = Query(None, description="应用ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取日志级别列表"""
    try:
        levels = log_query_service.get_log_levels(db, app_id)
        return {"levels": levels}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get log levels: {str(e)}"
        )


@router.post("/export")
def export_logs(
    export_request: LogExportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """导出日志"""
    try:
        # 验证导出格式
        supported_formats = ["csv", "json", "txt"]
        if export_request.format.lower() not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported format. Supported formats: {', '.join(supported_formats)}",
            )

        # 导出日志
        content = log_query_service.export_logs(
            db, export_request.query, export_request.format
        )

        # 设置响应头
        format_lower = export_request.format.lower()
        if format_lower == "csv":
            media_type = "text/csv"
            filename = "logs.csv"
        elif format_lower == "json":
            media_type = "application/json"
            filename = "logs.json"
        else:  # txt
            media_type = "text/plain"
            filename = "logs.txt"

        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export logs: {str(e)}")


@router.delete("/")
def delete_logs(
    delete_request: LogDeleteRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """删除日志"""
    try:
        deleted_count = log_query_service.delete_logs(db, delete_request.query)
        return {"deleted_count": deleted_count}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete logs: {str(e)}")


@router.get("/stats/{app_id}", response_model=LogStats)
def get_log_stats(
    app_id: int,
    hours: int = Query(24, ge=1, le=168, description="统计时间范围(小时)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取日志统计信息"""
    try:
        stats = log_collection_service.get_log_stats(db, app_id, hours)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get log stats: {str(e)}"
        )


# 日志收集相关端点
@router.post("/collect", response_model=Log)
def collect_log(
    log_data: LogCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """收集单条日志"""
    try:
        log = log_collection_service.collect_log(db, log_data)
        return log

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to collect log: {str(e)}")


@router.post("/collect/batch")
def collect_logs_batch(
    logs_data: List[LogCreate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """批量收集日志"""
    try:
        logs = log_collection_service.collect_logs_batch(db, logs_data)
        return {"collected_count": len(logs), "logs": logs}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to collect logs batch: {str(e)}"
        )


@router.post("/cleanup")
def cleanup_old_logs(
    days: int = Query(30, ge=1, le=365, description="清理多少天前的日志"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """清理旧日志"""
    try:
        log_collection_service.cleanup_old_logs(db, days)
        return {"message": f"Successfully cleaned up logs older than {days} days"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cleanup logs: {str(e)}")


# 日志监控相关端点
@router.post("/monitor/start/{app_id}")
def start_log_monitoring(
    app_id: int,
    service_type: str = Query(..., description="服务类型"),
    log_file_path: Optional[str] = Query(None, description="日志文件路径"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """开始监控应用日志"""
    try:
        log_monitor_service.start_app_monitoring(app_id, service_type, log_file_path)
        return {
            "message": f"Started monitoring logs for app {app_id} service {service_type}"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start log monitoring: {str(e)}"
        )


@router.post("/monitor/stop/{app_id}")
def stop_log_monitoring(
    app_id: int,
    service_type: str = Query(..., description="服务类型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """停止监控应用日志"""
    try:
        log_monitor_service.stop_app_monitoring(app_id, service_type)
        return {
            "message": f"Stopped monitoring logs for app {app_id} service {service_type}"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop log monitoring: {str(e)}"
        )


@router.get("/monitor/status/{app_id}")
def get_monitoring_status(
    app_id: int,
    service_type: str = Query(..., description="服务类型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取日志监控状态"""
    try:
        is_monitoring = log_monitor_service.get_monitoring_status(app_id, service_type)
        return {
            "app_id": app_id,
            "service_type": service_type,
            "is_monitoring": is_monitoring,
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get monitoring status: {str(e)}"
        )
