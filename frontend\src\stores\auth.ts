/**
 * 认证状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm } from '@/types/auth'
import { login as apiLogin, logout as apiLogout, getUserInfo } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const user = ref<User | null>(null)
  const loading = ref(false)
  const permissions = ref<string[]>([])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 登录
  const login = async (loginForm: LoginForm): Promise<boolean> => {
    try {
      loading.value = true
      const response = await apiLogin(loginForm)

      if (!response?.access_token) {
        throw new Error('登录响应格式错误')
      }

      token.value = response.access_token
      setToken(response.access_token)

      // 获取用户信息
      await fetchUserInfo()

      return true
    } catch (error) {
      console.error('登录失败:', error)
      // 清理可能的部分状态
      token.value = ''
      user.value = null
      removeToken()
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await apiLogout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      token.value = ''
      user.value = null
      permissions.value = []
      removeToken()
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await getUserInfo()
      user.value = userInfo
      // 如果API返回了权限信息，更新权限列表
      if (userInfo.permissions) {
        permissions.value = userInfo.permissions
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除token
      token.value = ''
      removeToken()
      throw error
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission) || user.value?.is_superuser || false
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return user.value?.roles?.includes(role) || false
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        await fetchUserInfo()
      } catch (error) {
        // 如果token无效，清除认证状态
        await logout()
      }
    }
  }

  return {
    token,
    user,
    loading,
    permissions,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    hasPermission,
    hasRole,
    initAuth
  }
})