"""
服务控制服务单元测试
"""
from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.models.app import App, AppService
from app.schemas.app import ServiceControlRequest
from app.services.service_control import ServiceControlService


class TestServiceControlService:
    """服务控制服务测试类"""

    def test_get_app_service(self, db_session: Session):
        """测试获取应用服务记录"""
        service = ServiceControlService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        app_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(app_service)
        db_session.commit()

        # 测试获取存在的服务
        result = service.get_app_service(app.id, "frontend")
        assert result is not None
        assert result.service_type == "frontend"
        assert result.status == "running"
        assert result.pid == 1234

        # 测试获取不存在的服务
        result = service.get_app_service(app.id, "backend")
        assert result is None

    def test_create_or_update_service_create(self, db_session: Session):
        """测试创建新服务记录"""
        service = ServiceControlService(db_session)

        # 创建测试应用
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        result = service.create_or_update_service(
            app_id=app.id,
            service_type="frontend",
            status="running",
            pid=1234,
            port=3000,
        )

        assert result.app_id == app.id
        assert result.service_type == "frontend"
        assert result.status == "running"
        assert result.pid == 1234
        assert result.port == 3000

    def test_create_or_update_service_update(self, db_session: Session):
        """测试更新现有服务记录"""
        service = ServiceControlService(db_session)

        # 创建测试数据
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        existing_service = AppService(
            app_id=app.id, service_type="frontend", status="stopped"
        )
        db_session.add(existing_service)
        db_session.commit()

        result = service.create_or_update_service(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )

        assert result.id == existing_service.id
        assert result.status == "running"
        assert result.pid == 1234

    @patch("app.services.log.log_collection_service.collect_log")
    def test_log_service_event(self, mock_collect_log, db_session: Session):
        """测试记录服务事件日志"""
        service = ServiceControlService(db_session)

        service.log_service_event(1, "frontend", "INFO", "Service started")

        mock_collect_log.assert_called_once()
        call_args = mock_collect_log.call_args
        assert call_args[0][0] == db_session  # db参数
        log_data = call_args[0][1]  # log_data参数
        assert log_data.app_id == 1
        assert log_data.service_type == "frontend"
        assert log_data.level == "INFO"
        assert log_data.message == "Service started"

    @patch("subprocess.Popen")
    @patch("os.path.exists")
    @patch("time.sleep")
    @patch("app.services.log_monitor.log_monitor_service.start_app_monitoring")
    def test_start_service_success(
        self,
        mock_start_monitoring,
        mock_sleep,
        mock_exists,
        mock_popen,
        db_session: Session,
    ):
        """测试成功启动服务"""
        # 设置模拟
        mock_exists.return_value = True
        mock_process = Mock()
        mock_process.pid = 1234
        mock_process.poll.return_value = None  # 进程正在运行
        mock_popen.return_value = mock_process

        service = ServiceControlService(db_session)

        # 创建测试应用
        app = App(
            name="Test App",
            frontend_dir="/path/to/frontend",
            frontend_start_cmd="npm start",
            frontend_port=3000,
            created_by=1,
        )
        db_session.add(app)
        db_session.commit()

        result = service.start_service(app.id, "frontend")

        assert result["status"] == "success"
        assert result["pid"] == 1234
        assert result["port"] == 3000
        assert "启动成功" in result["message"]

        # 验证服务记录已创建
        app_service = service.get_app_service(app.id, "frontend")
        assert app_service.status == "running"
        assert app_service.pid == 1234
        assert app_service.port == 3000

        # 验证监控已启动
        mock_start_monitoring.assert_called_once_with(app.id, "frontend")

    def test_start_service_app_not_found(self, db_session: Session):
        """测试启动不存在应用的服务"""
        service = ServiceControlService(db_session)

        with pytest.raises(HTTPException) as exc_info:
            service.start_service(999, "frontend")

        assert exc_info.value.status_code == 404
        assert "应用不存在" in str(exc_info.value.detail)

    @patch("psutil.pid_exists")
    def test_start_service_already_running(self, mock_pid_exists, db_session: Session):
        """测试启动已运行的服务"""
        mock_pid_exists.return_value = True

        service = ServiceControlService(db_session)

        # 创建测试应用和运行中的服务
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        running_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(running_service)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.start_service(app.id, "frontend")

        assert exc_info.value.status_code == 400
        assert "已在运行" in str(exc_info.value.detail)

    def test_start_service_no_config(self, db_session: Session):
        """测试启动未配置的服务"""
        service = ServiceControlService(db_session)

        # 创建没有前端配置的应用
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.start_service(app.id, "frontend")

        assert exc_info.value.status_code == 400
        assert "未配置启动命令或工作目录" in str(exc_info.value.detail)

    @patch("os.path.exists")
    def test_start_service_directory_not_exists(self, mock_exists, db_session: Session):
        """测试启动服务时工作目录不存在"""
        mock_exists.return_value = False

        service = ServiceControlService(db_session)

        # 创建测试应用
        app = App(
            name="Test App",
            frontend_dir="/nonexistent/path",
            frontend_start_cmd="npm start",
            created_by=1,
        )
        db_session.add(app)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.start_service(app.id, "frontend")

        assert exc_info.value.status_code == 400
        assert "工作目录不存在" in str(exc_info.value.detail)

    @patch("subprocess.Popen")
    @patch("os.path.exists")
    @patch("time.sleep")
    def test_start_service_process_failed(
        self, mock_sleep, mock_exists, mock_popen, db_session: Session
    ):
        """测试启动服务进程失败"""
        mock_exists.return_value = True
        mock_process = Mock()
        mock_process.pid = 1234
        mock_process.poll.return_value = 1  # 进程已退出，返回码非0
        mock_process.communicate.return_value = (b"", b"Error starting process")
        mock_popen.return_value = mock_process

        service = ServiceControlService(db_session)

        # 创建测试应用
        app = App(
            name="Test App",
            frontend_dir="/path/to/frontend",
            frontend_start_cmd="invalid_command",
            created_by=1,
        )
        db_session.add(app)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.start_service(app.id, "frontend")

        assert exc_info.value.status_code == 500
        assert "启动失败" in str(exc_info.value.detail)

        # 验证服务状态设置为错误
        app_service = service.get_app_service(app.id, "frontend")
        assert app_service.status == "error"

    @patch("psutil.pid_exists")
    @patch("psutil.Process")
    @patch("psutil.wait_procs")
    @patch("app.services.log_monitor.log_monitor_service.stop_app_monitoring")
    def test_stop_service_success(
        self,
        mock_stop_monitoring,
        mock_wait_procs,
        mock_process_class,
        mock_pid_exists,
        db_session: Session,
    ):
        """测试成功停止服务"""
        # 设置模拟
        mock_pid_exists.return_value = True
        mock_process = Mock()
        mock_process.children.return_value = []
        mock_process_class.return_value = mock_process
        mock_wait_procs.return_value = ([mock_process], [])  # 进程已正常退出

        service = ServiceControlService(db_session)

        # 创建测试应用和运行中的服务
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        running_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(running_service)
        db_session.commit()

        result = service.stop_service(app.id, "frontend")

        assert result["status"] == "success"
        assert "停止成功" in result["message"]

        # 验证服务状态已更新
        db_session.refresh(running_service)
        assert running_service.status == "stopped"
        assert running_service.pid is None
        assert running_service.stopped_at is not None

        # 验证监控已停止
        mock_stop_monitoring.assert_called_once_with(app.id, "frontend")

    def test_stop_service_not_running(self, db_session: Session):
        """测试停止未运行的服务"""
        service = ServiceControlService(db_session)

        # 创建测试应用和停止的服务
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        stopped_service = AppService(
            app_id=app.id, service_type="frontend", status="stopped"
        )
        db_session.add(stopped_service)
        db_session.commit()

        with pytest.raises(HTTPException) as exc_info:
            service.stop_service(app.id, "frontend")

        assert exc_info.value.status_code == 400
        assert "未运行" in str(exc_info.value.detail)

    @patch("psutil.pid_exists")
    def test_stop_service_process_not_exists(
        self, mock_pid_exists, db_session: Session
    ):
        """测试停止不存在进程的服务"""
        mock_pid_exists.return_value = False

        service = ServiceControlService(db_session)

        # 创建测试应用和运行中的服务（但进程不存在）
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        running_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(running_service)
        db_session.commit()

        result = service.stop_service(app.id, "frontend")

        assert result["status"] == "success"
        assert "已停止" in result["message"]

        # 验证服务状态已更新
        db_session.refresh(running_service)
        assert running_service.status == "stopped"
        assert running_service.pid is None

    @patch("app.services.service_control.ServiceControlService.stop_service")
    @patch("app.services.service_control.ServiceControlService.start_service")
    @patch("time.sleep")
    def test_restart_service_success(
        self, mock_sleep, mock_start, mock_stop, db_session: Session
    ):
        """测试成功重启服务"""
        mock_stop.return_value = {"status": "success", "message": "停止成功"}
        mock_start.return_value = {"status": "success", "message": "启动成功", "pid": 1234}

        service = ServiceControlService(db_session)

        result = service.restart_service(1, "frontend")

        assert result["status"] == "success"
        assert result["pid"] == 1234

        mock_stop.assert_called_once_with(1, "frontend")
        mock_start.assert_called_once_with(1, "frontend")

    @patch("app.services.service_control.ServiceControlService.start_service")
    @patch("time.sleep")
    def test_restart_service_not_running(
        self, mock_sleep, mock_start, db_session: Session
    ):
        """测试重启未运行的服务"""
        mock_start.return_value = {"status": "success", "message": "启动成功", "pid": 1234}

        service = ServiceControlService(db_session)

        # 模拟停止服务时抛出"未运行"异常
        with patch.object(service, "stop_service") as mock_stop:
            mock_stop.side_effect = HTTPException(status_code=400, detail="服务未运行")

            result = service.restart_service(1, "frontend")

            assert result["status"] == "success"
            assert result["pid"] == 1234

            mock_start.assert_called_once_with(1, "frontend")

    @patch("psutil.pid_exists")
    def test_get_service_status_running(self, mock_pid_exists, db_session: Session):
        """测试获取运行中服务的状态"""
        mock_pid_exists.return_value = True

        service = ServiceControlService(db_session)

        # 创建测试应用和服务
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        app_service = AppService(
            app_id=app.id,
            service_type="frontend",
            status="running",
            pid=1234,
            port=3000,
            started_at=datetime.utcnow(),
        )
        db_session.add(app_service)
        db_session.commit()

        result = service.get_service_status(app.id, "frontend")

        assert result["status"] == "running"
        assert result["pid"] == 1234
        assert result["port"] == 3000
        assert result["started_at"] is not None

    @patch("psutil.pid_exists")
    def test_get_service_status_process_dead(
        self, mock_pid_exists, db_session: Session
    ):
        """测试获取进程已死服务的状态"""
        mock_pid_exists.return_value = False

        service = ServiceControlService(db_session)

        # 创建测试应用和服务（状态为运行但进程不存在）
        app = App(name="Test App", created_by=1)
        db_session.add(app)
        db_session.commit()

        app_service = AppService(
            app_id=app.id, service_type="frontend", status="running", pid=1234
        )
        db_session.add(app_service)
        db_session.commit()

        result = service.get_service_status(app.id, "frontend")

        assert result["status"] == "stopped"
        assert result["pid"] is None

        # 验证数据库中的状态已更新
        db_session.refresh(app_service)
        assert app_service.status == "stopped"
        assert app_service.pid is None

    def test_get_service_status_not_configured(self, db_session: Session):
        """测试获取未配置服务的状态"""
        service = ServiceControlService(db_session)

        result = service.get_service_status(1, "frontend")

        assert result["status"] == "not_configured"
        assert "未配置" in result["message"]

    def test_control_service_start(self, db_session: Session):
        """测试服务控制 - 启动"""
        service = ServiceControlService(db_session)

        request = ServiceControlRequest(service_type="frontend", action="start")

        with patch.object(service, "start_service") as mock_start:
            mock_start.return_value = {"status": "success"}

            result = service.control_service(1, request)

            assert result["status"] == "success"
            mock_start.assert_called_once_with(1, "frontend")

    def test_control_service_invalid_action(self, db_session: Session):
        """测试服务控制 - 无效操作"""
        service = ServiceControlService(db_session)

        request = ServiceControlRequest(
            service_type="frontend", action="invalid_action"
        )

        with pytest.raises(HTTPException) as exc_info:
            service.control_service(1, request)

        assert exc_info.value.status_code == 400
        assert "不支持的操作" in str(exc_info.value.detail)
