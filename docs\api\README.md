# API 文档

应用项目管理系统 RESTful API 文档。

## 基础信息

- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

### 获取访问令牌

```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=your_username&password=your_password
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### 使用访问令牌

在请求头中包含访问令牌：

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 用户管理

### 获取当前用户信息

```http
GET /auth/me
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "is_active": true,
  "is_superuser": true,
  "roles": ["admin"],
  "permissions": ["read", "write", "delete"]
}
```

### 获取用户列表

```http
GET /users?page=1&size=20&search=keyword
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20)
- `search`: 搜索关键词 (可选)
- `is_active`: 是否活跃 (可选)

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "username": "user1",
      "email": "<EMAIL>",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5
}
```

### 创建用户

```http
POST /users
Authorization: Bearer {token}
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword",
  "is_active": true,
  "roles": ["user"]
}
```

## 应用管理

### 获取应用列表

```http
GET /apps?page=1&size=20&status=running
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `status`: 应用状态 (running, stopped, error)
- `search`: 搜索关键词

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "name": "My App",
      "description": "Application description",
      "status": "running",
      "frontend_port": 3000,
      "backend_port": 8000,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 10,
  "page": 1,
  "size": 20,
  "pages": 1
}
```

### 创建应用

```http
POST /apps
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "New App",
  "description": "New application",
  "frontend_port": 3001,
  "backend_port": 8001,
  "config": {
    "environment": "development",
    "auto_start": true
  }
}
```

### 启动应用

```http
POST /apps/{app_id}/start
Authorization: Bearer {token}
```

### 停止应用

```http
POST /apps/{app_id}/stop
Authorization: Bearer {token}
```

### 重启应用

```http
POST /apps/{app_id}/restart
Authorization: Bearer {token}
```

## 任务管理

### 获取任务列表

```http
GET /tasks?page=1&size=20&status=pending
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "name": "Deploy App",
      "description": "Deploy application to production",
      "status": "pending",
      "progress": 0,
      "created_at": "2024-01-01T00:00:00Z",
      "started_at": null,
      "completed_at": null,
      "error_message": null
    }
  ],
  "total": 5,
  "page": 1,
  "size": 20,
  "pages": 1
}
```

### 创建任务

```http
POST /tasks
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Backup Database",
  "description": "Create database backup",
  "task_type": "backup",
  "parameters": {
    "database": "app_manager",
    "compression": true
  },
  "schedule": "0 2 * * *"
}
```

### 取消任务

```http
POST /tasks/{task_id}/cancel
Authorization: Bearer {token}
```

## 日志管理

### 获取日志列表

```http
GET /logs?page=1&size=50&level=ERROR&app_id=1
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `app_id`: 应用ID
- `start_time`: 开始时间 (ISO 8601)
- `end_time`: 结束时间 (ISO 8601)
- `search`: 搜索关键词

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "app_id": 1,
      "level": "ERROR",
      "message": "Database connection failed",
      "timestamp": "2024-01-01T12:00:00Z",
      "service_type": "backend",
      "metadata": {
        "error_code": "DB_CONNECTION_ERROR",
        "retry_count": 3
      }
    }
  ],
  "total": 100,
  "page": 1,
  "size": 50,
  "pages": 2
}
```

### 收集日志

```http
POST /logs
Authorization: Bearer {token}
Content-Type: application/json

{
  "app_id": 1,
  "level": "INFO",
  "message": "Application started successfully",
  "service_type": "backend",
  "metadata": {
    "version": "1.0.0",
    "environment": "production"
  }
}
```

## 系统信息

### 获取系统状态

```http
GET /system/status
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 86400,
  "database": {
    "status": "connected",
    "connections": 5,
    "max_connections": 100
  },
  "redis": {
    "status": "connected",
    "memory_usage": "10MB",
    "connected_clients": 3
  },
  "disk_usage": {
    "total": "100GB",
    "used": "45GB",
    "free": "55GB",
    "percentage": 45
  },
  "memory_usage": {
    "total": "8GB",
    "used": "3.2GB",
    "free": "4.8GB",
    "percentage": 40
  }
}
```

### 获取系统指标

```http
GET /system/metrics?period=1h
Authorization: Bearer {token}
```

**查询参数**:
- `period`: 时间周期 (1h, 6h, 24h, 7d, 30d)

## WebSocket 连接

### 连接端点

```
ws://localhost:8000/ws?token={jwt_token}
```

### 消息格式

**客户端发送**:
```json
{
  "type": "subscribe",
  "channel": "app_status",
  "app_id": 1
}
```

**服务端推送**:
```json
{
  "type": "app_status_update",
  "data": {
    "app_id": 1,
    "status": "running",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### 支持的频道

- `app_status` - 应用状态更新
- `task_progress` - 任务进度更新
- `log_stream` - 实时日志流
- `system_alerts` - 系统告警

## 错误处理

### 错误响应格式

```json
{
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "具体错误信息"
  }
}
```

### 常见错误码

- `400` - 请求参数错误
- `401` - 未认证或令牌无效
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 数据验证失败
- `429` - 请求频率过高
- `500` - 服务器内部错误

## 限流

API 实施了速率限制：
- 认证端点: 5 次/分钟
- 其他端点: 100 次/分钟
- WebSocket 连接: 10 次/分钟

超出限制时返回 `429 Too Many Requests`。

## 版本控制

API 使用版本前缀进行版本控制：
- 当前版本: `v1`
- 向后兼容性: 保证至少 6 个月
- 废弃通知: 提前 3 个月通知

## 更多信息

- [Swagger UI](http://localhost:8000/docs) - 交互式 API 文档
- [ReDoc](http://localhost:8000/redoc) - 详细 API 文档
- [OpenAPI 规范](http://localhost:8000/openapi.json) - API 规范文件
