"""
应用管理服务
"""
import asyncio
import os
import subprocess
from typing import Dict, List, Optional

import psutil
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.app import App, AppConfig, AppLog, AppService
from app.models.user import User
from app.schemas.app import (
    AppConfigCreate,
    AppConfigUpdate,
    AppCreate,
    AppUpdate,
    ServiceControlRequest,
)


class AppManagementService:
    """应用管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_apps(
        self, skip: int = 0, limit: int = 100, user_id: Optional[int] = None
    ) -> List[App]:
        """
        获取应用列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数
            user_id: 用户ID（可选，用于过滤用户创建的应用）

        Returns:
            应用列表
        """
        query = self.db.query(App)
        if user_id:
            query = query.filter(App.created_by == user_id)
        return query.offset(skip).limit(limit).all()

    def get_app_by_id(self, app_id: int) -> Optional[App]:
        """
        根据ID获取应用

        Args:
            app_id: 应用ID

        Returns:
            应用对象或None
        """
        return self.db.query(App).filter(App.id == app_id).first()

    def get_app_by_name(self, name: str) -> Optional[App]:
        """
        根据名称获取应用

        Args:
            name: 应用名称

        Returns:
            应用对象或None
        """
        return self.db.query(App).filter(App.name == name).first()

    def create_app(self, app_data: AppCreate, user_id: int) -> App:
        """
        创建应用

        Args:
            app_data: 应用创建数据
            user_id: 创建者用户ID

        Returns:
            创建的应用对象

        Raises:
            HTTPException: 应用名称已存在时抛出异常
        """
        # 检查应用名称是否已存在
        if self.get_app_by_name(app_data.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="应用名称已存在"
            )

        # 验证目录路径
        if app_data.frontend_dir and not os.path.exists(app_data.frontend_dir):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="前端目录不存在"
            )

        if app_data.backend_dir and not os.path.exists(app_data.backend_dir):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="后端目录不存在"
            )

        # 创建应用
        db_app = App(
            name=app_data.name,
            description=app_data.description,
            frontend_dir=app_data.frontend_dir,
            backend_dir=app_data.backend_dir,
            frontend_start_cmd=app_data.frontend_start_cmd,
            backend_start_cmd=app_data.backend_start_cmd,
            frontend_stop_cmd=app_data.frontend_stop_cmd,
            backend_stop_cmd=app_data.backend_stop_cmd,
            frontend_port=app_data.frontend_port,
            backend_port=app_data.backend_port,
            is_active=app_data.is_active,
            created_by=user_id,
        )

        self.db.add(db_app)
        self.db.commit()
        self.db.refresh(db_app)

        # 创建对应的服务记录
        if app_data.frontend_dir:
            frontend_service = AppService(
                app_id=db_app.id,
                service_type="frontend",
                status="stopped",
                port=app_data.frontend_port,
            )
            self.db.add(frontend_service)

        if app_data.backend_dir:
            backend_service = AppService(
                app_id=db_app.id,
                service_type="backend",
                status="stopped",
                port=app_data.backend_port,
            )
            self.db.add(backend_service)

        self.db.commit()

        # 发送WebSocket通知
        asyncio.create_task(self._notify_app_created(db_app))

        return db_app

    def update_app(self, app_id: int, app_data: AppUpdate, user_id: int) -> App:
        """
        更新应用信息

        Args:
            app_id: 应用ID
            app_data: 应用更新数据
            user_id: 当前用户ID

        Returns:
            更新后的应用对象

        Raises:
            HTTPException: 应用不存在、无权限或名称已被其他应用使用时抛出异常
        """
        app = self.get_app_by_id(app_id)
        if not app:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

        # 检查权限（只有创建者可以修改）
        if app.created_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="无权限修改此应用"
            )

        # 检查应用名称是否被其他应用使用
        if app_data.name and app_data.name != app.name:
            existing_app = self.get_app_by_name(app_data.name)
            if existing_app and existing_app.id != app_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="应用名称已被其他应用使用"
                )

        # 验证目录路径
        if app_data.frontend_dir and not os.path.exists(app_data.frontend_dir):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="前端目录不存在"
            )

        if app_data.backend_dir and not os.path.exists(app_data.backend_dir):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="后端目录不存在"
            )

        # 更新应用信息
        update_data = app_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(app, field, value)

        self.db.commit()
        self.db.refresh(app)

        # 发送WebSocket通知
        asyncio.create_task(self._notify_app_updated(app))

        return app

    def delete_app(self, app_id: int, user_id: int) -> bool:
        """
        删除应用

        Args:
            app_id: 应用ID
            user_id: 当前用户ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 应用不存在、无权限或应用正在运行时抛出异常
        """
        app = self.get_app_by_id(app_id)
        if not app:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

        # 检查权限（只有创建者可以删除）
        if app.created_by != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="无权限删除此应用"
            )

        # 检查应用是否有正在运行的服务
        running_services = (
            self.db.query(AppService)
            .filter(AppService.app_id == app_id, AppService.status == "running")
            .first()
        )

        if running_services:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="应用有正在运行的服务，请先停止服务"
            )

        # 发送WebSocket通知
        asyncio.create_task(self._notify_app_deleted(app_id))

        self.db.delete(app)
        self.db.commit()

        return True

    def get_app_with_services(self, app_id: int) -> Dict:
        """
        获取应用及其服务状态

        Args:
            app_id: 应用ID

        Returns:
            包含服务状态的应用信息
        """
        app = self.get_app_by_id(app_id)
        if not app:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="应用不存在")

        # 获取服务状态
        services = self.db.query(AppService).filter(AppService.app_id == app_id).all()
        service_status = {}

        for service in services:
            service_status[f"{service.service_type}_status"] = service.status
            service_status[f"{service.service_type}_pid"] = service.pid
            service_status[f"{service.service_type}_port"] = service.port
            service_status[f"{service.service_type}_started_at"] = service.started_at
            service_status[f"{service.service_type}_stopped_at"] = service.stopped_at

        # 转换为字典并合并服务状态
        app_dict = {
            "id": app.id,
            "name": app.name,
            "description": app.description,
            "frontend_dir": app.frontend_dir,
            "backend_dir": app.backend_dir,
            "frontend_start_cmd": app.frontend_start_cmd,
            "backend_start_cmd": app.backend_start_cmd,
            "frontend_stop_cmd": app.frontend_stop_cmd,
            "backend_stop_cmd": app.backend_stop_cmd,
            "frontend_port": app.frontend_port,
            "backend_port": app.backend_port,
            "is_active": app.is_active,
            "created_by": app.created_by,
            "created_at": app.created_at,
            "updated_at": app.updated_at,
            **service_status,
        }

        return app_dict

    def get_app_stats(self) -> Dict:
        """
        获取应用统计信息

        Returns:
            应用统计数据
        """
        total_apps = self.db.query(App).count()

        # 统计不同状态的应用数量
        running_apps = (
            self.db.query(AppService).filter(AppService.status == "running").count()
        )
        stopped_apps = (
            self.db.query(AppService).filter(AppService.status == "stopped").count()
        )
        error_apps = (
            self.db.query(AppService).filter(AppService.status == "error").count()
        )

        return {
            "total_apps": total_apps,
            "running_apps": running_apps,
            "stopped_apps": stopped_apps,
            "error_apps": error_apps,
        }

    async def _notify_app_created(self, app: App):
        """通知应用创建"""
        try:
            from app.websocket.connection_manager import send_app_status_update

            await send_app_status_update(
                app.id,
                {
                    "app_id": app.id,
                    "name": app.name,
                    "frontend_status": "stopped",
                    "backend_status": "stopped",
                    "is_active": app.is_active,
                    "event": "app_created",
                },
            )
        except Exception as e:
            print(f"Failed to send WebSocket notification: {e}")

    async def _notify_app_updated(self, app: App):
        """通知应用更新"""
        try:
            from app.websocket.connection_manager import send_app_status_update

            await send_app_status_update(
                app.id,
                {
                    "app_id": app.id,
                    "name": app.name,
                    "frontend_status": getattr(app, "frontend_status", "stopped"),
                    "backend_status": getattr(app, "backend_status", "stopped"),
                    "is_active": app.is_active,
                    "event": "app_updated",
                },
            )
        except Exception as e:
            print(f"Failed to send WebSocket notification: {e}")

    async def _notify_app_deleted(self, app_id: int):
        """通知应用删除"""
        try:
            from app.websocket.connection_manager import send_app_status_update

            await send_app_status_update(
                app_id, {"app_id": app_id, "event": "app_deleted"}
            )
        except Exception as e:
            print(f"Failed to send WebSocket notification: {e}")


class AppConfigService:
    """应用配置服务类"""

    def __init__(self, db: Session):
        self.db = db

    def _record_config_history(
        self,
        config: AppConfig,
        operation: str,
        old_value: str = None,
        new_value: str = None,
        user_id: int = None,
    ):
        """
        记录配置变更历史

        Args:
            config: 配置对象
            operation: 操作类型
            old_value: 旧值
            new_value: 新值
            user_id: 操作用户ID
        """
        from app.models.config_history import AppConfigHistory

        history = AppConfigHistory(
            config_id=config.id,
            app_id=config.app_id,
            key=config.key,
            old_value=old_value,
            new_value=new_value,
            operation=operation,
            changed_by=user_id,
        )

        self.db.add(history)
        self.db.commit()

    def get_app_configs(self, app_id: int) -> List[AppConfig]:
        """
        获取应用配置列表

        Args:
            app_id: 应用ID

        Returns:
            应用配置列表
        """
        return self.db.query(AppConfig).filter(AppConfig.app_id == app_id).all()

    def get_config_by_id(self, config_id: int) -> Optional[AppConfig]:
        """
        根据ID获取配置

        Args:
            config_id: 配置ID

        Returns:
            配置对象或None
        """
        return self.db.query(AppConfig).filter(AppConfig.id == config_id).first()

    def create_config(
        self, app_id: int, config_data: AppConfigCreate, user_id: int = None
    ) -> AppConfig:
        """
        创建应用配置

        Args:
            app_id: 应用ID
            config_data: 配置创建数据

        Returns:
            创建的配置对象

        Raises:
            HTTPException: 配置键已存在时抛出异常
        """
        # 检查配置键是否已存在
        existing_config = (
            self.db.query(AppConfig)
            .filter(AppConfig.app_id == app_id, AppConfig.key == config_data.key)
            .first()
        )

        if existing_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="配置键已存在"
            )

        # 创建配置
        db_config = AppConfig(
            app_id=app_id,
            key=config_data.key,
            value=config_data.value,
            description=config_data.description,
        )

        self.db.add(db_config)
        self.db.commit()
        self.db.refresh(db_config)

        # 记录配置历史
        if user_id:
            self._record_config_history(
                db_config, "create", None, config_data.value, user_id
            )

        return db_config

    def update_config(
        self, config_id: int, config_data: AppConfigUpdate, user_id: int = None
    ) -> AppConfig:
        """
        更新应用配置

        Args:
            config_id: 配置ID
            config_data: 配置更新数据

        Returns:
            更新后的配置对象

        Raises:
            HTTPException: 配置不存在时抛出异常
        """
        config = self.get_config_by_id(config_id)
        if not config:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="配置不存在")

        # 记录旧值
        old_value = config.value

        # 更新配置信息
        update_data = config_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(config, field, value)

        self.db.commit()
        self.db.refresh(config)

        # 记录配置历史
        if user_id and "value" in update_data:
            self._record_config_history(
                config, "update", old_value, config.value, user_id
            )

        return config

    def delete_config(self, config_id: int, user_id: int = None) -> bool:
        """
        删除应用配置

        Args:
            config_id: 配置ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 配置不存在时抛出异常
        """
        config = self.get_config_by_id(config_id)
        if not config:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="配置不存在")

        # 记录配置历史
        if user_id:
            self._record_config_history(config, "delete", config.value, None, user_id)

        self.db.delete(config)
        self.db.commit()

        return True

    def get_config_history(self, app_id: int, config_id: int = None) -> List:
        """
        获取配置变更历史

        Args:
            app_id: 应用ID
            config_id: 配置ID（可选，不指定则获取应用所有配置的历史）

        Returns:
            配置历史记录列表
        """
        from app.models.config_history import AppConfigHistory

        query = self.db.query(AppConfigHistory).filter(
            AppConfigHistory.app_id == app_id
        )

        if config_id:
            query = query.filter(AppConfigHistory.config_id == config_id)

        return query.order_by(AppConfigHistory.changed_at.desc()).all()

    def rollback_config(
        self, config_id: int, history_id: int, user_id: int
    ) -> AppConfig:
        """
        回滚配置到指定历史版本

        Args:
            config_id: 配置ID
            history_id: 历史记录ID
            user_id: 操作用户ID

        Returns:
            回滚后的配置对象

        Raises:
            HTTPException: 配置或历史记录不存在时抛出异常
        """
        from app.models.config_history import AppConfigHistory

        config = self.get_config_by_id(config_id)
        if not config:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="配置不存在")

        history = (
            self.db.query(AppConfigHistory)
            .filter(AppConfigHistory.id == history_id)
            .first()
        )
        if not history:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="历史记录不存在")

        if history.config_id != config_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="历史记录与配置不匹配"
            )

        # 记录当前值作为旧值
        old_value = config.value

        # 回滚到历史值
        rollback_value = (
            history.old_value if history.operation == "update" else history.new_value
        )
        config.value = rollback_value

        self.db.commit()
        self.db.refresh(config)

        # 记录回滚操作
        self._record_config_history(
            config, "rollback", old_value, rollback_value, user_id
        )

        return config
