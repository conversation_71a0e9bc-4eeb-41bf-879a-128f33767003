"""
认证API测试
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.models.user import User
from app.core.security import verify_password


class TestAuthAPI:
    """认证API测试类"""
    
    def test_login_success(self, client: TestClient, test_user: User):
        """测试登录成功"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "secret"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_username(self, client: TestClient):
        """测试无效用户名登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "nonexistent",
                "password": "secret"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_invalid_password(self, client: TestClient, test_user: User):
        """测试无效密码登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "wrongpassword"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
    
    def test_login_inactive_user(self, client: TestClient, db: Session):
        """测试非活跃用户登录"""
        # 创建非活跃用户
        inactive_user = User(
            username="inactive",
            email="<EMAIL>",
            hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",
            is_active=False
        )
        db.add(inactive_user)
        db.commit()
        
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "inactive",
                "password": "secret"
            }
        )
        
        assert response.status_code == 401
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict):
        """测试获取当前用户信息"""
        response = client.get(
            "/api/v1/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "username" in data
        assert "email" in data
        assert "is_active" in data
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """测试未授权获取用户信息"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """测试无效token获取用户信息"""
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
    
    def test_refresh_token(self, client: TestClient, auth_headers: dict):
        """测试刷新token"""
        response = client.post(
            "/api/v1/auth/refresh",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
    
    def test_logout(self, client: TestClient, auth_headers: dict):
        """测试登出"""
        response = client.post(
            "/api/v1/auth/logout",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Successfully logged out"


class TestPasswordSecurity:
    """密码安全测试"""
    
    def test_password_hashing(self):
        """测试密码哈希"""
        from app.core.security import get_password_hash, verify_password
        
        password = "testpassword"
        hashed = get_password_hash(password)
        
        # 哈希后的密码不应该等于原密码
        assert hashed != password
        
        # 验证密码应该成功
        assert verify_password(password, hashed) is True
        
        # 错误密码验证应该失败
        assert verify_password("wrongpassword", hashed) is False
    
    def test_password_strength_validation(self):
        """测试密码强度验证"""
        from app.core.security import validate_password_strength
        
        # 强密码
        assert validate_password_strength("StrongP@ssw0rd123") is True
        
        # 弱密码
        assert validate_password_strength("weak") is False
        assert validate_password_strength("12345678") is False
        assert validate_password_strength("password") is False


class TestJWTTokens:
    """JWT令牌测试"""
    
    def test_create_access_token(self):
        """测试创建访问令牌"""
        from app.core.security import create_access_token
        
        data = {"sub": "testuser"}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_token(self):
        """测试验证令牌"""
        from app.core.security import create_access_token, verify_token
        
        username = "testuser"
        token = create_access_token({"sub": username})
        
        # 验证有效令牌
        decoded_username = verify_token(token)
        assert decoded_username == username
        
        # 验证无效令牌
        invalid_decoded = verify_token("invalid_token")
        assert invalid_decoded is None
    
    def test_token_expiration(self):
        """测试令牌过期"""
        from datetime import timedelta
        from app.core.security import create_access_token, verify_token
        
        # 创建立即过期的令牌
        token = create_access_token(
            {"sub": "testuser"}, 
            expires_delta=timedelta(seconds=-1)
        )
        
        # 验证过期令牌
        decoded_username = verify_token(token)
        assert decoded_username is None


@pytest.mark.integration
class TestAuthIntegration:
    """认证集成测试"""
    
    def test_full_auth_flow(self, client: TestClient, test_user: User):
        """测试完整认证流程"""
        # 1. 登录
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "secret"
            }
        )
        assert login_response.status_code == 200
        token_data = login_response.json()
        token = token_data["access_token"]
        
        # 2. 使用token访问受保护资源
        headers = {"Authorization": f"Bearer {token}"}
        me_response = client.get("/api/v1/auth/me", headers=headers)
        assert me_response.status_code == 200
        user_data = me_response.json()
        assert user_data["username"] == test_user.username
        
        # 3. 刷新token
        refresh_response = client.post("/api/v1/auth/refresh", headers=headers)
        assert refresh_response.status_code == 200
        new_token_data = refresh_response.json()
        new_token = new_token_data["access_token"]
        assert new_token != token
        
        # 4. 使用新token访问资源
        new_headers = {"Authorization": f"Bearer {new_token}"}
        me_response2 = client.get("/api/v1/auth/me", headers=new_headers)
        assert me_response2.status_code == 200
        
        # 5. 登出
        logout_response = client.post("/api/v1/auth/logout", headers=new_headers)
        assert logout_response.status_code == 200


@pytest.mark.performance
class TestAuthPerformance:
    """认证性能测试"""
    
    def test_login_performance(self, client: TestClient, test_user: User):
        """测试登录性能"""
        import time
        
        start_time = time.time()
        
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": test_user.username,
                "password": "secret"
            }
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert response.status_code == 200
        assert duration < 1.0  # 登录应该在1秒内完成
    
    def test_token_verification_performance(self, auth_headers: dict):
        """测试token验证性能"""
        from app.core.security import verify_token
        import time
        
        token = auth_headers["Authorization"].split(" ")[1]
        
        start_time = time.time()
        
        # 执行多次验证
        for _ in range(100):
            verify_token(token)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 100次验证应该在0.1秒内完成
        assert duration < 0.1
