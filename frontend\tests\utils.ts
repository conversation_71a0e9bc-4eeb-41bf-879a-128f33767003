/**
 * 测试工具函数
 */
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { vi } from 'vitest'
import type { Component } from 'vue'

// 创建测试路由
export function createTestRouter(routes: any[] = []) {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/login', component: { template: '<div>Login</div>' } },
      ...routes
    ]
  })
}

// 创建测试Pinia实例
export function createTestPinia() {
  return createPinia()
}

// 挂载组件的辅助函数
export function mountComponent(
  component: Component,
  options: {
    props?: Record<string, any>
    slots?: Record<string, any>
    global?: Record<string, any>
    router?: any
    pinia?: any
  } = {}
) {
  const { props, slots, global, router, pinia } = options

  const defaultGlobal = {
    plugins: [pinia || createTestPinia()],
    mocks: {
      $t: (key: string) => key, // Mock i18n
      $route: { path: '/', params: {}, query: {} },
      $router: router || createTestRouter()
    }
  }

  if (router) {
    defaultGlobal.plugins.push(router)
  }

  return mount(component, {
    props,
    slots,
    global: {
      ...defaultGlobal,
      ...global
    }
  })
}

// 等待异步操作完成
export async function flushPromises() {
  return new Promise(resolve => setTimeout(resolve, 0))
}

// 等待Vue的下一个tick
export async function nextTick() {
  return new Promise(resolve => setTimeout(resolve, 0))
}

// 模拟用户输入
export async function userInput(wrapper: VueWrapper<any>, selector: string, value: string) {
  const input = wrapper.find(selector)
  await input.setValue(value)
  await input.trigger('input')
  await nextTick()
}

// 模拟用户点击
export async function userClick(wrapper: VueWrapper<any>, selector: string) {
  const element = wrapper.find(selector)
  await element.trigger('click')
  await nextTick()
}

// 模拟表单提交
export async function submitForm(wrapper: VueWrapper<any>, formSelector: string = 'form') {
  const form = wrapper.find(formSelector)
  await form.trigger('submit')
  await nextTick()
}

// Mock API响应
export function mockApiResponse(data: any, status: number = 200) {
  return vi.fn().mockResolvedValue({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {}
  })
}

// Mock API错误
export function mockApiError(message: string, status: number = 500) {
  return vi.fn().mockRejectedValue({
    response: {
      data: { message },
      status,
      statusText: 'Error'
    }
  })
}

// 创建测试用户数据
export function createTestUser(overrides: Partial<any> = {}) {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    isActive: true,
    roles: ['user'],
    permissions: ['read'],
    ...overrides
  }
}

// 创建测试应用数据
export function createTestApp(overrides: Partial<any> = {}) {
  return {
    id: 1,
    name: 'Test App',
    description: 'Test application',
    status: 'running',
    frontendPort: 3000,
    backendPort: 8000,
    ...overrides
  }
}

// 创建测试任务数据
export function createTestTask(overrides: Partial<any> = {}) {
  return {
    id: 1,
    name: 'Test Task',
    description: 'Test task description',
    status: 'pending',
    createdAt: new Date().toISOString(),
    ...overrides
  }
}

// Mock Element Plus组件
export function mockElementPlus() {
  vi.mock('element-plus', () => ({
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElNotification: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue('confirm'),
      alert: vi.fn().mockResolvedValue('confirm'),
      prompt: vi.fn().mockResolvedValue({ value: 'test' })
    }
  }))
}

// 测试数据生成器
export class TestDataGenerator {
  static generateUsers(count: number = 5) {
    return Array.from({ length: count }, (_, index) => ({
      id: index + 1,
      username: `user${index + 1}`,
      email: `user${index + 1}@example.com`,
      isActive: true,
      roles: ['user']
    }))
  }

  static generateApps(count: number = 3) {
    return Array.from({ length: count }, (_, index) => ({
      id: index + 1,
      name: `App ${index + 1}`,
      description: `Test application ${index + 1}`,
      status: index % 2 === 0 ? 'running' : 'stopped',
      frontendPort: 3000 + index,
      backendPort: 8000 + index
    }))
  }

  static generateTasks(count: number = 10) {
    const statuses = ['pending', 'running', 'completed', 'failed']
    return Array.from({ length: count }, (_, index) => ({
      id: index + 1,
      name: `Task ${index + 1}`,
      description: `Test task ${index + 1}`,
      status: statuses[index % statuses.length],
      createdAt: new Date(Date.now() - index * 86400000).toISOString()
    }))
  }

  static generateLogs(count: number = 20) {
    const levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
    return Array.from({ length: count }, (_, index) => ({
      id: index + 1,
      level: levels[index % levels.length],
      message: `Log message ${index + 1}`,
      timestamp: new Date(Date.now() - index * 60000).toISOString(),
      appId: (index % 3) + 1
    }))
  }
}

// 性能测试工具
export class PerformanceTestHelper {
  private startTime: number = 0

  start() {
    this.startTime = performance.now()
  }

  end(): number {
    return performance.now() - this.startTime
  }

  async measureAsync<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    this.start()
    const result = await fn()
    const duration = this.end()
    return { result, duration }
  }

  measure<T>(fn: () => T): { result: T; duration: number } {
    this.start()
    const result = fn()
    const duration = this.end()
    return { result, duration }
  }
}

// 等待条件满足
export async function waitFor(
  condition: () => boolean,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now()
  
  while (!condition()) {
    if (Date.now() - startTime > timeout) {
      throw new Error(`Timeout waiting for condition after ${timeout}ms`)
    }
    await new Promise(resolve => setTimeout(resolve, interval))
  }
}

// 等待元素出现
export async function waitForElement(
  wrapper: VueWrapper<any>,
  selector: string,
  timeout: number = 5000
): Promise<void> {
  await waitFor(() => wrapper.find(selector).exists(), timeout)
}

// 清理测试环境
export function cleanupTest() {
  vi.clearAllMocks()
  vi.clearAllTimers()
  localStorage.clear()
  sessionStorage.clear()
}
