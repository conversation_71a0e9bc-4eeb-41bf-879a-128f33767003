"""
任务管理API端点
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_user
from app.models.user import User
from app.schemas.task import (
    Task,
    TaskBatchOperation,
    TaskCreate,
    TaskExecuteRequest,
    TaskExecutionStats,
    TaskExecutionUpdate,
    TaskQuery,
    TaskStats,
    TaskTemplate,
    TaskTemplateCreate,
    TaskTemplateUpdate,
    TaskUpdate,
)
from app.services.task_execution_service import task_execution_service
from app.services.task_scheduler import task_scheduler, task_trigger_service
from app.services.task_service import task_service, task_template_service

router = APIRouter()


# 任务管理端点
@router.get("/", response_model=List[Task])
def get_tasks(
    app_id: Optional[int] = Query(None, description="应用ID"),
    task_type: Optional[str] = Query(None, description="任务类型"),
    schedule_type: Optional[str] = Query(None, description="调度类型"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    keyword: Optional[str] = Query(None, description="关键词"),
    created_by: Optional[int] = Query(None, description="创建者ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取任务列表"""
    try:
        query = TaskQuery(
            app_id=app_id,
            task_type=task_type,
            schedule_type=schedule_type,
            is_active=is_active,
            keyword=keyword,
            created_by=created_by,
            skip=skip,
            limit=limit,
        )

        tasks = task_service.get_tasks(db, query)
        return tasks

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tasks: {str(e)}")


@router.post("/", response_model=Task)
def create_task(
    task_data: TaskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建任务"""
    try:
        task = task_service.create_task(db, task_data, current_user.id)
        return task

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")


@router.get("/{task_id}", response_model=Task)
def get_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单个任务"""
    try:
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return task

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get task: {str(e)}")


@router.put("/{task_id}", response_model=Task)
def update_task(
    task_id: int,
    task_data: TaskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """更新任务"""
    try:
        task = task_service.update_task(db, task_id, task_data)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return task

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update task: {str(e)}")


@router.delete("/{task_id}")
def delete_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """删除任务"""
    try:
        success = task_service.delete_task(db, task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")

        return {"message": "Task deleted successfully"}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete task: {str(e)}")


@router.post("/batch")
def batch_operation(
    operation: TaskBatchOperation,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """批量操作任务"""
    try:
        if operation.operation not in ["activate", "deactivate", "delete", "execute"]:
            raise HTTPException(status_code=400, detail="Invalid operation")

        results = task_service.batch_operation(db, operation)
        return results

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to perform batch operation: {str(e)}"
        )


@router.get("/stats/overview", response_model=TaskStats)
def get_task_stats(
    app_id: Optional[int] = Query(None, description="应用ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取任务统计信息"""
    try:
        stats = task_service.get_task_stats(db, app_id)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get task stats: {str(e)}"
        )


@router.get("/stats/executions", response_model=TaskExecutionStats)
def get_execution_stats(
    task_id: Optional[int] = Query(None, description="任务ID"),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取执行统计信息"""
    try:
        stats = task_service.get_execution_stats(db, task_id, days)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get execution stats: {str(e)}"
        )


@router.get("/count/")
def count_tasks(
    app_id: Optional[int] = Query(None, description="应用ID"),
    task_type: Optional[str] = Query(None, description="任务类型"),
    schedule_type: Optional[str] = Query(None, description="调度类型"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    keyword: Optional[str] = Query(None, description="关键词"),
    created_by: Optional[int] = Query(None, description="创建者ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """统计任务数量"""
    try:
        query = TaskQuery(
            app_id=app_id,
            task_type=task_type,
            schedule_type=schedule_type,
            is_active=is_active,
            keyword=keyword,
            created_by=created_by,
        )

        count = task_service.count_tasks(db, query)
        return {"count": count}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to count tasks: {str(e)}")


# 任务模板端点
@router.get("/templates/", response_model=List[TaskTemplate])
def get_task_templates(
    category: Optional[str] = Query(None, description="模板分类"),
    is_public: Optional[bool] = Query(None, description="是否公共模板"),
    created_by: Optional[int] = Query(None, description="创建者ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取任务模板列表"""
    try:
        templates = task_template_service.get_templates(
            db, category, is_public, created_by
        )
        return templates

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get task templates: {str(e)}"
        )


@router.post("/templates/", response_model=TaskTemplate)
def create_task_template(
    template_data: TaskTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建任务模板"""
    try:
        template = task_template_service.create_template(
            db, template_data, current_user.id
        )
        return template

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create task template: {str(e)}"
        )


@router.post("/templates/{template_id}/use", response_model=TaskTemplate)
def use_task_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """使用任务模板"""
    try:
        template = task_template_service.use_template(db, template_id)
        if not template:
            raise HTTPException(status_code=404, detail="Task template not found")

        return template

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to use task template: {str(e)}"
        )


# 任务类型和调度类型枚举端点
@router.get("/types/")
def get_task_types():
    """获取任务类型列表"""
    from app.schemas.task import ScheduleType, TaskType

    return {
        "task_types": [
            {
                "value": task_type.value,
                "label": task_type.value.replace("_", " ").title(),
            }
            for task_type in TaskType
        ],
        "schedule_types": [
            {
                "value": schedule_type.value,
                "label": schedule_type.value.replace("_", " ").title(),
            }
            for schedule_type in ScheduleType
        ],
    }


@router.post("/{task_id}/execute")
def execute_task(
    task_id: int,
    execute_request: Optional[TaskExecuteRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """手动执行任务"""
    try:
        from app.services.task_executor import task_executor

        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        if not task.is_active:
            raise HTTPException(status_code=400, detail="Task is not active")

        # 检查是否有正在运行的执行
        from app.models.task import TaskExecution

        running_execution = (
            db.query(TaskExecution)
            .filter(TaskExecution.task_id == task_id, TaskExecution.status == "running")
            .first()
        )

        if running_execution and not (execute_request and execute_request.force):
            raise HTTPException(status_code=400, detail="Task is already running")

        # 执行任务
        parameters = execute_request.parameters if execute_request else None
        execution = task_executor.execute_task(db, task_id, parameters)

        return {
            "message": "Task execution started",
            "execution_id": execution.id,
            "status": execution.status,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to execute task: {str(e)}")


@router.get("/{task_id}/executions")
def get_task_executions(
    task_id: int,
    status: Optional[str] = Query(None, description="执行状态"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取任务执行记录"""
    try:
        from app.models.task import TaskExecution
        from app.schemas.task import TaskExecutionQuery

        # 验证任务是否存在
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        query = db.query(TaskExecution).filter(TaskExecution.task_id == task_id)

        if status:
            query = query.filter(TaskExecution.status == status)

        executions = (
            query.order_by(TaskExecution.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return executions

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get task executions: {str(e)}"
        )


@router.get("/executions/{execution_id}")
def get_task_execution(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取单个任务执行记录"""
    try:
        from app.models.task import TaskExecution

        execution = (
            db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
        )
        if not execution:
            raise HTTPException(status_code=404, detail="Task execution not found")

        return execution

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get task execution: {str(e)}"
        )


@router.post("/executions/{execution_id}/cancel")
def cancel_task_execution(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """取消任务执行"""
    try:
        from app.services.task_executor import task_executor

        success = task_executor.cancel_execution(db, execution_id)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Task execution not found or cannot be cancelled",
            )

        return {"message": "Task execution cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to cancel task execution: {str(e)}"
        )


@router.get("/validate/cron")
def validate_cron_expression(
    expression: str = Query(..., description="Cron表达式"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """验证Cron表达式"""
    try:
        task_service._validate_cron_expression(expression)

        # 计算接下来几次执行时间
        next_runs = []
        try:
            from datetime import datetime

            from croniter import croniter

            cron = croniter(expression, datetime.now())
            for _ in range(5):  # 显示接下来5次执行时间
                next_runs.append(cron.get_next(datetime).isoformat())
        except ImportError:
            next_runs = ["需要安装croniter库来预览执行时间"]

        return {"valid": True, "message": "Cron表达式有效", "next_runs": next_runs}

    except ValueError as e:
        return {"valid": False, "message": str(e), "next_runs": []}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate cron expression: {str(e)}"
        )


# 调度器管理端点
@router.get("/scheduler/status")
def get_scheduler_status(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取调度器状态"""
    try:
        status = task_scheduler.get_scheduler_status()
        return status

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get scheduler status: {str(e)}"
        )


@router.post("/scheduler/start")
def start_scheduler(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """启动调度器"""
    try:
        task_scheduler.start()
        return {"message": "Task scheduler started successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start scheduler: {str(e)}"
        )


@router.post("/scheduler/stop")
def stop_scheduler(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """停止调度器"""
    try:
        task_scheduler.stop()
        return {"message": "Task scheduler stopped successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop scheduler: {str(e)}"
        )


@router.get("/scheduler/next-scheduled")
def get_next_scheduled_tasks(
    limit: int = Query(10, ge=1, le=100, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取即将执行的任务"""
    try:
        tasks = task_scheduler.get_next_scheduled_tasks(limit)
        return {"tasks": tasks}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get next scheduled tasks: {str(e)}"
        )


@router.post("/{task_id}/schedule")
def schedule_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """调度任务"""
    try:
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        task_scheduler.schedule_task(task)
        return {"message": f"Task {task.name} scheduled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to schedule task: {str(e)}"
        )


@router.post("/{task_id}/unschedule")
def unschedule_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """取消任务调度"""
    try:
        task_scheduler.unschedule_task(task_id)
        return {"message": f"Task {task_id} unscheduled successfully"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to unschedule task: {str(e)}"
        )


# 触发器管理端点
@router.post("/{task_id}/triggers/file-watcher")
def create_file_watcher_trigger(
    task_id: int,
    file_path: str,
    check_interval: int = Query(60, ge=1, le=3600, description="检查间隔(秒)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建文件监控触发器"""
    try:
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        task_trigger_service.create_file_watcher_trigger(
            task_id, file_path, check_interval
        )
        return {"message": f"File watcher trigger created for task {task.name}"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create file watcher trigger: {str(e)}"
        )


@router.post("/{task_id}/triggers/log-pattern")
def create_log_pattern_trigger(
    task_id: int,
    log_file: str,
    pattern: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建日志模式触发器"""
    try:
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        task_trigger_service.create_log_pattern_trigger(task_id, log_file, pattern)
        return {"message": f"Log pattern trigger created for task {task.name}"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create log pattern trigger: {str(e)}"
        )


@router.post("/{task_id}/triggers/http-endpoint")
def create_http_endpoint_trigger(
    task_id: int,
    url: str,
    expected_status: int = Query(200, ge=100, le=599, description="期望的HTTP状态码"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建HTTP端点监控触发器"""
    try:
        task = task_service.get_task(db, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        task_trigger_service.create_http_endpoint_trigger(task_id, url, expected_status)
        return {"message": f"HTTP endpoint trigger created for task {task.name}"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create HTTP endpoint trigger: {str(e)}"
        )


@router.delete("/{task_id}/triggers")
def remove_task_triggers(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """移除任务的所有触发器"""
    try:
        task_trigger_service.scheduler.remove_condition_trigger(task_id)
        return {"message": f"All triggers removed for task {task_id}"}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to remove task triggers: {str(e)}"
        )


# 执行记录管理端点
@router.get("/executions/")
def get_all_executions(
    task_id: Optional[int] = Query(None, description="任务ID"),
    status: Optional[str] = Query(None, description="执行状态"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=1000, description="限制数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取所有执行记录"""
    try:
        from datetime import datetime

        from app.schemas.task import TaskExecutionQuery

        # 解析时间参数
        parsed_start_time = None
        parsed_end_time = None

        if start_time:
            try:
                parsed_start_time = datetime.fromisoformat(
                    start_time.replace("Z", "+00:00")
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_time format")

        if end_time:
            try:
                parsed_end_time = datetime.fromisoformat(
                    end_time.replace("Z", "+00:00")
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_time format")

        query = TaskExecutionQuery(
            task_id=task_id,
            status=status,
            start_time=parsed_start_time,
            end_time=parsed_end_time,
            skip=skip,
            limit=limit,
        )

        executions = task_execution_service.get_executions(db, query)
        total = task_execution_service.count_executions(db, query)

        return {"executions": executions, "total": total, "skip": skip, "limit": limit}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get executions: {str(e)}"
        )


@router.put("/executions/{execution_id}")
def update_execution(
    execution_id: int,
    execution_data: TaskExecutionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """更新执行记录"""
    try:
        execution = task_execution_service.update_execution(
            db, execution_id, execution_data
        )
        if not execution:
            raise HTTPException(status_code=404, detail="Task execution not found")

        return execution

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to update execution: {str(e)}"
        )


@router.delete("/executions/{execution_id}")
def delete_execution(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """删除执行记录"""
    try:
        success = task_execution_service.delete_execution(db, execution_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task execution not found")

        return {"message": "Task execution deleted successfully"}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete execution: {str(e)}"
        )


@router.get("/executions/{execution_id}/logs")
def get_execution_logs(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取执行日志"""
    try:
        logs = task_execution_service.get_execution_logs(db, execution_id)
        return logs

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get execution logs: {str(e)}"
        )


@router.post("/executions/{execution_id}/retry")
def retry_execution(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """重试失败的执行"""
    try:
        retry_execution = task_execution_service.retry_failed_execution(
            db, execution_id
        )
        if not retry_execution:
            raise HTTPException(status_code=404, detail="Task execution not found")

        return {
            "message": "Task execution retry started",
            "retry_execution_id": retry_execution.id,
            "status": retry_execution.status,
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to retry execution: {str(e)}"
        )


@router.get("/executions/trends")
def get_execution_trends(
    task_id: Optional[int] = Query(None, description="任务ID"),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取执行趋势数据"""
    try:
        trends = task_execution_service.get_execution_trends(db, task_id, days)
        return trends

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get execution trends: {str(e)}"
        )


@router.get("/{task_id}/execution-summary")
def get_task_execution_summary(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取任务执行摘要"""
    try:
        summary = task_execution_service.get_task_execution_summary(db, task_id)
        return summary

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get task execution summary: {str(e)}"
        )


@router.post("/executions/cleanup")
def cleanup_old_executions(
    days: int = Query(90, ge=1, le=365, description="清理多少天前的执行记录"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """清理旧的执行记录"""
    try:
        deleted_count = task_execution_service.cleanup_old_executions(db, days)
        return {
            "message": f"Successfully cleaned up {deleted_count} old execution records"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to cleanup old executions: {str(e)}"
        )
