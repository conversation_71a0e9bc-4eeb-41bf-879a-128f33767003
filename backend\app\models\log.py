"""
日志相关模型
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class LogAlert(Base):
    """日志告警规则模型"""

    __tablename__ = "log_alerts"

    id = Column(Integer, primary_key=True, index=True, comment="告警规则ID")
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    rule_name = Column(String(100), nullable=False, comment="规则名称")
    level = Column(String(20), nullable=False, comment="日志级别")
    pattern = Column(String(500), nullable=False, comment="匹配模式")
    threshold = Column(Integer, default=1, comment="触发阈值")
    time_window = Column(Integer, default=5, comment="时间窗口(分钟)")
    is_active = Column(<PERSON><PERSON><PERSON>, default=True, comment="是否激活")
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    # 关系
    app = relationship("App", back_populates="log_alerts")
    alert_records = relationship(
        "LogAlertRecord", back_populates="alert_rule", cascade="all, delete-orphan"
    )


class LogAlertRecord(Base):
    """日志告警记录模型"""

    __tablename__ = "log_alert_records"

    id = Column(Integer, primary_key=True, index=True, comment="告警记录ID")
    alert_rule_id = Column(
        Integer, ForeignKey("log_alerts.id"), nullable=False, comment="告警规则ID"
    )
    app_id = Column(Integer, ForeignKey("apps.id"), nullable=False, comment="应用ID")
    triggered_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="触发时间"
    )
    log_count = Column(Integer, nullable=False, comment="触发时的日志数量")
    message = Column(Text, nullable=True, comment="告警消息")
    is_resolved = Column(Boolean, default=False, comment="是否已解决")
    resolved_at = Column(DateTime(timezone=True), nullable=True, comment="解决时间")

    # 关系
    alert_rule = relationship("LogAlert", back_populates="alert_records")
    app = relationship("App", back_populates="alert_records")
