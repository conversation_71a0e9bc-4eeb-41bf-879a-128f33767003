"""
应用相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class AppBase(BaseModel):
    """应用基础模式"""

    name: str
    description: Optional[str] = None
    frontend_dir: Optional[str] = None
    backend_dir: Optional[str] = None
    frontend_start_cmd: Optional[str] = None
    backend_start_cmd: Optional[str] = None
    frontend_stop_cmd: Optional[str] = None
    backend_stop_cmd: Optional[str] = None
    frontend_port: Optional[int] = None
    backend_port: Optional[int] = None
    is_active: bool = True


class AppCreate(AppBase):
    """应用创建模式"""

    pass


class AppUpdate(BaseModel):
    """应用更新模式"""

    name: Optional[str] = None
    description: Optional[str] = None
    frontend_dir: Optional[str] = None
    backend_dir: Optional[str] = None
    frontend_start_cmd: Optional[str] = None
    backend_start_cmd: Optional[str] = None
    frontend_stop_cmd: Optional[str] = None
    backend_stop_cmd: Optional[str] = None
    frontend_port: Optional[int] = None
    backend_port: Optional[int] = None
    is_active: Optional[bool] = None


class App(AppBase):
    """应用响应模式"""

    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class AppWithServices(App):
    """包含服务状态的应用模式"""

    frontend_status: str = "stopped"
    backend_status: str = "stopped"
    frontend_pid: Optional[int] = None
    backend_pid: Optional[int] = None


class AppConfigBase(BaseModel):
    """应用配置基础模式"""

    key: str
    value: Optional[str] = None
    description: Optional[str] = None


class AppConfigCreate(AppConfigBase):
    """应用配置创建模式"""

    pass


class AppConfigUpdate(BaseModel):
    """应用配置更新模式"""

    key: Optional[str] = None
    value: Optional[str] = None
    description: Optional[str] = None


class AppConfig(AppConfigBase):
    """应用配置响应模式"""

    id: int
    app_id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class AppLogBase(BaseModel):
    """应用日志基础模式"""

    service_type: str
    level: str
    message: str


class AppLog(AppLogBase):
    """应用日志响应模式"""

    id: int
    app_id: int
    timestamp: datetime

    model_config = {"from_attributes": True}


class AppServiceBase(BaseModel):
    """应用服务基础模式"""

    service_type: str
    status: str = "stopped"
    pid: Optional[int] = None
    port: Optional[int] = None
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None


class AppService(AppServiceBase):
    """应用服务响应模式"""

    id: int
    app_id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class ServiceControlRequest(BaseModel):
    """服务控制请求模式"""

    action: str  # start, stop, restart
    service_type: str  # frontend, backend


class AppStats(BaseModel):
    """应用统计模式"""

    total_apps: int
    running_apps: int
    stopped_apps: int
    error_apps: int
