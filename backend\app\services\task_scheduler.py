"""
任务调度器服务
"""
import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Callable, Dict, List, Optional

import schedule
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.models.task import Task, TaskDependency, TaskExecution
from app.services.task_executor import task_executor


class TaskScheduler:
    """任务调度器"""

    def __init__(self):
        self.logger = logging.getLogger("task_scheduler")
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.scheduled_jobs: Dict[int, schedule.Job] = {}
        self.interval_tasks: Dict[int, Dict] = {}
        self.condition_triggers: Dict[int, Callable] = {}

    def start(self):
        """启动调度器"""
        if self.is_running:
            return

        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()

        # 加载现有的调度任务
        self._load_scheduled_tasks()

        self.logger.info("Task scheduler started")

    def stop(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)

        # 清理所有调度任务
        schedule.clear()
        self.scheduled_jobs.clear()
        self.interval_tasks.clear()
        self.condition_triggers.clear()

        self.logger.info("Task scheduler stopped")

    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.is_running:
            try:
                # 运行定时任务
                schedule.run_pending()

                # 检查间隔任务
                self._check_interval_tasks()

                # 检查条件触发器
                self._check_condition_triggers()

                # 清理已完成的执行
                self._cleanup_executions()

                # 等待1秒
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"Scheduler error: {str(e)}")
                time.sleep(5)  # 出错时等待更长时间

    def _load_scheduled_tasks(self):
        """加载数据库中的调度任务"""
        try:
            db = SessionLocal()
            try:
                # 获取所有激活的调度任务
                tasks = (
                    db.query(Task)
                    .filter(Task.is_active == True, Task.schedule_type != "manual")
                    .all()
                )

                for task in tasks:
                    self.schedule_task(task)

                self.logger.info(f"Loaded {len(tasks)} scheduled tasks")

            finally:
                db.close()

        except Exception as e:
            self.logger.error(f"Failed to load scheduled tasks: {str(e)}")

    def schedule_task(self, task: Task):
        """调度单个任务"""
        try:
            # 移除现有的调度（如果存在）
            self.unschedule_task(task.id)

            if not task.is_active:
                return

            if task.schedule_type == "cron":
                self._schedule_cron_task(task)
            elif task.schedule_type == "interval":
                self._schedule_interval_task(task)

            self.logger.info(f"Scheduled task: {task.name} ({task.schedule_type})")

        except Exception as e:
            self.logger.error(f"Failed to schedule task {task.id}: {str(e)}")

    def unschedule_task(self, task_id: int):
        """取消任务调度"""
        try:
            # 移除定时任务
            if task_id in self.scheduled_jobs:
                schedule.cancel_job(self.scheduled_jobs[task_id])
                del self.scheduled_jobs[task_id]

            # 移除间隔任务
            if task_id in self.interval_tasks:
                del self.interval_tasks[task_id]

            # 移除条件触发器
            if task_id in self.condition_triggers:
                del self.condition_triggers[task_id]

            self.logger.info(f"Unscheduled task: {task_id}")

        except Exception as e:
            self.logger.error(f"Failed to unschedule task {task_id}: {str(e)}")

    def _schedule_cron_task(self, task: Task):
        """调度Cron任务"""
        try:
            if not task.cron_expression:
                raise ValueError("Cron任务缺少cron表达式")

            # 解析cron表达式并创建调度
            # 这里简化处理，实际应该使用croniter库
            cron_parts = task.cron_expression.split()
            if len(cron_parts) != 5:
                raise ValueError("无效的cron表达式格式")

            minute, hour, day, month, weekday = cron_parts

            # 创建调度任务
            job = None

            # 简单的cron解析（仅支持基本格式）
            if minute == "*" and hour == "*":
                # 每分钟执行
                job = schedule.every().minute.do(self._execute_scheduled_task, task.id)
            elif hour == "*":
                # 每小时的指定分钟执行
                if minute.isdigit():
                    job = (
                        schedule.every()
                        .hour.at(f":{minute}")
                        .do(self._execute_scheduled_task, task.id)
                    )
            elif minute.isdigit() and hour.isdigit():
                # 每天的指定时间执行
                job = (
                    schedule.every()
                    .day.at(f"{hour}:{minute}")
                    .do(self._execute_scheduled_task, task.id)
                )

            if job:
                self.scheduled_jobs[task.id] = job
            else:
                # 对于复杂的cron表达式，使用间隔检查
                self.interval_tasks[task.id] = {
                    "cron_expression": task.cron_expression,
                    "last_check": datetime.now(),
                    "next_run": self._calculate_next_cron_run(task.cron_expression),
                }

        except Exception as e:
            self.logger.error(f"Failed to schedule cron task {task.id}: {str(e)}")
            raise

    def _schedule_interval_task(self, task: Task):
        """调度间隔任务"""
        try:
            if not task.interval_seconds:
                raise ValueError("间隔任务缺少间隔时间")

            # 记录间隔任务信息
            self.interval_tasks[task.id] = {
                "interval_seconds": task.interval_seconds,
                "last_run": task.last_run_at or datetime.now(),
                "next_run": (task.last_run_at or datetime.now())
                + timedelta(seconds=task.interval_seconds),
            }

        except Exception as e:
            self.logger.error(f"Failed to schedule interval task {task.id}: {str(e)}")
            raise

    def _check_interval_tasks(self):
        """检查间隔任务"""
        now = datetime.now()

        for task_id, task_info in list(self.interval_tasks.items()):
            try:
                next_run = task_info.get("next_run")
                if next_run and now >= next_run:
                    # 执行任务
                    self._execute_scheduled_task(task_id)

                    # 更新下次执行时间
                    if "interval_seconds" in task_info:
                        task_info["last_run"] = now
                        task_info["next_run"] = now + timedelta(
                            seconds=task_info["interval_seconds"]
                        )
                    elif "cron_expression" in task_info:
                        task_info["next_run"] = self._calculate_next_cron_run(
                            task_info["cron_expression"]
                        )

            except Exception as e:
                self.logger.error(f"Error checking interval task {task_id}: {str(e)}")

    def _check_condition_triggers(self):
        """检查条件触发器"""
        for task_id, trigger_func in list(self.condition_triggers.items()):
            try:
                if trigger_func():
                    self._execute_scheduled_task(task_id)

            except Exception as e:
                self.logger.error(
                    f"Error checking condition trigger {task_id}: {str(e)}"
                )

    def _execute_scheduled_task(self, task_id: int):
        """执行调度任务"""
        try:
            db = SessionLocal()
            try:
                task = db.query(Task).filter(Task.id == task_id).first()
                if not task:
                    self.logger.warning(
                        f"Task {task_id} not found for scheduled execution"
                    )
                    return

                if not task.is_active:
                    self.logger.info(
                        f"Task {task.name} is not active, skipping execution"
                    )
                    return

                # 检查是否有正在运行的执行
                running_execution = (
                    db.query(TaskExecution)
                    .filter(
                        TaskExecution.task_id == task_id,
                        TaskExecution.status == "running",
                    )
                    .first()
                )

                if running_execution:
                    self.logger.info(
                        f"Task {task.name} is already running, skipping execution"
                    )
                    return

                # 检查任务依赖
                if not self._check_task_dependencies(db, task_id):
                    self.logger.info(
                        f"Task {task.name} dependencies not satisfied, skipping execution"
                    )
                    return

                # 执行任务
                execution = task_executor.execute_task(db, task_id)

                # 更新任务的最后执行时间
                task.last_run_at = datetime.now()

                # 计算下次执行时间
                if task.schedule_type == "cron" and task.cron_expression:
                    task.next_run_at = self._calculate_next_cron_run(
                        task.cron_expression
                    )
                elif task.schedule_type == "interval" and task.interval_seconds:
                    task.next_run_at = datetime.now() + timedelta(
                        seconds=task.interval_seconds
                    )

                db.commit()

                self.logger.info(
                    f"Scheduled execution started for task {task.name} (execution_id: {execution.id})"
                )

            finally:
                db.close()

        except Exception as e:
            self.logger.error(f"Failed to execute scheduled task {task_id}: {str(e)}")

    def _check_task_dependencies(self, db: Session, task_id: int) -> bool:
        """检查任务依赖"""
        try:
            # 获取任务的所有依赖
            dependencies = (
                db.query(TaskDependency)
                .filter(
                    TaskDependency.task_id == task_id, TaskDependency.is_active == True
                )
                .all()
            )

            if not dependencies:
                return True  # 没有依赖，可以执行

            for dependency in dependencies:
                # 获取依赖任务的最近执行记录
                latest_execution = (
                    db.query(TaskExecution)
                    .filter(TaskExecution.task_id == dependency.depends_on_task_id)
                    .order_by(TaskExecution.created_at.desc())
                    .first()
                )

                if not latest_execution:
                    # 依赖任务从未执行过
                    if dependency.dependency_type != "always":
                        return False
                    continue

                # 检查依赖条件
                if dependency.dependency_type == "success":
                    if latest_execution.status != "success":
                        return False
                elif dependency.dependency_type == "failure":
                    if latest_execution.status != "failed":
                        return False
                # "always" 类型的依赖总是满足

            return True

        except Exception as e:
            self.logger.error(
                f"Failed to check task dependencies for {task_id}: {str(e)}"
            )
            return False

    def _calculate_next_cron_run(self, cron_expression: str) -> Optional[datetime]:
        """计算下次cron执行时间"""
        try:
            from croniter import croniter

            cron = croniter(cron_expression, datetime.now())
            return cron.get_next(datetime)
        except ImportError:
            # 如果没有croniter库，返回1小时后
            return datetime.now() + timedelta(hours=1)
        except Exception as e:
            self.logger.error(f"Failed to calculate next cron run: {str(e)}")
            return None

    def _cleanup_executions(self):
        """清理执行记录"""
        try:
            task_executor.cleanup_finished_executions(SessionLocal())
        except Exception as e:
            self.logger.error(f"Failed to cleanup executions: {str(e)}")

    def add_condition_trigger(self, task_id: int, condition_func: Callable[[], bool]):
        """添加条件触发器"""
        self.condition_triggers[task_id] = condition_func
        self.logger.info(f"Added condition trigger for task {task_id}")

    def remove_condition_trigger(self, task_id: int):
        """移除条件触发器"""
        if task_id in self.condition_triggers:
            del self.condition_triggers[task_id]
            self.logger.info(f"Removed condition trigger for task {task_id}")

    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        return {
            "is_running": self.is_running,
            "scheduled_jobs_count": len(self.scheduled_jobs),
            "interval_tasks_count": len(self.interval_tasks),
            "condition_triggers_count": len(self.condition_triggers),
            "running_executions": task_executor.get_running_executions(),
        }

    def get_next_scheduled_tasks(self, limit: int = 10) -> List[Dict]:
        """获取即将执行的任务"""
        try:
            db = SessionLocal()
            try:
                tasks = (
                    db.query(Task)
                    .filter(
                        Task.is_active == True,
                        Task.schedule_type != "manual",
                        Task.next_run_at.isnot(None),
                    )
                    .order_by(Task.next_run_at)
                    .limit(limit)
                    .all()
                )

                result = []
                for task in tasks:
                    result.append(
                        {
                            "task_id": task.id,
                            "task_name": task.name,
                            "schedule_type": task.schedule_type,
                            "next_run_at": task.next_run_at.isoformat()
                            if task.next_run_at
                            else None,
                            "last_run_at": task.last_run_at.isoformat()
                            if task.last_run_at
                            else None,
                        }
                    )

                return result

            finally:
                db.close()

        except Exception as e:
            self.logger.error(f"Failed to get next scheduled tasks: {str(e)}")
            return []


class TaskTriggerService:
    """任务触发器服务"""

    def __init__(self, scheduler: TaskScheduler):
        self.scheduler = scheduler
        self.logger = logging.getLogger("task_trigger")

    def create_file_watcher_trigger(
        self, task_id: int, file_path: str, check_interval: int = 60
    ):
        """创建文件监控触发器"""
        import os

        last_modified = {}

        def check_file_modified():
            try:
                if os.path.exists(file_path):
                    current_mtime = os.path.getmtime(file_path)
                    if file_path not in last_modified:
                        last_modified[file_path] = current_mtime
                        return False

                    if current_mtime > last_modified[file_path]:
                        last_modified[file_path] = current_mtime
                        return True

                return False

            except Exception as e:
                self.logger.error(f"Error checking file {file_path}: {str(e)}")
                return False

        self.scheduler.add_condition_trigger(task_id, check_file_modified)
        self.logger.info(
            f"Created file watcher trigger for task {task_id}: {file_path}"
        )

    def create_log_pattern_trigger(self, task_id: int, log_file: str, pattern: str):
        """创建日志模式触发器"""
        import re

        last_position = {}

        def check_log_pattern():
            try:
                if not os.path.exists(log_file):
                    return False

                with open(log_file, "r", encoding="utf-8") as f:
                    # 获取上次读取位置
                    if log_file not in last_position:
                        f.seek(0, 2)  # 移动到文件末尾
                        last_position[log_file] = f.tell()
                        return False

                    f.seek(last_position[log_file])
                    new_content = f.read()
                    last_position[log_file] = f.tell()

                    # 检查模式匹配
                    if re.search(pattern, new_content, re.IGNORECASE):
                        return True

                return False

            except Exception as e:
                self.logger.error(f"Error checking log pattern in {log_file}: {str(e)}")
                return False

        self.scheduler.add_condition_trigger(task_id, check_log_pattern)
        self.logger.info(f"Created log pattern trigger for task {task_id}: {pattern}")

    def create_http_endpoint_trigger(
        self, task_id: int, url: str, expected_status: int = 200
    ):
        """创建HTTP端点监控触发器"""
        import requests

        last_status = {}

        def check_http_endpoint():
            try:
                response = requests.get(url, timeout=10)
                current_status = response.status_code

                if url not in last_status:
                    last_status[url] = current_status
                    return False

                # 如果状态从非预期变为预期，触发任务
                if (
                    last_status[url] != expected_status
                    and current_status == expected_status
                ):
                    last_status[url] = current_status
                    return True

                last_status[url] = current_status
                return False

            except Exception as e:
                self.logger.error(f"Error checking HTTP endpoint {url}: {str(e)}")
                return False

        self.scheduler.add_condition_trigger(task_id, check_http_endpoint)
        self.logger.info(f"Created HTTP endpoint trigger for task {task_id}: {url}")


# 全局调度器实例
task_scheduler = TaskScheduler()
task_trigger_service = TaskTriggerService(task_scheduler)
