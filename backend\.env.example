# 环境变量配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# 基本配置
PROJECT_NAME=应用项目管理系统
VERSION=1.0.0
API_V1_STR=/api/v1
ENVIRONMENT=development
DEBUG=false

# 数据库配置
# 生产环境请使用强密码并限制访问
DATABASE_URL=postgresql://username:password@localhost:5432/app_manager
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# JWT配置
# 生产环境必须使用强密钥（至少32字符）
SECRET_KEY=your-very-long-secret-key-change-in-production-minimum-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS配置
# 生产环境请限制为实际的前端域名
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 应用管理配置
APPS_BASE_DIR=/opt/apps
MAX_LOG_SIZE=104857600
LOG_BACKUP_COUNT=5

# 安全配置
# 生产环境建议启用
ENABLE_RATE_LIMITING=false
RATE_LIMIT_PER_MINUTE=60

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090