"""
系统监控服务单元测试
"""
import asyncio
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from app.services.system_monitor import SystemMonitor


class TestSystemMonitor:
    """系统监控服务测试类"""

    @pytest.fixture
    def monitor(self):
        """创建系统监控实例"""
        return SystemMonitor()

    @patch("psutil.cpu_percent")
    @patch("psutil.cpu_count")
    @patch("psutil.virtual_memory")
    @patch("psutil.disk_usage")
    @patch("psutil.boot_time")
    @patch("time.time")
    def test_get_system_metrics(
        self,
        mock_time,
        mock_boot_time,
        mock_disk_usage,
        mock_virtual_memory,
        mock_cpu_count,
        mock_cpu_percent,
        monitor,
    ):
        """测试获取系统指标"""
        # 设置模拟返回值
        mock_cpu_percent.return_value = 45.5
        mock_cpu_count.return_value = 4

        mock_memory = Mock()
        mock_memory.total = 8589934592  # 8GB
        mock_memory.used = 4294967296  # 4GB
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.total = 107374182400  # 100GB
        mock_disk.used = 53687091200  # 50GB
        mock_disk.percent = 50.0
        mock_disk_usage.return_value = mock_disk

        mock_boot_time.return_value = 1000000000
        mock_time.return_value = 1000086400  # 24小时后

        # 异步测试
        async def test_async():
            metrics = await monitor.get_system_metrics()

            assert metrics["cpu_percent"] == 45.5
            assert metrics["cpu_count"] == 4
            assert metrics["memory_total"] == 8589934592
            assert metrics["memory_used"] == 4294967296
            assert metrics["memory_percent"] == 50.0
            assert metrics["disk_total"] == 107374182400
            assert metrics["disk_used"] == 53687091200
            assert metrics["disk_percent"] == 50.0
            assert metrics["uptime"] == 86400  # 24小时

        asyncio.run(test_async())

    @patch("psutil.process_iter")
    def test_get_process_list(self, mock_process_iter, monitor):
        """测试获取进程列表"""
        # 创建模拟进程
        mock_proc1 = Mock()
        mock_proc1.info = {
            "pid": 1234,
            "name": "python",
            "cpu_percent": 15.5,
            "memory_percent": 8.2,
            "status": "running",
        }

        mock_proc2 = Mock()
        mock_proc2.info = {
            "pid": 5678,
            "name": "node",
            "cpu_percent": 12.3,
            "memory_percent": 6.1,
            "status": "running",
        }

        mock_process_iter.return_value = [mock_proc1, mock_proc2]

        processes = monitor.get_process_list()

        assert len(processes) == 2
        assert processes[0]["pid"] == 1234
        assert processes[0]["name"] == "python"
        assert processes[0]["cpu_percent"] == 15.5
        assert processes[1]["pid"] == 5678
        assert processes[1]["name"] == "node"

    @patch("psutil.process_iter")
    def test_get_process_list_with_exception(self, mock_process_iter, monitor):
        """测试获取进程列表时发生异常"""
        # 创建一个正常进程和一个异常进程
        mock_proc1 = Mock()
        mock_proc1.info = {
            "pid": 1234,
            "name": "python",
            "cpu_percent": 15.5,
            "memory_percent": 8.2,
            "status": "running",
        }

        mock_proc2 = Mock()
        mock_proc2.side_effect = Exception("Process access denied")

        mock_process_iter.return_value = [mock_proc1, mock_proc2]

        processes = monitor.get_process_list()

        # 应该只返回正常的进程，异常的进程被跳过
        assert len(processes) == 1
        assert processes[0]["pid"] == 1234

    @patch("psutil.net_connections")
    def test_get_network_connections(self, mock_net_connections, monitor):
        """测试获取网络连接"""
        # 创建模拟连接
        mock_conn1 = Mock()
        mock_conn1.laddr = Mock()
        mock_conn1.laddr.ip = "127.0.0.1"
        mock_conn1.laddr.port = 8000
        mock_conn1.raddr = Mock()
        mock_conn1.raddr.ip = "127.0.0.1"
        mock_conn1.raddr.port = 54321
        mock_conn1.status = "ESTABLISHED"
        mock_conn1.pid = 1234

        mock_conn2 = Mock()
        mock_conn2.laddr = Mock()
        mock_conn2.laddr.ip = "127.0.0.1"
        mock_conn2.laddr.port = 3000
        mock_conn2.raddr = None  # 监听状态
        mock_conn2.status = "LISTEN"
        mock_conn2.pid = 5678

        mock_net_connections.return_value = [mock_conn1, mock_conn2]

        connections = monitor.get_network_connections()

        assert len(connections) == 2
        assert connections[0]["local_address"] == "127.0.0.1:8000"
        assert connections[0]["remote_address"] == "127.0.0.1:54321"
        assert connections[0]["status"] == "ESTABLISHED"
        assert connections[0]["pid"] == 1234

        assert connections[1]["local_address"] == "127.0.0.1:3000"
        assert connections[1]["remote_address"] is None
        assert connections[1]["status"] == "LISTEN"
        assert connections[1]["pid"] == 5678

    @patch("psutil.disk_partitions")
    @patch("psutil.disk_usage")
    def test_get_disk_partitions(self, mock_disk_usage, mock_disk_partitions, monitor):
        """测试获取磁盘分区"""
        # 创建模拟分区
        mock_partition = Mock()
        mock_partition.device = "/dev/sda1"
        mock_partition.mountpoint = "/"
        mock_partition.fstype = "ext4"

        mock_disk_partitions.return_value = [mock_partition]

        # 创建模拟磁盘使用情况
        mock_usage = Mock()
        mock_usage.total = 107374182400
        mock_usage.used = 53687091200
        mock_usage.free = 53687091200
        mock_usage.percent = 50.0

        mock_disk_usage.return_value = mock_usage

        partitions = monitor.get_disk_partitions()

        assert len(partitions) == 1
        assert partitions[0]["device"] == "/dev/sda1"
        assert partitions[0]["mountpoint"] == "/"
        assert partitions[0]["fstype"] == "ext4"
        assert partitions[0]["total"] == 107374182400
        assert partitions[0]["used"] == 53687091200
        assert partitions[0]["free"] == 53687091200
        assert partitions[0]["percent"] == 50.0

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    @patch("psutil.disk_usage")
    def test_analyze_resource_usage_normal(
        self, mock_disk_usage, mock_virtual_memory, mock_cpu_percent, monitor
    ):
        """测试资源使用分析 - 正常状态"""
        # 设置正常使用率
        mock_cpu_percent.return_value = 45.0

        mock_memory = Mock()
        mock_memory.percent = 50.0
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.percent = 40.0
        mock_disk_usage.return_value = mock_disk

        analysis = monitor.analyze_resource_usage()

        assert analysis["cpu_status"] == "normal"
        assert analysis["memory_status"] == "normal"
        assert analysis["disk_status"] == "normal"
        assert analysis["overall_status"] == "normal"
        assert len(analysis["recommendations"]) > 0

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    @patch("psutil.disk_usage")
    def test_analyze_resource_usage_high(
        self, mock_disk_usage, mock_virtual_memory, mock_cpu_percent, monitor
    ):
        """测试资源使用分析 - 高使用率"""
        # 设置高使用率
        mock_cpu_percent.return_value = 85.0

        mock_memory = Mock()
        mock_memory.percent = 90.0
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.percent = 95.0
        mock_disk_usage.return_value = mock_disk

        analysis = monitor.analyze_resource_usage()

        assert analysis["cpu_status"] == "high"
        assert analysis["memory_status"] == "critical"
        assert analysis["disk_status"] == "critical"
        assert analysis["overall_status"] == "critical"
        assert len(analysis["recommendations"]) > 0
        assert any("CPU使用率过高" in rec for rec in analysis["recommendations"])
        assert any("内存使用率过高" in rec for rec in analysis["recommendations"])
        assert any("磁盘使用率过高" in rec for rec in analysis["recommendations"])

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    @patch("psutil.disk_usage")
    def test_analyze_resource_usage_warning(
        self, mock_disk_usage, mock_virtual_memory, mock_cpu_percent, monitor
    ):
        """测试资源使用分析 - 警告状态"""
        # 设置警告级别使用率
        mock_cpu_percent.return_value = 75.0

        mock_memory = Mock()
        mock_memory.percent = 80.0
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.percent = 85.0
        mock_disk_usage.return_value = mock_disk

        analysis = monitor.analyze_resource_usage()

        assert analysis["cpu_status"] == "warning"
        assert analysis["memory_status"] == "warning"
        assert analysis["disk_status"] == "warning"
        assert analysis["overall_status"] == "warning"

    def test_start_monitoring(self, monitor):
        """测试开始监控"""
        assert monitor.monitoring is False

        monitor.start_monitoring()

        assert monitor.monitoring is True
        assert monitor.monitor_task is not None

    def test_stop_monitoring(self, monitor):
        """测试停止监控"""
        # 先开始监控
        monitor.start_monitoring()
        assert monitor.monitoring is True

        # 停止监控
        monitor.stop_monitoring()

        assert monitor.monitoring is False
        assert monitor.monitor_task is None

    def test_get_metrics_history_empty(self, monitor):
        """测试获取空的指标历史"""
        history = monitor.get_metrics_history(hours=1)
        assert history == []

    def test_get_metrics_history_with_data(self, monitor):
        """测试获取有数据的指标历史"""
        # 添加一些历史数据
        now = datetime.now()
        monitor.metrics_history = [
            {
                "timestamp": now - timedelta(minutes=30),
                "cpu_percent": 45.0,
                "memory_percent": 50.0,
            },
            {
                "timestamp": now - timedelta(minutes=60),
                "cpu_percent": 40.0,
                "memory_percent": 45.0,
            },
            {
                "timestamp": now - timedelta(hours=2),  # 超出范围
                "cpu_percent": 35.0,
                "memory_percent": 40.0,
            },
        ]

        history = monitor.get_metrics_history(hours=1)

        # 应该只返回1小时内的数据
        assert len(history) == 2
        assert all(
            (now - item["timestamp"]).total_seconds() <= 3600 for item in history
        )

    @patch("asyncio.sleep")
    async def test_monitor_loop(self, mock_sleep, monitor):
        """测试监控循环"""
        # 设置监控间隔为很短的时间以便测试
        monitor.monitoring_interval = 0.1

        # 模拟监控运行一次后停止
        call_count = 0
        original_get_metrics = monitor.get_system_metrics

        async def mock_get_metrics():
            nonlocal call_count
            call_count += 1
            if call_count >= 2:  # 运行两次后停止
                monitor.monitoring = False
            return await original_get_metrics()

        monitor.get_system_metrics = mock_get_metrics

        # 启动监控
        monitor.monitoring = True
        await monitor._monitor_loop()

        # 验证监控被调用了
        assert call_count >= 1
        assert not monitor.monitoring

    def test_add_metrics_to_history(self, monitor):
        """测试添加指标到历史记录"""
        metrics = {
            "cpu_percent": 45.0,
            "memory_percent": 50.0,
            "timestamp": datetime.now(),
        }

        monitor._add_metrics_to_history(metrics)

        assert len(monitor.metrics_history) == 1
        assert monitor.metrics_history[0] == metrics

    def test_add_metrics_to_history_limit(self, monitor):
        """测试历史记录数量限制"""
        # 添加超过限制的记录
        for i in range(1500):  # 超过默认限制1000
            metrics = {"cpu_percent": float(i), "timestamp": datetime.now()}
            monitor._add_metrics_to_history(metrics)

        # 验证历史记录被限制在1000条
        assert len(monitor.metrics_history) == 1000
        # 验证保留的是最新的记录
        assert monitor.metrics_history[0]["cpu_percent"] == 1499.0

    def test_cleanup_old_metrics(self, monitor):
        """测试清理旧指标"""
        now = datetime.now()

        # 添加新旧混合的指标
        monitor.metrics_history = [
            {"timestamp": now - timedelta(minutes=30), "cpu_percent": 45.0},
            {"timestamp": now - timedelta(hours=25), "cpu_percent": 40.0},  # 超过24小时
            {"timestamp": now - timedelta(minutes=60), "cpu_percent": 50.0},
            {"timestamp": now - timedelta(hours=30), "cpu_percent": 35.0},  # 超过24小时
        ]

        monitor._cleanup_old_metrics()

        # 应该只保留24小时内的指标
        assert len(monitor.metrics_history) == 2
        assert all(
            (now - item["timestamp"]).total_seconds() <= 86400
            for item in monitor.metrics_history
        )
