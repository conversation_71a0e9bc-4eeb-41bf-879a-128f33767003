# 开发环境依赖
# 代码格式化和检查
black==24.10.0
flake8==7.1.1
isort==5.13.2
mypy==1.13.0

# 测试相关
pytest==8.3.4
pytest-asyncio==0.24.0
pytest-cov==6.0.0
pytest-mock==3.14.0
pytest-xdist==3.6.0

# 开发工具
pre-commit==4.0.1
bandit==1.8.0  # 安全检查
safety==3.2.11  # 依赖安全检查

# 文档生成
sphinx==8.1.3
sphinx-rtd-theme==3.0.2

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# API文档
httpx==0.28.1  # 用于API测试

# 类型检查相关
types-redis==4.6.0.20241004
types-requests==2.32.0.20241016
