"""
告警管理服务
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.system import AlertRule, SystemAlert
from app.schemas.system import (
    AlertQuery,
    AlertRuleCreate,
    AlertRuleUpdate,
    AlertSeverity,
    AlertStatus,
    SystemAlertCreate,
    SystemAlertUpdate,
)
from app.services.notification_service import notification_service
from app.services.system_monitor import system_monitor


class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.evaluation_task = None
        self.evaluating = False
        self.alert_history = []

    async def start_evaluation(self, interval: int = 60):
        """开始告警评估"""
        if self.evaluating:
            return

        self.evaluating = True
        self.evaluation_task = asyncio.create_task(self._evaluation_loop(interval))

    async def stop_evaluation(self):
        """停止告警评估"""
        self.evaluating = False
        if self.evaluation_task:
            self.evaluation_task.cancel()
            try:
                await self.evaluation_task
            except asyncio.CancelledError:
                pass

    async def _evaluation_loop(self, interval: int):
        """告警评估循环"""
        while self.evaluating:
            try:
                await self.evaluate_all_rules()
                await asyncio.sleep(interval)

            except Exception as e:
                print(f"告警评估循环错误: {e}")
                await asyncio.sleep(interval)

    async def evaluate_all_rules(self):
        """评估所有告警规则"""
        db = next(get_db())
        try:
            # 获取所有激活的告警规则
            rules = db.query(AlertRule).filter(AlertRule.is_active == True).all()

            for rule in rules:
                await self.evaluate_rule(db, rule)

        finally:
            db.close()

    async def evaluate_rule(self, db: Session, rule: AlertRule):
        """评估单个告警规则"""
        try:
            # 获取当前系统指标
            current_metrics = system_monitor.get_current_metrics()

            if not current_metrics:
                return

            # 获取指标值
            metric_value = current_metrics.get(rule.metric_name)
            if metric_value is None:
                return

            # 评估条件
            is_triggered = self._evaluate_condition(
                metric_value, rule.threshold_value, rule.comparison_operator
            )

            # 更新规则的最后评估时间
            rule.last_evaluation = datetime.now()
            db.commit()

            if is_triggered:
                # 检查是否已存在活跃告警
                existing_alert = (
                    db.query(SystemAlert)
                    .filter(
                        and_(
                            SystemAlert.metric_name == rule.metric_name,
                            SystemAlert.status == AlertStatus.ACTIVE,
                            SystemAlert.threshold_value == rule.threshold_value,
                        )
                    )
                    .first()
                )

                if not existing_alert:
                    # 创建新告警
                    await self.create_alert(db, rule, metric_value)

            else:
                # 检查是否需要自动解决告警
                await self.auto_resolve_alerts(db, rule.metric_name)

        except Exception as e:
            print(f"评估告警规则错误 {rule.name}: {e}")

    def _evaluate_condition(
        self, current_value: float, threshold: float, operator: str
    ) -> bool:
        """评估告警条件"""
        if operator == ">":
            return current_value > threshold
        elif operator == "<":
            return current_value < threshold
        elif operator == ">=":
            return current_value >= threshold
        elif operator == "<=":
            return current_value <= threshold
        elif operator == "==":
            return current_value == threshold
        elif operator == "!=":
            return current_value != threshold
        else:
            return False

    async def create_alert(self, db: Session, rule: AlertRule, current_value: float):
        """创建告警"""
        try:
            alert_data = SystemAlertCreate(
                alert_type="system_metric",
                severity=rule.severity,
                title=f"{rule.name} - {rule.metric_name}告警",
                message=f"指标 {rule.metric_name} 当前值 {current_value} {rule.comparison_operator} 阈值 {rule.threshold_value}",
                metric_name=rule.metric_name,
                threshold_value=rule.threshold_value,
                comparison_operator=rule.comparison_operator,
                current_value=current_value,
                status=AlertStatus.ACTIVE,
                is_active=True,
            )

            alert = SystemAlert(**alert_data.dict())
            db.add(alert)
            db.commit()
            db.refresh(alert)

            # 更新规则的最后触发时间
            rule.last_triggered = datetime.now()
            db.commit()

            # 发送通知
            if rule.notification_enabled:
                await self.send_alert_notification(alert, rule)

            # 添加到历史记录
            self.alert_history.append(
                {
                    "alert_id": alert.id,
                    "rule_name": rule.name,
                    "metric_name": rule.metric_name,
                    "current_value": current_value,
                    "threshold_value": rule.threshold_value,
                    "severity": rule.severity,
                    "triggered_at": alert.triggered_at,
                    "status": "triggered",
                }
            )

            print(f"创建告警: {alert.title}")

        except Exception as e:
            print(f"创建告警错误: {e}")
            db.rollback()

    async def auto_resolve_alerts(self, db: Session, metric_name: str):
        """自动解决告警"""
        try:
            # 查找该指标的活跃告警
            active_alerts = (
                db.query(SystemAlert)
                .filter(
                    and_(
                        SystemAlert.metric_name == metric_name,
                        SystemAlert.status == AlertStatus.ACTIVE,
                    )
                )
                .all()
            )

            for alert in active_alerts:
                # 检查当前值是否仍然触发告警
                current_metrics = system_monitor.get_current_metrics()
                current_value = current_metrics.get(metric_name)

                if current_value is not None:
                    is_still_triggered = self._evaluate_condition(
                        current_value, alert.threshold_value, alert.comparison_operator
                    )

                    if not is_still_triggered:
                        # 自动解决告警
                        alert.status = AlertStatus.RESOLVED
                        alert.resolved_at = datetime.now()
                        alert.resolved_by = "system_auto"
                        alert.notes = "系统自动解决：指标值已恢复正常"

                        db.commit()

                        print(f"自动解决告警: {alert.title}")

        except Exception as e:
            print(f"自动解决告警错误: {e}")

    async def send_alert_notification(self, alert: SystemAlert, rule: AlertRule):
        """发送告警通知"""
        try:
            notification_data = {
                "type": "alert",
                "severity": alert.severity,
                "title": alert.title,
                "message": alert.message,
                "metric_name": alert.metric_name,
                "current_value": alert.current_value,
                "threshold_value": alert.threshold_value,
                "triggered_at": alert.triggered_at.isoformat(),
            }

            # 解析通知渠道配置
            channels = rule.notification_channels or {}

            # 发送邮件通知
            if channels.get("email", {}).get("enabled"):
                await notification_service.send_email_notification(
                    recipients=channels["email"].get("recipients", []),
                    subject=f"[告警] {alert.title}",
                    content=alert.message,
                    data=notification_data,
                )

            # 发送Webhook通知
            if channels.get("webhook", {}).get("enabled"):
                await notification_service.send_webhook_notification(
                    url=channels["webhook"].get("url"), data=notification_data
                )

            # 发送企业微信通知
            if channels.get("wechat", {}).get("enabled"):
                await notification_service.send_wechat_notification(
                    webhook_url=channels["wechat"].get("webhook_url"),
                    message=f"告警通知\n{alert.title}\n{alert.message}",
                    data=notification_data,
                )

        except Exception as e:
            print(f"发送告警通知错误: {e}")

    # CRUD操作
    def create_alert_rule(
        self, db: Session, rule_data: AlertRuleCreate, created_by: str
    ) -> AlertRule:
        """创建告警规则"""
        try:
            # 验证指标名称
            if not self._validate_metric_name(rule_data.metric_name):
                raise ValueError(f"不支持的指标名称: {rule_data.metric_name}")

            rule = AlertRule(**rule_data.dict(), created_by=created_by)

            db.add(rule)
            db.commit()
            db.refresh(rule)

            return rule

        except Exception as e:
            db.rollback()
            raise e

    def get_alert_rules(
        self, db: Session, is_active: Optional[bool] = None
    ) -> List[AlertRule]:
        """获取告警规则列表"""
        query = db.query(AlertRule)

        if is_active is not None:
            query = query.filter(AlertRule.is_active == is_active)

        return query.order_by(AlertRule.created_at.desc()).all()

    def get_alert_rule(self, db: Session, rule_id: int) -> Optional[AlertRule]:
        """获取单个告警规则"""
        return db.query(AlertRule).filter(AlertRule.id == rule_id).first()

    def update_alert_rule(
        self, db: Session, rule_id: int, rule_data: AlertRuleUpdate
    ) -> Optional[AlertRule]:
        """更新告警规则"""
        try:
            rule = self.get_alert_rule(db, rule_id)
            if not rule:
                return None

            update_data = rule_data.dict(exclude_unset=True)

            # 验证指标名称
            if "metric_name" in update_data:
                if not self._validate_metric_name(update_data["metric_name"]):
                    raise ValueError(f"不支持的指标名称: {update_data['metric_name']}")

            for field, value in update_data.items():
                setattr(rule, field, value)

            db.commit()
            db.refresh(rule)

            return rule

        except Exception as e:
            db.rollback()
            raise e

    def delete_alert_rule(self, db: Session, rule_id: int) -> bool:
        """删除告警规则"""
        try:
            rule = self.get_alert_rule(db, rule_id)
            if not rule:
                return False

            db.delete(rule)
            db.commit()

            return True

        except Exception as e:
            db.rollback()
            raise e

    def get_system_alerts(self, db: Session, query: AlertQuery) -> List[SystemAlert]:
        """获取系统告警列表"""
        db_query = db.query(SystemAlert)

        if query.severity:
            db_query = db_query.filter(SystemAlert.severity == query.severity)

        if query.status:
            db_query = db_query.filter(SystemAlert.status == query.status)

        if query.alert_type:
            db_query = db_query.filter(SystemAlert.alert_type == query.alert_type)

        if query.start_time:
            db_query = db_query.filter(SystemAlert.triggered_at >= query.start_time)

        if query.end_time:
            db_query = db_query.filter(SystemAlert.triggered_at <= query.end_time)

        return (
            db_query.order_by(SystemAlert.triggered_at.desc())
            .offset(query.skip)
            .limit(query.limit)
            .all()
        )

    def get_system_alert(self, db: Session, alert_id: int) -> Optional[SystemAlert]:
        """获取单个系统告警"""
        return db.query(SystemAlert).filter(SystemAlert.id == alert_id).first()

    def update_system_alert(
        self, db: Session, alert_id: int, alert_data: SystemAlertUpdate, updated_by: str
    ) -> Optional[SystemAlert]:
        """更新系统告警"""
        try:
            alert = self.get_system_alert(db, alert_id)
            if not alert:
                return None

            update_data = alert_data.dict(exclude_unset=True)

            # 处理状态变更
            if "status" in update_data:
                new_status = update_data["status"]

                if (
                    new_status == AlertStatus.ACKNOWLEDGED
                    and alert.status == AlertStatus.ACTIVE
                ):
                    alert.acknowledged_at = datetime.now()
                    alert.acknowledged_by = updated_by

                elif new_status == AlertStatus.RESOLVED:
                    alert.resolved_at = datetime.now()
                    alert.resolved_by = updated_by

            for field, value in update_data.items():
                setattr(alert, field, value)

            db.commit()
            db.refresh(alert)

            return alert

        except Exception as e:
            db.rollback()
            raise e

    def acknowledge_alert(
        self,
        db: Session,
        alert_id: int,
        acknowledged_by: str,
        notes: Optional[str] = None,
    ) -> Optional[SystemAlert]:
        """确认告警"""
        alert_data = SystemAlertUpdate(status=AlertStatus.ACKNOWLEDGED, notes=notes)
        return self.update_system_alert(db, alert_id, alert_data, acknowledged_by)

    def resolve_alert(
        self, db: Session, alert_id: int, resolved_by: str, notes: Optional[str] = None
    ) -> Optional[SystemAlert]:
        """解决告警"""
        alert_data = SystemAlertUpdate(status=AlertStatus.RESOLVED, notes=notes)
        return self.update_system_alert(db, alert_id, alert_data, resolved_by)

    def get_alert_statistics(self, db: Session, days: int = 30) -> Dict[str, Any]:
        """获取告警统计"""
        try:
            start_time = datetime.now() - timedelta(days=days)

            # 总告警数
            total_alerts = (
                db.query(SystemAlert)
                .filter(SystemAlert.triggered_at >= start_time)
                .count()
            )

            # 活跃告警数
            active_alerts = (
                db.query(SystemAlert)
                .filter(SystemAlert.status == AlertStatus.ACTIVE)
                .count()
            )

            # 严重告警数
            critical_alerts = (
                db.query(SystemAlert)
                .filter(
                    and_(
                        SystemAlert.severity == AlertSeverity.CRITICAL,
                        SystemAlert.triggered_at >= start_time,
                    )
                )
                .count()
            )

            # 按严重程度统计
            severity_stats = {}
            for severity in AlertSeverity:
                count = (
                    db.query(SystemAlert)
                    .filter(
                        and_(
                            SystemAlert.severity == severity,
                            SystemAlert.triggered_at >= start_time,
                        )
                    )
                    .count()
                )
                severity_stats[severity.value] = count

            # 按状态统计
            status_stats = {}
            for status in AlertStatus:
                count = (
                    db.query(SystemAlert)
                    .filter(
                        and_(
                            SystemAlert.status == status,
                            SystemAlert.triggered_at >= start_time,
                        )
                    )
                    .count()
                )
                status_stats[status.value] = count

            # 告警规则数
            alert_rules_count = (
                db.query(AlertRule).filter(AlertRule.is_active == True).count()
            )

            return {
                "total_alerts": total_alerts,
                "active_alerts": active_alerts,
                "critical_alerts": critical_alerts,
                "alert_rules_count": alert_rules_count,
                "severity_stats": severity_stats,
                "status_stats": status_stats,
                "period_days": days,
            }

        except Exception as e:
            print(f"获取告警统计错误: {e}")
            return {}

    def _validate_metric_name(self, metric_name: str) -> bool:
        """验证指标名称"""
        valid_metrics = [
            "cpu_percent",
            "memory_percent",
            "disk_percent",
            "load_avg_1",
            "load_avg_5",
            "load_avg_15",
            "network_bytes_sent",
            "network_bytes_recv",
            "disk_read_bytes",
            "disk_write_bytes",
            "process_count",
            "uptime",
        ]
        return metric_name in valid_metrics

    def get_available_metrics(self) -> List[Dict[str, str]]:
        """获取可用的监控指标"""
        return [
            {"name": "cpu_percent", "label": "CPU使用率(%)", "unit": "%"},
            {"name": "memory_percent", "label": "内存使用率(%)", "unit": "%"},
            {"name": "disk_percent", "label": "磁盘使用率(%)", "unit": "%"},
            {"name": "load_avg_1", "label": "1分钟平均负载", "unit": ""},
            {"name": "load_avg_5", "label": "5分钟平均负载", "unit": ""},
            {"name": "load_avg_15", "label": "15分钟平均负载", "unit": ""},
            {"name": "network_bytes_sent", "label": "网络发送字节数", "unit": "bytes"},
            {"name": "network_bytes_recv", "label": "网络接收字节数", "unit": "bytes"},
            {"name": "disk_read_bytes", "label": "磁盘读取字节数", "unit": "bytes"},
            {"name": "disk_write_bytes", "label": "磁盘写入字节数", "unit": "bytes"},
            {"name": "process_count", "label": "进程数量", "unit": "个"},
            {"name": "uptime", "label": "系统运行时间", "unit": "秒"},
        ]


# 全局告警管理器实例
alert_manager = AlertManager()
