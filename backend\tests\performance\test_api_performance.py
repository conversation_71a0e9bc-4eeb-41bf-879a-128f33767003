"""
API性能测试
"""
import asyncio
import statistics
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.auth import create_access_token
from app.main import app
from app.models.app import App
from app.models.user import User


@pytest.mark.performance
class TestAPIPerformance:
    """API性能测试类"""

    def setup_test_data(self, db_session: Session):
        """设置测试数据"""
        # 创建测试用户
        user = User(
            username="perfuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        # 创建测试应用
        apps = []
        for i in range(100):
            app = App(
                name=f"Perf App {i}",
                description=f"Performance test app {i}",
                created_by=user.id,
            )
            apps.append(app)

        db_session.add_all(apps)
        db_session.commit()

        return user, apps

    def test_app_list_performance(self, client: TestClient, db_session: Session):
        """测试应用列表API性能"""
        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 预热
        client.get("/api/v1/apps/", headers=headers)

        # 性能测试
        response_times = []
        for _ in range(10):
            start_time = time.time()
            response = client.get("/api/v1/apps/", headers=headers)
            end_time = time.time()

            assert response.status_code == 200
            response_times.append(end_time - start_time)

        # 性能断言
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)

        assert avg_time < 0.5, f"平均响应时间 {avg_time:.3f}s 超过 0.5s"
        assert max_time < 1.0, f"最大响应时间 {max_time:.3f}s 超过 1.0s"

        print(f"应用列表API性能: 平均 {avg_time:.3f}s, 最大 {max_time:.3f}s")

    def test_concurrent_requests_performance(
        self, client: TestClient, db_session: Session
    ):
        """测试并发请求性能"""
        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        def make_request():
            start_time = time.time()
            response = client.get("/api/v1/apps/", headers=headers)
            end_time = time.time()
            return response.status_code, end_time - start_time

        # 并发测试
        concurrent_users = 10
        requests_per_user = 5

        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []

            start_time = time.time()

            for _ in range(concurrent_users * requests_per_user):
                future = executor.submit(make_request)
                futures.append(future)

            results = []
            for future in as_completed(futures):
                status_code, response_time = future.result()
                results.append((status_code, response_time))

            total_time = time.time() - start_time

        # 分析结果
        successful_requests = [r for r in results if r[0] == 200]
        response_times = [r[1] for r in successful_requests]

        success_rate = len(successful_requests) / len(results)
        avg_response_time = statistics.mean(response_times)
        throughput = len(successful_requests) / total_time

        # 性能断言
        assert success_rate >= 0.95, f"成功率 {success_rate:.2%} 低于 95%"
        assert avg_response_time < 1.0, f"平均响应时间 {avg_response_time:.3f}s 超过 1.0s"
        assert throughput >= 10, f"吞吐量 {throughput:.1f} req/s 低于 10 req/s"

        print(
            f"并发性能: 成功率 {success_rate:.2%}, 平均响应时间 {avg_response_time:.3f}s, 吞吐量 {throughput:.1f} req/s"
        )

    def test_database_query_performance(self, client: TestClient, db_session: Session):
        """测试数据库查询性能"""
        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 测试分页查询性能
        page_sizes = [10, 50, 100]

        for page_size in page_sizes:
            response_times = []

            for page in range(5):  # 测试前5页
                start_time = time.time()
                response = client.get(
                    f"/api/v1/apps/?skip={page * page_size}&limit={page_size}",
                    headers=headers,
                )
                end_time = time.time()

                assert response.status_code == 200
                response_times.append(end_time - start_time)

            avg_time = statistics.mean(response_times)
            assert avg_time < 0.3, f"页面大小 {page_size} 的平均查询时间 {avg_time:.3f}s 超过 0.3s"

            print(f"分页查询性能 (页面大小 {page_size}): 平均 {avg_time:.3f}s")

    def test_memory_usage_performance(self, client: TestClient, db_session: Session):
        """测试内存使用性能"""
        import os

        import psutil

        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        process = psutil.Process(os.getpid())

        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 执行大量请求
        for _ in range(100):
            response = client.get("/api/v1/apps/", headers=headers)
            assert response.status_code == 200

        # 记录最终内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长不应超过50MB
        assert memory_increase < 50, f"内存增长 {memory_increase:.1f}MB 超过 50MB"

        print(
            f"内存使用: 初始 {initial_memory:.1f}MB, 最终 {final_memory:.1f}MB, 增长 {memory_increase:.1f}MB"
        )

    def test_response_size_performance(self, client: TestClient, db_session: Session):
        """测试响应大小性能"""
        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        response = client.get("/api/v1/apps/", headers=headers)
        assert response.status_code == 200

        response_size = len(response.content)
        response_size_kb = response_size / 1024

        # 响应大小不应超过1MB
        assert response_size_kb < 1024, f"响应大小 {response_size_kb:.1f}KB 超过 1MB"

        print(f"响应大小: {response_size_kb:.1f}KB")

    def test_cache_performance(self, client: TestClient, db_session: Session):
        """测试缓存性能"""
        user, apps = self.setup_test_data(db_session)
        token = create_access_token(data={"sub": user.username})
        headers = {"Authorization": f"Bearer {token}"}

        # 第一次请求（无缓存）
        start_time = time.time()
        response1 = client.get("/api/v1/system/metrics", headers=headers)
        first_request_time = time.time() - start_time

        assert response1.status_code == 200

        # 第二次请求（可能有缓存）
        start_time = time.time()
        response2 = client.get("/api/v1/system/metrics", headers=headers)
        second_request_time = time.time() - start_time

        assert response2.status_code == 200

        # 如果有缓存，第二次请求应该更快
        if second_request_time < first_request_time * 0.8:
            print(
                f"缓存有效: 第一次 {first_request_time:.3f}s, 第二次 {second_request_time:.3f}s"
            )
        else:
            print(
                f"缓存可能无效: 第一次 {first_request_time:.3f}s, 第二次 {second_request_time:.3f}s"
            )


@pytest.mark.performance
class TestWebSocketPerformance:
    """WebSocket性能测试类"""

    @pytest.mark.asyncio
    async def test_websocket_connection_performance(self):
        """测试WebSocket连接性能"""
        from unittest.mock import AsyncMock, Mock

        from app.websocket.connection_manager import ConnectionManager

        manager = ConnectionManager()

        # 模拟WebSocket连接
        mock_websockets = []
        for i in range(100):
            mock_ws = Mock()
            mock_ws.accept = AsyncMock()
            mock_ws.send_text = AsyncMock()
            mock_websockets.append(mock_ws)

        # 测试连接建立性能
        start_time = time.time()

        connection_ids = []
        for i, mock_ws in enumerate(mock_websockets):
            connection_id = await manager.connect(mock_ws, i % 10)  # 10个应用
            connection_ids.append(connection_id)

        connection_time = time.time() - start_time

        # 连接建立时间应该合理
        assert connection_time < 1.0, f"建立100个连接耗时 {connection_time:.3f}s 超过 1.0s"

        # 测试消息广播性能
        message = {"type": "test", "data": "performance test"}

        start_time = time.time()

        for app_id in range(10):
            await manager.send_to_app(app_id, message)

        broadcast_time = time.time() - start_time

        # 广播时间应该合理
        assert broadcast_time < 0.5, f"广播消息耗时 {broadcast_time:.3f}s 超过 0.5s"

        print(f"WebSocket性能: 连接 {connection_time:.3f}s, 广播 {broadcast_time:.3f}s")


@pytest.mark.performance
class TestTaskExecutionPerformance:
    """任务执行性能测试类"""

    def test_task_execution_performance(self, db_session: Session):
        """测试任务执行性能"""
        from app.models.task import Task
        from app.services.task_execution_service import TaskExecutionService

        # 创建测试用户和应用
        user = User(
            username="perfuser",
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
        )
        db_session.add(user)
        db_session.commit()

        app = App(name="Perf App", created_by=user.id)
        db_session.add(app)
        db_session.commit()

        # 创建测试任务
        tasks = []
        for i in range(10):
            task = Task(
                app_id=app.id,
                name=f"Perf Task {i}",
                task_type="command",
                command="echo 'performance test'",
                created_by=user.id,
            )
            tasks.append(task)

        db_session.add_all(tasks)
        db_session.commit()

        service = TaskExecutionService(db_session)

        # 测试任务执行性能
        execution_times = []

        for task in tasks:
            start_time = time.time()
            execution = service.execute_task(task.id)
            end_time = time.time()

            assert execution.status in ["success", "failed"]
            execution_times.append(end_time - start_time)

        avg_execution_time = statistics.mean(execution_times)
        max_execution_time = max(execution_times)

        # 任务执行时间应该合理
        assert avg_execution_time < 2.0, f"平均任务执行时间 {avg_execution_time:.3f}s 超过 2.0s"
        assert max_execution_time < 5.0, f"最大任务执行时间 {max_execution_time:.3f}s 超过 5.0s"

        print(f"任务执行性能: 平均 {avg_execution_time:.3f}s, 最大 {max_execution_time:.3f}s")


@pytest.mark.performance
class TestSystemMonitoringPerformance:
    """系统监控性能测试类"""

    def test_system_metrics_collection_performance(self):
        """测试系统指标收集性能"""
        from app.services.system_monitor import SystemMonitor

        monitor = SystemMonitor()

        # 测试指标收集性能
        collection_times = []

        for _ in range(10):
            start_time = time.time()
            metrics = monitor.get_current_metrics()
            end_time = time.time()

            assert "cpu_percent" in metrics
            assert "memory_percent" in metrics
            collection_times.append(end_time - start_time)

        avg_collection_time = statistics.mean(collection_times)
        max_collection_time = max(collection_times)

        # 指标收集时间应该合理
        assert avg_collection_time < 0.1, f"平均指标收集时间 {avg_collection_time:.3f}s 超过 0.1s"
        assert max_collection_time < 0.2, f"最大指标收集时间 {max_collection_time:.3f}s 超过 0.2s"

        print(f"系统监控性能: 平均 {avg_collection_time:.3f}s, 最大 {max_collection_time:.3f}s")


def run_performance_tests():
    """运行性能测试的便捷函数"""
    import subprocess
    import sys

    cmd = [
        sys.executable,
        "-m",
        "pytest",
        "tests/performance/",
        "-m",
        "performance",
        "-v",
        "--tb=short",
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)

    print("性能测试结果:")
    print(result.stdout)

    if result.stderr:
        print("错误信息:")
        print(result.stderr)

    return result.returncode == 0


if __name__ == "__main__":
    success = run_performance_tests()
    exit(0 if success else 1)
