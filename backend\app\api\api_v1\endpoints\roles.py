"""
角色管理API端点
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_active_user, require_permissions
from app.models.user import User as UserModel
from app.schemas.role import (
    Permission,
    PermissionCreate,
    PermissionUpdate,
    Role,
    RoleCreate,
    RoleUpdate,
    RoleWithPermissions,
)
from app.services.role import PermissionService, RoleService

router = APIRouter()


# 角色管理端点
@router.get("/", response_model=List[Role], summary="获取角色列表")
async def get_roles(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("role:read")),
):
    """
    获取角色列表

    需要权限: role:read
    """
    role_service = RoleService(db)
    roles = role_service.get_roles(skip=skip, limit=limit)
    return roles


@router.get("/{role_id}", response_model=RoleWithPermissions, summary="获取角色详情")
async def get_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("role:read")),
):
    """
    根据ID获取角色详情（包含权限信息）

    需要权限: role:read
    """
    role_service = RoleService(db)
    role = role_service.get_role_by_id(role_id)
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    return role


@router.post("/", response_model=RoleWithPermissions, summary="创建角色")
async def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("role:create")),
):
    """
    创建新角色

    需要权限: role:create

    - **name**: 角色名称（必填，唯一）
    - **description**: 角色描述（可选）
    - **permission_ids**: 权限ID列表（可选）
    """
    role_service = RoleService(db)
    return role_service.create_role(role_data)


@router.put("/{role_id}", response_model=RoleWithPermissions, summary="更新角色")
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("role:update")),
):
    """
    更新角色信息

    需要权限: role:update
    """
    role_service = RoleService(db)
    return role_service.update_role(role_id, role_data)


@router.delete("/{role_id}", summary="删除角色")
async def delete_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("role:delete")),
):
    """
    删除角色

    需要权限: role:delete

    注意：只能删除没有用户的角色
    """
    role_service = RoleService(db)
    role_service.delete_role(role_id)
    return {"message": "角色删除成功"}


@router.post(
    "/{role_id}/permissions", response_model=RoleWithPermissions, summary="为角色分配权限"
)
async def assign_permissions_to_role(
    role_id: int,
    permission_ids: List[int],
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(
        require_permissions("role:update", "permission:assign")
    ),
):
    """
    为角色分配权限

    需要权限: role:update, permission:assign
    """
    role_service = RoleService(db)
    return role_service.assign_permissions_to_role(role_id, permission_ids)


# 权限管理端点
@router.get("/permissions/", response_model=List[Permission], summary="获取权限列表")
async def get_permissions(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("permission:read")),
):
    """
    获取权限列表

    需要权限: permission:read
    """
    permission_service = PermissionService(db)
    permissions = permission_service.get_permissions(skip=skip, limit=limit)
    return permissions


@router.get("/permissions/{permission_id}", response_model=Permission, summary="获取权限详情")
async def get_permission(
    permission_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("permission:read")),
):
    """
    根据ID获取权限详情

    需要权限: permission:read
    """
    permission_service = PermissionService(db)
    permission = permission_service.get_permission_by_id(permission_id)
    if not permission:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="权限不存在")
    return permission


@router.post("/permissions/", response_model=Permission, summary="创建权限")
async def create_permission(
    permission_data: PermissionCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("permission:create")),
):
    """
    创建新权限

    需要权限: permission:create

    - **code**: 权限代码（必填，唯一）
    - **name**: 权限名称（必填）
    - **description**: 权限描述（可选）
    """
    permission_service = PermissionService(db)
    return permission_service.create_permission(permission_data)


@router.put("/permissions/{permission_id}", response_model=Permission, summary="更新权限")
async def update_permission(
    permission_id: int,
    permission_data: PermissionUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("permission:update")),
):
    """
    更新权限信息

    需要权限: permission:update
    """
    permission_service = PermissionService(db)
    return permission_service.update_permission(permission_id, permission_data)


@router.delete("/permissions/{permission_id}", summary="删除权限")
async def delete_permission(
    permission_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("permission:delete")),
):
    """
    删除权限

    需要权限: permission:delete

    注意：只能删除没有被角色使用的权限
    """
    permission_service = PermissionService(db)
    permission_service.delete_permission(permission_id)
    return {"message": "权限删除成功"}
