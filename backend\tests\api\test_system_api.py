"""
系统监控API测试
"""
from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient


class TestSystemAPI:
    """系统监控API测试类"""

    def test_get_current_metrics(self, client):
        """测试获取当前系统指标"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_current_metrics.return_value = {
                "cpu_percent": 45.5,
                "memory_percent": 50.0,
                "disk_percent": 30.0,
                "load_avg_1": 1.5,
            }

            response = client.get("/api/v1/system/metrics/current")

            assert response.status_code == 200
            data = response.json()
            assert data["cpu_percent"] == 45.5
            assert data["memory_percent"] == 50.0
            assert data["disk_percent"] == 30.0
            assert data["load_avg_1"] == 1.5
            assert "created_at" in data
            assert data["id"] == 0  # 临时ID

    def test_get_current_metrics_failure(self, client):
        """测试获取当前系统指标失败"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_current_metrics.return_value = {}

            response = client.get("/api/v1/system/metrics/current")

            assert response.status_code == 500
            assert "Failed to collect system metrics" in response.json()["detail"]

    def test_get_metrics_history(self, client):
        """测试获取历史系统指标"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_metrics_history.return_value = [
                {
                    "timestamp": "2023-01-01T12:00:00",
                    "cpu_percent": 40.0,
                    "memory_percent": 45.0,
                },
                {
                    "timestamp": "2023-01-01T12:01:00",
                    "cpu_percent": 50.0,
                    "memory_percent": 55.0,
                },
            ]

            response = client.get("/api/v1/system/metrics/history?hours=24")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["cpu_percent"] == 40.0
            assert data[1]["cpu_percent"] == 50.0

    def test_get_process_list(self, client):
        """测试获取进程列表"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_process_list.return_value = [
                {
                    "pid": 1234,
                    "name": "python",
                    "cpu_percent": 15.5,
                    "memory_percent": 8.2,
                    "status": "running",
                }
            ]

            response = client.get("/api/v1/system/processes")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["pid"] == 1234
            assert data[0]["name"] == "python"

    def test_get_network_connections(self, client):
        """测试获取网络连接"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_network_connections.return_value = [
                {
                    "local_address": "127.0.0.1:8000",
                    "remote_address": "127.0.0.1:54321",
                    "status": "ESTABLISHED",
                    "pid": 1234,
                }
            ]

            response = client.get("/api/v1/system/network/connections")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["local_address"] == "127.0.0.1:8000"
            assert data[0]["status"] == "ESTABLISHED"

    def test_get_disk_partitions(self, client):
        """测试获取磁盘分区"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_disk_partitions.return_value = [
                {
                    "device": "/dev/sda1",
                    "mountpoint": "/",
                    "fstype": "ext4",
                    "total": 107374182400,
                    "used": 53687091200,
                    "free": 53687091200,
                    "percent": 50.0,
                }
            ]

            response = client.get("/api/v1/system/disk/partitions")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["device"] == "/dev/sda1"
            assert data[0]["percent"] == 50.0

    def test_get_resource_analysis(self, client):
        """测试获取资源分析"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.analyze_resource_usage.return_value = {
                "cpu_status": "normal",
                "memory_status": "normal",
                "disk_status": "normal",
                "overall_status": "normal",
                "recommendations": ["系统资源使用正常"],
            }

            response = client.get("/api/v1/system/analysis")

            assert response.status_code == 200
            data = response.json()
            assert data["cpu_status"] == "normal"
            assert data["overall_status"] == "normal"
            assert len(data["recommendations"]) == 1

    def test_start_monitoring(self, client):
        """测试启动系统监控"""
        response = client.post("/api/v1/system/monitoring/start?interval=60")

        assert response.status_code == 200
        data = response.json()
        assert "System monitoring started" in data["message"]
        assert "60s interval" in data["message"]

    def test_stop_monitoring(self, client):
        """测试停止系统监控"""
        response = client.post("/api/v1/system/monitoring/stop")

        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "System monitoring stopped"

    def test_get_monitoring_status(self, client):
        """测试获取监控状态"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.monitoring = True
            mock_monitor.metrics_history = [1, 2, 3]

            response = client.get("/api/v1/system/monitoring/status")

            assert response.status_code == 200
            data = response.json()
            assert data["monitoring"] is True
            assert data["metrics_count"] == 3

    def test_get_alert_rules(self, client):
        """测试获取告警规则列表"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.get_alert_rules.return_value = []

            response = client.get("/api/v1/system/alerts/rules")

            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)

    def test_create_alert_rule(self, client):
        """测试创建告警规则"""
        rule_data = {
            "name": "CPU Usage Alert",
            "description": "Alert when CPU usage is high",
            "metric_name": "cpu_percent",
            "threshold_value": 80.0,
            "comparison_operator": ">",
            "severity": "high",
            "duration_minutes": 5,
            "evaluation_interval": 60,
            "notification_enabled": True,
            "is_active": True,
        }

        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_rule = Mock()
            mock_rule.id = 1
            mock_rule.name = "CPU Usage Alert"
            mock_manager.create_alert_rule.return_value = mock_rule

            response = client.post("/api/v1/system/alerts/rules", json=rule_data)

            assert response.status_code == 200

    def test_create_alert_rule_invalid_metric(self, client):
        """测试创建告警规则 - 无效指标"""
        rule_data = {
            "name": "Invalid Metric Alert",
            "metric_name": "invalid_metric",
            "threshold_value": 80.0,
            "comparison_operator": ">",
            "severity": "high",
        }

        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.create_alert_rule.side_effect = ValueError("不支持的指标名称")

            response = client.post("/api/v1/system/alerts/rules", json=rule_data)

            assert response.status_code == 400
            assert "不支持的指标名称" in response.json()["detail"]

    def test_get_alert_rule(self, client):
        """测试获取单个告警规则"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_rule = Mock()
            mock_rule.id = 1
            mock_rule.name = "CPU Usage Alert"
            mock_manager.get_alert_rule.return_value = mock_rule

            response = client.get("/api/v1/system/alerts/rules/1")

            assert response.status_code == 200

    def test_get_alert_rule_not_found(self, client):
        """测试获取不存在的告警规则"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.get_alert_rule.return_value = None

            response = client.get("/api/v1/system/alerts/rules/999")

            assert response.status_code == 404
            assert "Alert rule not found" in response.json()["detail"]

    def test_update_alert_rule(self, client):
        """测试更新告警规则"""
        rule_data = {"threshold_value": 90.0, "severity": "critical"}

        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_rule = Mock()
            mock_rule.id = 1
            mock_rule.threshold_value = 90.0
            mock_manager.update_alert_rule.return_value = mock_rule

            response = client.put("/api/v1/system/alerts/rules/1", json=rule_data)

            assert response.status_code == 200

    def test_delete_alert_rule(self, client):
        """测试删除告警规则"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.delete_alert_rule.return_value = True

            response = client.delete("/api/v1/system/alerts/rules/1")

            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Alert rule deleted successfully"

    def test_delete_alert_rule_not_found(self, client):
        """测试删除不存在的告警规则"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.delete_alert_rule.return_value = False

            response = client.delete("/api/v1/system/alerts/rules/999")

            assert response.status_code == 404
            assert "Alert rule not found" in response.json()["detail"]

    def test_get_available_metrics(self, client):
        """测试获取可用指标"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.get_available_metrics.return_value = [
                {"name": "cpu_percent", "label": "CPU使用率(%)", "unit": "%"}
            ]

            response = client.get("/api/v1/system/alerts/metrics")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 1
            assert data[0]["name"] == "cpu_percent"

    def test_get_system_alerts(self, client):
        """测试获取系统告警列表"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.get_system_alerts.return_value = []

            response = client.get("/api/v1/system/alerts")

            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)

    def test_acknowledge_alert(self, client):
        """测试确认告警"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_alert = Mock()
            mock_manager.acknowledge_alert.return_value = mock_alert

            response = client.post("/api/v1/system/alerts/1/acknowledge")

            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Alert acknowledged successfully"

    def test_resolve_alert(self, client):
        """测试解决告警"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_alert = Mock()
            mock_manager.resolve_alert.return_value = mock_alert

            response = client.post("/api/v1/system/alerts/1/resolve")

            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Alert resolved successfully"

    def test_get_alert_statistics(self, client):
        """测试获取告警统计"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.get_alert_statistics.return_value = {
                "total_alerts": 10,
                "active_alerts": 3,
                "critical_alerts": 1,
                "alert_rules_count": 5,
            }

            response = client.get("/api/v1/system/alerts/statistics?days=30")

            assert response.status_code == 200
            data = response.json()
            assert data["total_alerts"] == 10
            assert data["active_alerts"] == 3

    def test_start_alert_evaluation(self, client):
        """测试启动告警评估"""
        response = client.post("/api/v1/system/alerts/evaluation/start?interval=60")

        assert response.status_code == 200
        data = response.json()
        assert "Alert evaluation started" in data["message"]

    def test_stop_alert_evaluation(self, client):
        """测试停止告警评估"""
        response = client.post("/api/v1/system/alerts/evaluation/stop")

        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Alert evaluation stopped"

    def test_get_alert_evaluation_status(self, client):
        """测试获取告警评估状态"""
        with patch("app.services.alert_manager.alert_manager") as mock_manager:
            mock_manager.evaluating = True
            mock_manager.alert_history = [1, 2, 3]

            response = client.get("/api/v1/system/alerts/evaluation/status")

            assert response.status_code == 200
            data = response.json()
            assert data["evaluating"] is True
            assert data["alert_history_count"] == 3

    def test_test_notification_channel(self, client):
        """测试通知渠道测试"""
        test_data = {
            "channel_type": "email",
            "config": {"recipients": ["<EMAIL>"]},
        }

        response = client.post("/api/v1/system/notifications/test", json=test_data)

        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Test notification sent"

    def test_get_system_stats(self, client):
        """测试获取系统统计"""
        with patch(
            "app.services.alert_manager.alert_manager"
        ) as mock_alert_manager, patch(
            "app.api.api_v1.endpoints.system.system_monitor"
        ) as mock_monitor:
            mock_alert_manager.get_alert_statistics.return_value = {
                "total_alerts": 10,
                "active_alerts": 3,
                "critical_alerts": 1,
                "alert_rules_count": 5,
            }

            mock_monitor.get_current_metrics.return_value = {
                "cpu_percent": 45.5,
                "memory_percent": 50.0,
                "disk_percent": 30.0,
            }

            response = client.get("/api/v1/system/stats")

            assert response.status_code == 200
            data = response.json()
            assert data["total_alerts"] == 10
            assert data["active_alerts"] == 3
            assert data["avg_cpu_usage"] == 45.5

    def test_cleanup_old_metrics(self, client):
        """测试清理旧指标"""
        response = client.post("/api/v1/system/cleanup/metrics?days=30")

        assert response.status_code == 200
        data = response.json()
        assert "Cleaned up metrics older than 30 days" in data["message"]

    def test_system_health_check(self, client):
        """测试系统健康检查"""
        with patch("app.api.api_v1.endpoints.system.system_monitor") as mock_monitor:
            mock_monitor.get_current_metrics.return_value = {
                "cpu_percent": 45.5,
                "memory_percent": 50.0,
                "disk_percent": 30.0,
                "uptime": 86400,
            }

            mock_monitor.analyze_resource_usage.return_value = {
                "overall_status": "normal"
            }

            mock_monitor.monitoring = True

            response = client.get("/api/v1/system/health")

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["overall_status"] == "normal"
            assert data["monitoring_active"] is True
            assert "timestamp" in data
