#!/usr/bin/env python3
"""
代码格式化脚本
"""
import subprocess
import sys
import os
from pathlib import Path
from typing import List, <PERSON><PERSON>


def run_command(command: List[str], cwd: Path = None) -> Tuple[int, str, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=False
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)


def format_python_code():
    """格式化Python代码"""
    print("🐍 格式化Python代码...")
    
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # 使用 black 格式化
    print("  运行 black...")
    returncode, stdout, stderr = run_command([
        "python", "-m", "black", 
        "app/", "tests/", "scripts/",
        "--line-length", "88",
        "--target-version", "py310"
    ], cwd=backend_dir)
    
    if returncode == 0:
        print("  ✅ black 格式化完成")
    else:
        print(f"  ❌ black 格式化失败: {stderr}")
        return False
    
    # 使用 isort 排序导入
    print("  运行 isort...")
    returncode, stdout, stderr = run_command([
        "python", "-m", "isort",
        "app/", "tests/", "scripts/",
        "--profile", "black"
    ], cwd=backend_dir)
    
    if returncode == 0:
        print("  ✅ isort 排序完成")
    else:
        print(f"  ❌ isort 排序失败: {stderr}")
        return False
    
    return True


def lint_python_code():
    """检查Python代码"""
    print("🔍 检查Python代码...")
    
    backend_dir = Path(__file__).parent.parent / "backend"
    
    # 使用 flake8 检查
    print("  运行 flake8...")
    returncode, stdout, stderr = run_command([
        "python", "-m", "flake8",
        "app/", "tests/", "scripts/"
    ], cwd=backend_dir)
    
    if returncode == 0:
        print("  ✅ flake8 检查通过")
    else:
        print(f"  ❌ flake8 检查失败:")
        print(stdout)
        return False
    
    # 使用 mypy 类型检查
    print("  运行 mypy...")
    returncode, stdout, stderr = run_command([
        "python", "-m", "mypy",
        "app/"
    ], cwd=backend_dir)
    
    if returncode == 0:
        print("  ✅ mypy 类型检查通过")
    else:
        print(f"  ⚠️  mypy 类型检查发现问题:")
        print(stdout)
        # mypy 问题不阻止流程
    
    return True


def format_frontend_code():
    """格式化前端代码"""
    print("🎨 格式化前端代码...")
    
    frontend_dir = Path(__file__).parent.parent / "frontend"
    
    # 检查 npm 是否可用
    returncode, _, _ = run_command(["npm", "--version"])
    if returncode != 0:
        print("  ❌ npm 不可用，跳过前端格式化")
        return False
    
    # 使用 prettier 格式化
    print("  运行 prettier...")
    returncode, stdout, stderr = run_command([
        "npm", "run", "format"
    ], cwd=frontend_dir)
    
    if returncode == 0:
        print("  ✅ prettier 格式化完成")
    else:
        print(f"  ❌ prettier 格式化失败: {stderr}")
        return False
    
    return True


def lint_frontend_code():
    """检查前端代码"""
    print("🔍 检查前端代码...")
    
    frontend_dir = Path(__file__).parent.parent / "frontend"
    
    # 检查 npm 是否可用
    returncode, _, _ = run_command(["npm", "--version"])
    if returncode != 0:
        print("  ❌ npm 不可用，跳过前端检查")
        return False
    
    # 使用 eslint 检查
    print("  运行 eslint...")
    returncode, stdout, stderr = run_command([
        "npm", "run", "lint:check"
    ], cwd=frontend_dir)
    
    if returncode == 0:
        print("  ✅ eslint 检查通过")
    else:
        print(f"  ❌ eslint 检查失败:")
        print(stdout)
        return False
    
    # TypeScript 类型检查
    print("  运行 TypeScript 类型检查...")
    returncode, stdout, stderr = run_command([
        "npm", "run", "type-check"
    ], cwd=frontend_dir)
    
    if returncode == 0:
        print("  ✅ TypeScript 类型检查通过")
    else:
        print(f"  ❌ TypeScript 类型检查失败:")
        print(stdout)
        return False
    
    return True


def check_file_permissions():
    """检查文件权限"""
    print("🔒 检查文件权限...")
    
    # 检查敏感文件
    sensitive_files = [
        "backend/.env",
        "backend/app/core/config.py",
        "backend/app/core/security.py"
    ]
    
    project_root = Path(__file__).parent.parent
    
    for file_path in sensitive_files:
        full_path = project_root / file_path
        if full_path.exists():
            # 在 Windows 上简化权限检查
            try:
                with open(full_path, 'r') as f:
                    pass
                print(f"  ✅ {file_path} 权限正常")
            except PermissionError:
                print(f"  ❌ {file_path} 权限异常")
                return False
    
    return True


def generate_format_report():
    """生成格式化报告"""
    print("\n📊 生成格式化报告...")
    
    results = {
        "python_format": format_python_code(),
        "python_lint": lint_python_code(),
        "frontend_format": format_frontend_code(),
        "frontend_lint": lint_frontend_code(),
        "file_permissions": check_file_permissions()
    }
    
    # 统计结果
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f"\n📈 格式化结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有代码格式化和检查都通过了！")
        return True
    else:
        print("⚠️  存在需要修复的问题")
        for check, result in results.items():
            status = "✅" if result else "❌"
            print(f"  {status} {check}")
        return False


def main():
    """主函数"""
    print("🎨 开始代码格式化和检查...")
    print("=" * 50)
    
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent
    if not (project_root / "backend").exists() or not (project_root / "frontend").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 运行格式化和检查
    success = generate_format_report()
    
    print("=" * 50)
    if success:
        print("✅ 代码格式化和检查完成，所有项目都通过")
        sys.exit(0)
    else:
        print("❌ 代码格式化和检查完成，存在需要修复的问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
