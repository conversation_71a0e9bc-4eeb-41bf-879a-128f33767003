"""
测试配置文件
"""
import asyncio
from typing import AsyncGenerator, Generator

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.middleware.auth import get_current_user
from app.core.database import Base, get_db
from app.main import app
from app.models.user import User

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """覆盖数据库依赖"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


def override_get_current_user():
    """覆盖当前用户依赖"""
    return User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        is_active=True,
        is_superuser=False,
    )


# 覆盖依赖
app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def db_engine():
    """数据库引擎"""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db_engine):
    """数据库会话"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)

    yield session

    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client():
    """测试客户端"""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def sample_user():
    """示例用户"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "is_active": True,
        "is_superuser": False,
    }


@pytest.fixture
def sample_app():
    """示例应用"""
    return {
        "name": "Test App",
        "description": "Test application",
        "frontend_dir": "/path/to/frontend",
        "backend_dir": "/path/to/backend",
        "frontend_start_cmd": "npm start",
        "backend_start_cmd": "python main.py",
        "frontend_stop_cmd": "pkill -f npm",
        "backend_stop_cmd": "pkill -f python",
        "frontend_port": 3000,
        "backend_port": 8000,
        "is_active": True,
    }


@pytest.fixture
def sample_task():
    """示例任务"""
    return {
        "name": "Test Task",
        "description": "Test task description",
        "task_type": "command",
        "command": "echo 'Hello World'",
        "schedule_type": "manual",
        "is_active": True,
        "max_retries": 3,
        "timeout_seconds": 300,
    }


@pytest.fixture
def sample_alert_rule():
    """示例告警规则"""
    return {
        "name": "CPU Usage Alert",
        "description": "Alert when CPU usage is high",
        "metric_name": "cpu_percent",
        "threshold_value": 80.0,
        "comparison_operator": ">",
        "severity": "high",
        "duration_minutes": 5,
        "evaluation_interval": 60,
        "notification_enabled": True,
        "is_active": True,
    }


@pytest.fixture
def sample_system_metrics():
    """示例系统指标"""
    return {
        "cpu_percent": 45.5,
        "cpu_count": 4,
        "memory_total": 8589934592,  # 8GB
        "memory_used": 4294967296,  # 4GB
        "memory_percent": 50.0,
        "disk_total": 107374182400,  # 100GB
        "disk_used": 53687091200,  # 50GB
        "disk_percent": 50.0,
        "process_count": 150,
        "load_avg_1": 1.5,
        "load_avg_5": 1.2,
        "load_avg_15": 1.0,
        "uptime": 86400,  # 1 day
    }


@pytest.fixture
def mock_process_list():
    """模拟进程列表"""
    return [
        {
            "pid": 1234,
            "name": "python",
            "cpu_percent": 15.5,
            "memory_percent": 8.2,
            "status": "running",
        },
        {
            "pid": 5678,
            "name": "node",
            "cpu_percent": 12.3,
            "memory_percent": 6.1,
            "status": "running",
        },
    ]


@pytest.fixture
def mock_network_connections():
    """模拟网络连接"""
    return [
        {
            "local_address": "127.0.0.1:8000",
            "remote_address": "127.0.0.1:54321",
            "status": "ESTABLISHED",
            "pid": 1234,
        },
        {
            "local_address": "127.0.0.1:3000",
            "remote_address": "127.0.0.1:54322",
            "status": "ESTABLISHED",
            "pid": 5678,
        },
    ]


@pytest.fixture
def mock_disk_partitions():
    """模拟磁盘分区"""
    return [
        {
            "device": "/dev/sda1",
            "mountpoint": "/",
            "fstype": "ext4",
            "total": 107374182400,
            "used": 53687091200,
            "free": 53687091200,
            "percent": 50.0,
        }
    ]


class MockSystemMonitor:
    """模拟系统监控器"""

    def __init__(self):
        self.monitoring = False
        self.metrics_history = []

    def get_current_metrics(self):
        return {
            "cpu_percent": 45.5,
            "memory_percent": 50.0,
            "disk_percent": 50.0,
            "load_avg_1": 1.5,
        }

    def get_metrics_history(self, hours=24):
        return []

    def get_process_list(self):
        return []

    def get_network_connections(self):
        return []

    def get_disk_partitions(self):
        return []

    def analyze_resource_usage(self):
        return {
            "cpu_status": "normal",
            "memory_status": "normal",
            "disk_status": "normal",
            "overall_status": "normal",
            "recommendations": ["系统资源使用正常"],
        }


@pytest.fixture
def mock_system_monitor():
    """模拟系统监控器实例"""
    return MockSystemMonitor()


class MockAlertManager:
    """模拟告警管理器"""

    def __init__(self):
        self.evaluating = False
        self.alert_history = []

    def get_alert_rules(self, db, is_active=None):
        return []

    def create_alert_rule(self, db, rule_data, created_by):
        return None

    def get_system_alerts(self, db, query):
        return []

    def get_alert_statistics(self, db, days=30):
        return {
            "total_alerts": 0,
            "active_alerts": 0,
            "critical_alerts": 0,
            "alert_rules_count": 0,
        }


@pytest.fixture
def mock_alert_manager():
    """模拟告警管理器实例"""
    return MockAlertManager()


class MockNotificationService:
    """模拟通知服务"""

    async def send_email_notification(self, recipients, subject, content, data=None):
        pass

    async def send_webhook_notification(self, url, data):
        pass

    async def test_notification_channel(self, channel_type, config):
        return {"success": True, "message": "测试通知发送成功"}


@pytest.fixture
def mock_notification_service():
    """模拟通知服务实例"""
    return MockNotificationService()
