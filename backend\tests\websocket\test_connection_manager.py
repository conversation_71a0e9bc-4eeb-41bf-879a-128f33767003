"""
WebSocket连接管理器单元测试
"""
import asyncio
import json
from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest

from app.websocket.connection_manager import (
    ConnectionManager,
    MessageType,
    send_alert_triggered,
    send_app_status_update,
    send_config_update,
    send_log_message,
    send_service_status_update,
    send_system_metrics,
    send_task_execution_update,
)


class TestConnectionManager:
    """连接管理器测试类"""

    @pytest.fixture
    def connection_manager(self):
        """创建连接管理器实例"""
        return ConnectionManager()

    @pytest.fixture
    def mock_websocket(self):
        """创建模拟WebSocket"""
        websocket = Mock()
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()
        websocket.close = AsyncMock()
        return websocket

    def test_generate_connection_id(self, connection_manager):
        """测试生成连接ID"""
        id1 = connection_manager.generate_connection_id()
        id2 = connection_manager.generate_connection_id()

        assert id1 != id2
        assert id1.startswith("conn_")
        assert id2.startswith("conn_")

    @pytest.mark.asyncio
    async def test_connect(self, connection_manager, mock_websocket):
        """测试连接WebSocket"""
        app_id = 1

        connection_id = await connection_manager.connect(mock_websocket, app_id)

        # 验证WebSocket被接受
        mock_websocket.accept.assert_called_once()

        # 验证连接被存储
        assert app_id in connection_manager.active_connections
        assert connection_id in connection_manager.active_connections[app_id]
        assert (
            connection_manager.active_connections[app_id][connection_id]
            == mock_websocket
        )

        # 验证订阅信息被初始化
        assert connection_id in connection_manager.subscriptions
        assert connection_manager.subscriptions[connection_id] == set()

    def test_disconnect(self, connection_manager, mock_websocket):
        """测试断开连接"""
        app_id = 1
        connection_id = "test_conn_1"

        # 手动添加连接
        connection_manager.active_connections[app_id] = {connection_id: mock_websocket}
        connection_manager.subscriptions[connection_id] = {"test_type"}

        connection_manager.disconnect(app_id, connection_id)

        # 验证连接被移除
        assert app_id not in connection_manager.active_connections
        assert connection_id not in connection_manager.subscriptions

    @pytest.mark.asyncio
    async def test_send_personal_message(self, connection_manager, mock_websocket):
        """测试发送个人消息"""
        message = {"type": "test", "data": "test_data"}

        await connection_manager.send_personal_message(message, mock_websocket)

        mock_websocket.send_text.assert_called_once_with(json.dumps(message))

    @pytest.mark.asyncio
    async def test_send_to_app(self, connection_manager):
        """测试向应用发送消息"""
        app_id = 1
        message = {"type": "test", "data": "test_data"}

        # 创建多个模拟连接
        mock_ws1 = Mock()
        mock_ws1.send_text = AsyncMock()
        mock_ws2 = Mock()
        mock_ws2.send_text = AsyncMock()

        connection_manager.active_connections[app_id] = {
            "conn1": mock_ws1,
            "conn2": mock_ws2,
        }
        connection_manager.subscriptions["conn1"] = {"test"}
        connection_manager.subscriptions["conn2"] = {"other"}

        # 发送消息给订阅了test类型的连接
        await connection_manager.send_to_app(app_id, message, "test")

        # 验证只有订阅了test类型的连接收到消息
        mock_ws1.send_text.assert_called_once_with(json.dumps(message))
        mock_ws2.send_text.assert_not_called()

    @pytest.mark.asyncio
    async def test_send_to_app_no_filter(self, connection_manager):
        """测试向应用发送消息（无类型过滤）"""
        app_id = 1
        message = {"type": "test", "data": "test_data"}

        # 创建模拟连接
        mock_ws1 = Mock()
        mock_ws1.send_text = AsyncMock()
        mock_ws2 = Mock()
        mock_ws2.send_text = AsyncMock()

        connection_manager.active_connections[app_id] = {
            "conn1": mock_ws1,
            "conn2": mock_ws2,
        }
        connection_manager.subscriptions["conn1"] = {"test"}
        connection_manager.subscriptions["conn2"] = {"other"}

        # 发送消息（不指定类型）
        await connection_manager.send_to_app(app_id, message)

        # 验证所有连接都收到消息
        mock_ws1.send_text.assert_called_once_with(json.dumps(message))
        mock_ws2.send_text.assert_called_once_with(json.dumps(message))

    def test_subscribe(self, connection_manager):
        """测试订阅消息类型"""
        connection_id = "test_conn"
        message_types = ["type1", "type2"]

        connection_manager.subscriptions[connection_id] = set()
        connection_manager.subscribe(connection_id, message_types)

        assert connection_manager.subscriptions[connection_id] == {"type1", "type2"}

    def test_unsubscribe(self, connection_manager):
        """测试取消订阅消息类型"""
        connection_id = "test_conn"

        connection_manager.subscriptions[connection_id] = {"type1", "type2", "type3"}
        connection_manager.unsubscribe(connection_id, ["type1", "type2"])

        assert connection_manager.subscriptions[connection_id] == {"type3"}

    def test_get_connection_count(self, connection_manager):
        """测试获取连接数量"""
        # 添加测试连接
        connection_manager.active_connections[1] = {"conn1": Mock(), "conn2": Mock()}
        connection_manager.active_connections[2] = {"conn3": Mock()}

        # 测试总连接数
        assert connection_manager.get_connection_count() == 3

        # 测试特定应用连接数
        assert connection_manager.get_connection_count(1) == 2
        assert connection_manager.get_connection_count(2) == 1
        assert connection_manager.get_connection_count(999) == 0

    def test_get_active_apps(self, connection_manager):
        """测试获取活跃应用列表"""
        connection_manager.active_connections[1] = {"conn1": Mock()}
        connection_manager.active_connections[3] = {"conn2": Mock()}

        active_apps = connection_manager.get_active_apps()
        assert set(active_apps) == {1, 3}


class TestMessageHelpers:
    """消息发送辅助函数测试类"""

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_app_status_update(self, mock_manager):
        """测试发送应用状态更新"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        status_data = {"frontend_status": "running", "backend_status": "stopped"}

        await send_app_status_update(app_id, status_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id  # app_id
        assert call_args[0][2] == MessageType.APP_STATUS_UPDATE  # message_type

        message = call_args[0][1]  # message
        assert message["type"] == MessageType.APP_STATUS_UPDATE
        assert message["data"] == status_data
        assert "timestamp" in message

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_service_status_update(self, mock_manager):
        """测试发送服务状态更新"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        service_type = "frontend"
        status = "running"

        await send_service_status_update(app_id, service_type, status)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.SERVICE_STATUS_UPDATE

        message = call_args[0][1]
        assert message["type"] == MessageType.SERVICE_STATUS_UPDATE
        assert message["data"]["service_type"] == service_type
        assert message["data"]["status"] == status
        assert message["data"]["app_id"] == app_id

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_task_execution_update(self, mock_manager):
        """测试发送任务执行更新"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        task_data = {"task_id": 123, "status": "success", "result": "Task completed"}

        await send_task_execution_update(app_id, task_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.TASK_EXECUTION_UPDATE

        message = call_args[0][1]
        assert message["type"] == MessageType.TASK_EXECUTION_UPDATE
        assert message["data"] == task_data

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_log_message(self, mock_manager):
        """测试发送日志消息"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        log_data = {
            "level": "INFO",
            "message": "Test log message",
            "timestamp": datetime.now().isoformat(),
        }

        await send_log_message(app_id, log_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.LOG_MESSAGE

        message = call_args[0][1]
        assert message["type"] == MessageType.LOG_MESSAGE
        assert message["data"] == log_data

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_system_metrics(self, mock_manager):
        """测试发送系统指标"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        metrics_data = {"cpu_usage": 45.5, "memory_usage": 60.2, "disk_usage": 30.1}

        await send_system_metrics(app_id, metrics_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.SYSTEM_METRICS

        message = call_args[0][1]
        assert message["type"] == MessageType.SYSTEM_METRICS
        assert message["data"] == metrics_data

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_alert_triggered(self, mock_manager):
        """测试发送告警触发"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        alert_data = {
            "alert_id": 456,
            "name": "High CPU Usage",
            "severity": "high",
            "message": "CPU usage exceeded 80%",
        }

        await send_alert_triggered(app_id, alert_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.ALERT_TRIGGERED

        message = call_args[0][1]
        assert message["type"] == MessageType.ALERT_TRIGGERED
        assert message["data"] == alert_data

    @pytest.mark.asyncio
    @patch("app.websocket.connection_manager.connection_manager")
    async def test_send_config_update(self, mock_manager):
        """测试发送配置更新"""
        mock_manager.send_to_app = AsyncMock()

        app_id = 1
        config_data = {
            "config_id": 789,
            "key": "database_url",
            "old_value": "old_url",
            "new_value": "new_url",
        }

        await send_config_update(app_id, config_data)

        mock_manager.send_to_app.assert_called_once()
        call_args = mock_manager.send_to_app.call_args

        assert call_args[0][0] == app_id
        assert call_args[0][2] == MessageType.CONFIG_UPDATE

        message = call_args[0][1]
        assert message["type"] == MessageType.CONFIG_UPDATE
        assert message["data"] == config_data


class TestMessageType:
    """消息类型常量测试类"""

    def test_message_type_constants(self):
        """测试消息类型常量"""
        assert MessageType.PING == "ping"
        assert MessageType.PONG == "pong"
        assert MessageType.APP_STATUS_UPDATE == "app_status_update"
        assert MessageType.SERVICE_STATUS_UPDATE == "service_status_update"
        assert MessageType.TASK_STATUS_UPDATE == "task_status_update"
        assert MessageType.TASK_EXECUTION_UPDATE == "task_execution_update"
        assert MessageType.LOG_MESSAGE == "log_message"
        assert MessageType.LOG_ALERT == "log_alert"
        assert MessageType.SYSTEM_METRICS == "system_metrics"
        assert MessageType.ALERT_TRIGGERED == "alert_triggered"
        assert MessageType.CONFIG_UPDATE == "config_update"
