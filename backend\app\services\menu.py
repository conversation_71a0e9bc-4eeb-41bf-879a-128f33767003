"""
菜单管理服务
"""
from typing import List, Optional

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.models.menu import Menu
from app.models.user import User
from app.schemas.menu import MenuCreate, MenuTree, MenuUpdate, UserMenu


class MenuService:
    """菜单管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_menus(self, skip: int = 0, limit: int = 100) -> List[Menu]:
        """
        获取菜单列表

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            菜单列表
        """
        return (
            self.db.query(Menu)
            .order_by(Menu.sort_order)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_menu_by_id(self, menu_id: int) -> Optional[Menu]:
        """
        根据ID获取菜单

        Args:
            menu_id: 菜单ID

        Returns:
            菜单对象或None
        """
        return self.db.query(Menu).filter(Menu.id == menu_id).first()

    def get_menu_by_name(self, name: str) -> Optional[Menu]:
        """
        根据名称获取菜单

        Args:
            name: 菜单名称

        Returns:
            菜单对象或None
        """
        return self.db.query(Menu).filter(Menu.name == name).first()

    def create_menu(self, menu_data: MenuCreate) -> Menu:
        """
        创建菜单

        Args:
            menu_data: 菜单创建数据

        Returns:
            创建的菜单对象

        Raises:
            HTTPException: 菜单名称已存在或父菜单不存在时抛出异常
        """
        # 检查菜单名称是否已存在
        if self.get_menu_by_name(menu_data.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="菜单名称已存在"
            )

        # 如果指定了父菜单，检查父菜单是否存在
        if menu_data.parent_id:
            parent_menu = self.get_menu_by_id(menu_data.parent_id)
            if not parent_menu:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="父菜单不存在"
                )

        # 创建菜单
        db_menu = Menu(
            name=menu_data.name,
            path=menu_data.path,
            component=menu_data.component,
            icon=menu_data.icon,
            parent_id=menu_data.parent_id,
            sort_order=menu_data.sort_order,
            is_hidden=menu_data.is_hidden,
            is_external=menu_data.is_external,
        )

        self.db.add(db_menu)
        self.db.commit()
        self.db.refresh(db_menu)

        return db_menu

    def update_menu(self, menu_id: int, menu_data: MenuUpdate) -> Menu:
        """
        更新菜单信息

        Args:
            menu_id: 菜单ID
            menu_data: 菜单更新数据

        Returns:
            更新后的菜单对象

        Raises:
            HTTPException: 菜单不存在、名称已被其他菜单使用或父菜单不存在时抛出异常
        """
        menu = self.get_menu_by_id(menu_id)
        if not menu:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="菜单不存在")

        # 检查菜单名称是否被其他菜单使用
        if menu_data.name and menu_data.name != menu.name:
            existing_menu = self.get_menu_by_name(menu_data.name)
            if existing_menu and existing_menu.id != menu_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="菜单名称已被其他菜单使用"
                )

        # 如果要更新父菜单，检查父菜单是否存在
        if menu_data.parent_id is not None:
            if menu_data.parent_id == menu_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="菜单不能设置自己为父菜单"
                )

            if menu_data.parent_id != 0:  # 0表示根菜单
                parent_menu = self.get_menu_by_id(menu_data.parent_id)
                if not parent_menu:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND, detail="父菜单不存在"
                    )

                # 检查是否会形成循环引用
                if self._would_create_cycle(menu_id, menu_data.parent_id):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不能设置子菜单为父菜单，这会形成循环引用",
                    )

        # 更新菜单信息
        update_data = menu_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(menu, field, value)

        self.db.commit()
        self.db.refresh(menu)

        return menu

    def delete_menu(self, menu_id: int) -> bool:
        """
        删除菜单

        Args:
            menu_id: 菜单ID

        Returns:
            删除成功返回True

        Raises:
            HTTPException: 菜单不存在或菜单下有子菜单时抛出异常
        """
        menu = self.get_menu_by_id(menu_id)
        if not menu:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="菜单不存在")

        # 检查是否有子菜单
        child_menus = self.db.query(Menu).filter(Menu.parent_id == menu_id).first()
        if child_menus:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="菜单下有子菜单，无法删除"
            )

        self.db.delete(menu)
        self.db.commit()

        return True

    def get_menu_tree(self) -> List[MenuTree]:
        """
        获取菜单树形结构

        Returns:
            菜单树形结构列表
        """
        # 获取所有菜单
        all_menus = self.db.query(Menu).order_by(Menu.sort_order).all()

        # 构建菜单字典
        menu_dict = {menu.id: MenuTree.model_validate(menu) for menu in all_menus}

        # 构建树形结构
        root_menus = []
        for menu in all_menus:
            menu_tree = menu_dict[menu.id]
            if menu.parent_id and menu.parent_id in menu_dict:
                # 添加到父菜单的children中
                parent_menu = menu_dict[menu.parent_id]
                parent_menu.children.append(menu_tree)
            else:
                # 根菜单
                root_menus.append(menu_tree)

        return root_menus

    def get_user_menus(self, user: User) -> List[UserMenu]:
        """
        获取用户可访问的菜单

        Args:
            user: 用户对象

        Returns:
            用户菜单列表
        """
        # 如果是超级用户，返回所有菜单
        if user.is_superuser:
            all_menus = (
                self.db.query(Menu)
                .filter(Menu.is_hidden == False)
                .order_by(Menu.sort_order)
                .all()
            )
        else:
            # 获取用户角色的菜单权限
            menu_ids = set()
            for role in user.roles:
                for menu in role.menus:
                    if not menu.is_hidden:
                        menu_ids.add(menu.id)

            if not menu_ids:
                return []

            all_menus = (
                self.db.query(Menu)
                .filter(Menu.id.in_(menu_ids), Menu.is_hidden == False)
                .order_by(Menu.sort_order)
                .all()
            )

        # 构建菜单字典
        menu_dict = {menu.id: UserMenu.model_validate(menu) for menu in all_menus}

        # 构建树形结构
        root_menus = []
        for menu in all_menus:
            user_menu = menu_dict[menu.id]
            if menu.parent_id and menu.parent_id in menu_dict:
                # 添加到父菜单的children中
                parent_menu = menu_dict[menu.parent_id]
                parent_menu.children.append(user_menu)
            else:
                # 根菜单
                root_menus.append(user_menu)

        return root_menus

    def get_menu_children(self, menu_id: int) -> List[Menu]:
        """
        获取菜单的直接子菜单

        Args:
            menu_id: 菜单ID

        Returns:
            子菜单列表
        """
        return (
            self.db.query(Menu)
            .filter(Menu.parent_id == menu_id)
            .order_by(Menu.sort_order)
            .all()
        )

    def get_menu_descendants(self, menu_id: int) -> List[Menu]:
        """
        获取菜单的所有后代菜单

        Args:
            menu_id: 菜单ID

        Returns:
            所有后代菜单列表
        """
        descendants = []
        children = self.get_menu_children(menu_id)

        for child in children:
            descendants.append(child)
            descendants.extend(self.get_menu_descendants(child.id))

        return descendants

    def _would_create_cycle(self, menu_id: int, new_parent_id: int) -> bool:
        """
        检查设置新的父菜单是否会创建循环引用

        Args:
            menu_id: 当前菜单ID
            new_parent_id: 新的父菜单ID

        Returns:
            如果会创建循环引用返回True，否则返回False
        """
        # 获取所有后代菜单
        descendants = self.get_menu_descendants(menu_id)
        descendant_ids = [menu.id for menu in descendants]

        # 如果新的父菜单是当前菜单的后代，则会形成循环
        return new_parent_id in descendant_ids
