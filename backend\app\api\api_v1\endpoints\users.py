"""
用户管理API端点
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.middleware.auth import get_current_active_user, require_permissions
from app.models.user import User as UserModel
from app.schemas.auth import User, UserCreate, UserUpdate
from app.services.user import UserService

router = APIRouter()


@router.get("/", response_model=List[User], summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制返回的记录数"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:read")),
):
    """
    获取用户列表

    需要权限: user:read
    """
    user_service = UserService(db)
    users = user_service.get_users(skip=skip, limit=limit)
    return users


@router.get("/{user_id}", response_model=User, summary="获取用户详情")
async def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:read")),
):
    """
    根据ID获取用户详情

    需要权限: user:read
    """
    user_service = UserService(db)
    user = user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    return user


@router.post("/", response_model=User, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:create")),
):
    """
    创建新用户

    需要权限: user:create

    - **username**: 用户名（必填，唯一）
    - **email**: 邮箱（必填，唯一）
    - **full_name**: 全名（可选）
    - **password**: 密码（必填）
    - **is_active**: 是否激活（默认为True）
    """
    user_service = UserService(db)
    return user_service.create_user(user_data)


@router.put("/{user_id}", response_model=User, summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:update")),
):
    """
    更新用户信息

    需要权限: user:update
    """
    user_service = UserService(db)
    return user_service.update_user(user_id, user_data)


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:delete")),
):
    """
    删除用户

    需要权限: user:delete
    """
    user_service = UserService(db)
    user_service.delete_user(user_id)
    return {"message": "用户删除成功"}


@router.post("/{user_id}/roles/{role_id}", response_model=User, summary="为用户分配角色")
async def assign_role_to_user(
    user_id: int,
    role_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(
        require_permissions("user:update", "role:assign")
    ),
):
    """
    为用户分配角色

    需要权限: user:update, role:assign
    """
    user_service = UserService(db)
    return user_service.assign_role_to_user(user_id, role_id)


@router.delete("/{user_id}/roles/{role_id}", response_model=User, summary="移除用户角色")
async def remove_role_from_user(
    user_id: int,
    role_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(
        require_permissions("user:update", "role:assign")
    ),
):
    """
    移除用户角色

    需要权限: user:update, role:assign
    """
    user_service = UserService(db)
    return user_service.remove_role_from_user(user_id, role_id)


@router.post(
    "/{user_id}/departments/{department_id}", response_model=User, summary="为用户分配部门"
)
async def assign_department_to_user(
    user_id: int,
    department_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(
        require_permissions("user:update", "department:assign")
    ),
):
    """
    为用户分配部门

    需要权限: user:update, department:assign
    """
    user_service = UserService(db)
    return user_service.assign_department_to_user(user_id, department_id)


@router.put("/{user_id}/status", response_model=User, summary="更新用户状态")
async def update_user_status(
    user_id: int,
    is_active: bool,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(require_permissions("user:update")),
):
    """
    更新用户激活状态

    需要权限: user:update
    """
    user_service = UserService(db)
    user_data = UserUpdate(is_active=is_active)
    return user_service.update_user(user_id, user_data)
