# Alembic configuration file

[alembic]
# path to migration scripts
script_location = alembic

# template used to generate migration files
file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s

# timezone
timezone =

# max length of characters to apply to the
truncate_slug_length = 40

# set to 'true' to run the environment during
revision_environment = false

# sqlalchemy.url - 从环境变量读取
# sqlalchemy.url = postgresql://postgres:password@localhost/app_manager

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S