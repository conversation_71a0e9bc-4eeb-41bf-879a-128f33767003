<template>
  <div class="task-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>任务管理</h2>
        <p>管理和监控系统中的所有任务</p>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          创建任务
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6" v-for="stat in statsCards" :key="stat.title">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <component :is="stat.icon" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-title">{{ stat.title }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索任务名称或描述"
            style="width: 200px"
            @pressEnter="handleSearch"
          >
            <template #suffix>
              <SearchOutlined @click="handleSearch" />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item label="任务类型">
          <a-select
            v-model:value="searchForm.task_type"
            placeholder="选择任务类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option
              v-for="type in filterOptions.taskTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="调度类型">
          <a-select
            v-model:value="searchForm.schedule_type"
            placeholder="选择调度类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option
              v-for="type in filterOptions.scheduleTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.is_active"
            placeholder="选择状态"
            style="width: 120px"
            allowClear
          >
            <a-select-option :value="true">激活</a-select-option>
            <a-select-option :value="false">停用</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button @click="handleReset" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedRowKeys.length > 0">
      <a-space>
        <span>已选择 {{ selectedRowKeys.length }} 项</span>
        <a-button @click="handleBatchActivate">批量激活</a-button>
        <a-button @click="handleBatchDeactivate">批量停用</a-button>
        <a-button @click="handleBatchExecute">批量执行</a-button>
        <a-button danger @click="handleBatchDelete">批量删除</a-button>
      </a-space>
    </div>

    <!-- 任务表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="tasks"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="task-name">
              <a @click="showTaskDetail(record)">{{ record.name }}</a>
              <div class="task-description">{{ record.description }}</div>
            </div>
          </template>

          <template v-else-if="column.key === 'task_type'">
            <a-tag :color="TASK_TYPE_MAP[record.task_type]?.color">
              <component :is="TASK_TYPE_MAP[record.task_type]?.icon" />
              {{ TASK_TYPE_MAP[record.task_type]?.label }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'schedule_type'">
            <a-tag :color="SCHEDULE_TYPE_MAP[record.schedule_type]?.color">
              <component :is="SCHEDULE_TYPE_MAP[record.schedule_type]?.icon" />
              {{ SCHEDULE_TYPE_MAP[record.schedule_type]?.label }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'schedule_info'">
            <div v-if="record.schedule_type === 'cron'">
              <div>{{ record.cron_expression }}</div>
              <div class="schedule-next" v-if="record.next_run_at">
                下次: {{ formatDateTime(record.next_run_at) }}
              </div>
            </div>
            <div v-else-if="record.schedule_type === 'interval'">
              <div>每 {{ record.interval_seconds }}s</div>
              <div class="schedule-next" v-if="record.next_run_at">
                下次: {{ formatDateTime(record.next_run_at) }}
              </div>
            </div>
            <div v-else>
              <span class="text-muted">手动执行</span>
            </div>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-switch
              v-model:checked="record.is_active"
              @change="(checked) => handleToggleStatus(record, checked)"
              :loading="record.statusLoading"
            />
          </template>

          <template v-else-if="column.key === 'last_execution'">
            <div v-if="record.last_run_at">
              <div>{{ formatDateTime(record.last_run_at) }}</div>
              <a-tag
                v-if="record.last_execution_status"
                :color="TASK_STATUS_MAP[record.last_execution_status]?.color"
                size="small"
              >
                {{ TASK_STATUS_MAP[record.last_execution_status]?.label }}
              </a-tag>
            </div>
            <span v-else class="text-muted">未执行</span>
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button
                type="text"
                size="small"
                @click="handleExecuteTask(record)"
                :disabled="!record.is_active"
                :loading="record.executeLoading"
              >
                <template #icon><PlayCircleOutlined /></template>
                执行
              </a-button>
              
              <a-dropdown>
                <a-button type="text" size="small">
                  <template #icon><MoreOutlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuAction(key, record)">
                    <a-menu-item key="edit">
                      <EditOutlined />
                      编辑
                    </a-menu-item>
                    <a-menu-item key="executions">
                      <HistoryOutlined />
                      执行记录
                    </a-menu-item>
                    <a-menu-item key="schedule" v-if="record.schedule_type !== 'manual'">
                      <ScheduleOutlined />
                      调度管理
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑任务弹窗 -->
    <TaskFormModal
      v-model:visible="formModalVisible"
      :task="currentTask"
      @success="handleFormSuccess"
    />

    <!-- 任务详情弹窗 -->
    <TaskDetailModal
      v-model:visible="detailModalVisible"
      :task="currentTask"
    />

    <!-- 执行记录弹窗 -->
    <TaskExecutionsModal
      v-model:visible="executionsModalVisible"
      :task="currentTask"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  VideoPlay,
  Edit,
  Delete,
  More,
  Clock,
  Timer
} from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import {
  Task,
  TaskQuery,
  TaskStats,
  TaskBatchOperation,
  TASK_TYPE_MAP,
  SCHEDULE_TYPE_MAP,
  TASK_STATUS_MAP,
  TaskFilterOptions,
  TaskStatCard
} from '@/types/task'
import { formatDateTime } from '@/utils/date'
import TaskFormModal from './components/TaskFormModal.vue'
import TaskDetailModal from './components/TaskDetailModal.vue'
import TaskExecutionsModal from './components/TaskExecutionsModal.vue'

// 响应式数据
const loading = ref(false)
const tasks = ref<Task[]>([])
const selectedRowKeys = ref<number[]>([])
const stats = ref<TaskStats>()

// 弹窗状态
const formModalVisible = ref(false)
const detailModalVisible = ref(false)
const executionsModalVisible = ref(false)
const currentTask = ref<Task | null>(null)

// 搜索表单
const searchForm = reactive<TaskQuery>({
  keyword: '',
  task_type: undefined,
  schedule_type: undefined,
  is_active: undefined,
  skip: 0,
  limit: 20
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 筛选选项
const filterOptions = ref<TaskFilterOptions>({
  taskTypes: [],
  scheduleTypes: [],
  statusOptions: [
    { label: '激活', value: 'true' },
    { label: '停用', value: 'false' }
  ],
  apps: []
})

// 表格列配置
const columns = [
  {
    title: '任务名称',
    key: 'name',
    width: 250,
    ellipsis: true
  },
  {
    title: '类型',
    key: 'task_type',
    width: 120
  },
  {
    title: '调度配置',
    key: 'schedule_info',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '最后执行',
    key: 'last_execution',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    customRender: ({ text }: { text: string }) => formatDateTime(text)
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 统计卡片
const statsCards = computed<TaskStatCard[]>(() => {
  if (!stats.value) return []
  
  return [
    {
      title: '总任务数',
      value: stats.value.total_tasks,
      icon: 'FileTextOutlined',
      color: '#1890ff'
    },
    {
      title: '激活任务',
      value: stats.value.active_tasks,
      icon: 'CheckCircleOutlined',
      color: '#52c41a'
    },
    {
      title: '调度任务',
      value: stats.value.scheduled_tasks,
      icon: 'ClockCircleOutlined',
      color: '#faad14'
    },
    {
      title: '手动任务',
      value: stats.value.manual_tasks,
      icon: 'PlayCircleOutlined',
      color: '#722ed1'
    }
  ]
})

// 方法
const loadTasks = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }
    
    const [tasksRes, countRes] = await Promise.all([
      taskApi.getTasks(params),
      taskApi.countTasks(params)
    ])
    
    tasks.value = tasksRes.data
    pagination.total = countRes.data.count
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const res = await taskApi.getTaskStats()
    stats.value = res.data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadFilterOptions = async () => {
  try {
    const res = await taskApi.getTaskTypes()
    if (res.data && res.data.task_types) {
      filterOptions.value.taskTypes = res.data.task_types
    }
    if (res.data && res.data.schedule_types) {
      filterOptions.value.scheduleTypes = res.data.schedule_types
    }
  } catch (error) {
    console.error('加载筛选选项失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadTasks()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    task_type: undefined,
    schedule_type: undefined,
    is_active: undefined
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadTasks()
}

const showCreateModal = () => {
  currentTask.value = null
  formModalVisible.value = true
}

const showTaskDetail = (task: Task) => {
  currentTask.value = task
  detailModalVisible.value = true
}

const handleFormSuccess = () => {
  loadTasks()
  loadStats()
}

const handleToggleStatus = async (task: Task, checked: boolean) => {
  try {
    task.statusLoading = true
    await taskApi.updateTask(task.id, { is_active: checked })
    task.is_active = checked
    ElMessage.success(checked ? '任务已激活' : '任务已停用')
    loadStats()
  } catch (error) {
    ElMessage.error('更新任务状态失败')
    // 恢复原状态
    task.is_active = !checked
  } finally {
    task.statusLoading = false
  }
}

const handleExecuteTask = async (task: Task) => {
  try {
    task.executeLoading = true
    await taskApi.executeTask(task.id)
    ElMessage.success('任务执行已启动')
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '执行任务失败')
  } finally {
    task.executeLoading = false
  }
}

const handleMenuAction = (key: string, task: Task) => {
  switch (key) {
    case 'edit':
      currentTask.value = task
      formModalVisible.value = true
      break
    case 'executions':
      currentTask.value = task
      executionsModalVisible.value = true
      break
    case 'schedule':
      // TODO: 实现调度管理
      ElMessage.info('调度管理功能开发中')
      break
    case 'delete':
      handleDeleteTask(task)
      break
  }
}

const handleDeleteTask = (task: Task) => {
  ElMessageBox.confirm(
    `确定要删除任务"${task.name}"吗？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await taskApi.deleteTask(task.id)
      ElMessage.success('任务删除成功')
      loadTasks()
      loadStats()
    } catch (error) {
      ElMessage.error('删除任务失败')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

const handleBatchOperation = async (operation: string) => {
  if (selectedRowKeys.value.length === 0) {
    ElMessage.warning('请先选择要操作的任务')
    return
  }

  const batchData: TaskBatchOperation = {
    task_ids: selectedRowKeys.value,
    operation: operation as any
  }

  try {
    await taskApi.batchOperation(batchData)
    ElMessage.success('批量操作成功')
    selectedRowKeys.value = []
    loadTasks()
    loadStats()
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

const handleBatchActivate = () => {
  ElMessageBox.confirm(
    `确定要激活选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    '批量激活',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    handleBatchOperation('activate')
  }).catch(() => {
    // 用户取消操作
  })
}

const handleBatchDeactivate = () => {
  ElMessageBox.confirm(
    `确定要停用选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    '批量停用',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleBatchOperation('deactivate')
  }).catch(() => {
    // 用户取消操作
  })
}

const handleBatchExecute = () => {
  ElMessageBox.confirm(
    `确定要执行选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    '批量执行',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    handleBatchOperation('execute')
  }).catch(() => {
    // 用户取消操作
  })
}

const handleBatchDelete = () => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRowKeys.value.length} 个任务吗？此操作不可恢复。`,
    '批量删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleBatchOperation('delete')
  }).catch(() => {
    // 用户取消操作
  })
}

// 生命周期
onMounted(() => {
  loadTasks()
  loadStats()
  loadFilterOptions()
})
</script>

<style scoped>
.task-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 4px 0 0 0;
  color: #666;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
}

.stat-title {
  color: #666;
  font-size: 14px;
  margin-top: 4px;
}

.search-card {
  margin-bottom: 16px;
}

.batch-actions {
  background: #f0f2f5;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.task-name a {
  font-weight: 500;
  color: #1890ff;
}

.task-description {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.schedule-next {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.text-muted {
  color: #999;
}
</style>