"""
日志查询功能测试
"""
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.database import Base
from app.models.app import App, AppLog
from app.models.user import User
from app.schemas.log import LogQuery
from app.services.log import LogQueryService


@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()

    # 创建测试用户
    user = User(
        username="testuser", email="<EMAIL>", password_hash="hashed_password"
    )
    session.add(user)
    session.commit()

    # 创建测试应用
    app = App(name="Test App", description="Test application", created_by=user.id)
    session.add(app)
    session.commit()

    yield session, app
    session.close()


@pytest.fixture
def log_query_service_instance():
    """创建日志查询服务实例"""
    with tempfile.TemporaryDirectory() as temp_dir:
        service = LogQueryService()
        service.log_dir = Path(temp_dir)
        yield service


class TestLogQueryService:
    """日志查询服务测试"""

    def test_query_logs_basic(self, db_session, log_query_service_instance):
        """测试基本日志查询"""
        db, test_app = db_session

        # 创建测试日志
        log1 = AppLog(
            app_id=test_app.id,
            service_type="backend",
            level="INFO",
            message="Test info message",
        )
        log2 = AppLog(
            app_id=test_app.id,
            service_type="frontend",
            level="ERROR",
            message="Test error message",
        )

        db.add_all([log1, log2])
        db.commit()

        # 查询所有日志
        query = LogQuery(app_id=test_app.id)
        logs = log_query_service_instance.query_logs(db, query)

        assert len(logs) == 2
        assert logs[0].app_id == test_app.id
        assert logs[1].app_id == test_app.id

    def test_query_logs_with_filters(self, db_session, log_query_service_instance):
        """测试带筛选条件的日志查询"""
        db, test_app = db_session

        # 创建测试日志
        log1 = AppLog(
            app_id=test_app.id,
            service_type="backend",
            level="INFO",
            message="Backend info message",
        )
        log2 = AppLog(
            app_id=test_app.id,
            service_type="frontend",
            level="ERROR",
            message="Frontend error message",
        )
        log3 = AppLog(
            app_id=test_app.id,
            service_type="backend",
            level="ERROR",
            message="Backend error message",
        )

        db.add_all([log1, log2, log3])
        db.commit()

        # 按服务类型筛选
        query = LogQuery(app_id=test_app.id, service_type="backend")
        logs = log_query_service_instance.query_logs(db, query)
        assert len(logs) == 2
        assert all(log.service_type == "backend" for log in logs)

        # 按日志级别筛选
        query = LogQuery(app_id=test_app.id, level="ERROR")
        logs = log_query_service_instance.query_logs(db, query)
        assert len(logs) == 2
        assert all(log.level == "ERROR" for log in logs)

        # 按关键词筛选
        query = LogQuery(app_id=test_app.id, keyword="Frontend")
        logs = log_query_service_instance.query_logs(db, query)
        assert len(logs) == 1
        assert "Frontend" in logs[0].message

    def test_export_logs_csv(self, db_session, log_query_service_instance):
        """测试CSV格式日志导出"""
        db, test_app = db_session

        # 创建测试日志
        log = AppLog(
            app_id=test_app.id,
            service_type="backend",
            level="INFO",
            message="Test export message",
        )
        db.add(log)
        db.commit()

        # 导出为CSV
        query = LogQuery(app_id=test_app.id)
        csv_content = log_query_service_instance.export_logs(db, query, "csv")

        assert "ID,App ID,Service Type,Level,Message,Timestamp" in csv_content
        assert "Test export message" in csv_content
