"""
应用项目管理系统 - 主入口文件
"""
import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.logging import setup_logging, RequestLoggingMiddleware, get_logger
from app.core.exception_handlers import register_exception_handlers
from app.api.api_v1.api import api_router
from app.websocket.routes import router as websocket_router
from app.core.database import engine
from app.models import Base
from app.services.realtime_service import start_realtime_service, stop_realtime_service

# 设置日志
setup_logging()
logger = get_logger("main")

# 创建数据库表（如果数据库可用）
try:
    Base.metadata.create_all(bind=engine)
    logger.info("数据库连接成功，表结构已创建")
except Exception as e:
    logger.warning(f"数据库连接失败: {e}")
    logger.info("应用将在无数据库模式下启动，请检查数据库配置")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("应用启动中...")
    try:
        await start_realtime_service()
        logger.info("实时服务启动成功")
    except Exception as e:
        logger.error(f"实时服务启动失败: {e}")

    yield

    # 关闭时执行
    logger.info("应用关闭中...")
    try:
        await stop_realtime_service()
        logger.info("实时服务关闭成功")
    except Exception as e:
        logger.error(f"实时服务关闭失败: {e}")


app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="应用项目管理系统API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.ENVIRONMENT != "production" else None,
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# 注册异常处理器
register_exception_handlers(app)

# 添加请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 包含WebSocket路由
app.include_router(websocket_router)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "应用项目管理系统API",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "version": settings.VERSION}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )