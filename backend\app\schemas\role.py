"""
角色相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class PermissionBase(BaseModel):
    """权限基础模式"""

    code: str
    name: str
    description: Optional[str] = None


class PermissionCreate(PermissionBase):
    """权限创建模式"""

    pass


class PermissionUpdate(BaseModel):
    """权限更新模式"""

    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None


class Permission(PermissionBase):
    """权限响应模式"""

    id: int
    created_at: datetime

    model_config = {"from_attributes": True}


class RoleBase(BaseModel):
    """角色基础模式"""

    name: str
    description: Optional[str] = None


class RoleCreate(RoleBase):
    """角色创建模式"""

    permission_ids: List[int] = []


class RoleUpdate(BaseModel):
    """角色更新模式"""

    name: Optional[str] = None
    description: Optional[str] = None
    permission_ids: Optional[List[int]] = None


class Role(RoleBase):
    """角色响应模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class RoleWithPermissions(Role):
    """包含权限的角色模式"""

    permissions: List[Permission] = []

    model_config = {"from_attributes": True}


class RoleAssignment(BaseModel):
    """角色分配模式"""

    user_id: int
    role_ids: List[int]
