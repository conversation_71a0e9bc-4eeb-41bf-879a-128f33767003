#!/usr/bin/env python3
"""
安全检查脚本
"""
import json
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List


def run_command(command: List[str], cwd: Path = None) -> tuple[int, str, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, cwd=cwd, capture_output=True, text=True, check=False
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)


def check_python_dependencies():
    """检查Python依赖的安全性"""
    print("🔍 检查Python依赖安全性...")

    # 检查是否安装了safety
    returncode, _, _ = run_command(["python", "-m", "pip", "show", "safety"])
    if returncode != 0:
        print("⚠️  safety未安装，正在安装...")
        run_command(["python", "-m", "pip", "install", "safety"])

    # 运行safety检查
    returncode, stdout, stderr = run_command(
        ["python", "-m", "safety", "check", "--json"]
    )

    if returncode == 0:
        print("✅ Python依赖安全检查通过")
        return True
    else:
        print("❌ 发现安全漏洞:")
        try:
            vulnerabilities = json.loads(stdout)
            for vuln in vulnerabilities:
                print(
                    f"  - {vuln.get('package', 'Unknown')}: {vuln.get('advisory', 'No description')}"
                )
        except json.JSONDecodeError:
            print(f"  错误信息: {stderr}")
        return False


def check_code_security():
    """检查代码安全性"""
    print("\n🔍 检查代码安全性...")

    # 检查是否安装了bandit
    returncode, _, _ = run_command(["python", "-m", "pip", "show", "bandit"])
    if returncode != 0:
        print("⚠️  bandit未安装，正在安装...")
        run_command(["python", "-m", "pip", "install", "bandit"])

    # 运行bandit检查
    returncode, stdout, stderr = run_command(
        ["python", "-m", "bandit", "-r", "app/", "-f", "json", "-ll"]  # 只显示中高风险问题
    )

    if returncode == 0:
        print("✅ 代码安全检查通过")
        return True
    else:
        print("❌ 发现安全问题:")
        try:
            results = json.loads(stdout)
            for result in results.get("results", []):
                print(
                    f"  - {result.get('filename', 'Unknown')}: {result.get('issue_text', 'No description')}"
                )
        except json.JSONDecodeError:
            print(f"  错误信息: {stderr}")
        return False


def check_secrets():
    """检查是否有硬编码的密钥"""
    print("\n🔍 检查硬编码密钥...")

    dangerous_patterns = [
        "password",
        "secret",
        "key",
        "token",
        "api_key",
        "private_key",
    ]

    issues_found = []

    # 检查配置文件
    config_files = [Path("app/core/config.py"), Path(".env"), Path(".env.example")]

    for config_file in config_files:
        if config_file.exists():
            with open(config_file, "r", encoding="utf-8") as f:
                content = f.read().lower()
                for pattern in dangerous_patterns:
                    if (
                        f"{pattern} = " in content
                        and "change-in-production" not in content
                    ):
                        lines = content.split("\n")
                        for i, line in enumerate(lines, 1):
                            if f"{pattern} = " in line and not line.strip().startswith(
                                "#"
                            ):
                                issues_found.append(
                                    f"{config_file}:{i} - 可能包含硬编码的{pattern}"
                                )

    if not issues_found:
        print("✅ 未发现硬编码密钥")
        return True
    else:
        print("❌ 发现可能的硬编码密钥:")
        for issue in issues_found:
            print(f"  - {issue}")
        return False


def check_file_permissions():
    """检查文件权限"""
    print("\n🔍 检查文件权限...")

    sensitive_files = [".env", "app/core/config.py", "app/core/security.py"]

    issues_found = []

    for file_path in sensitive_files:
        path = Path(file_path)
        if path.exists():
            # 在Windows上，文件权限检查比较复杂，这里简化处理
            try:
                # 检查文件是否可读
                with open(path, "r") as f:
                    pass
                print(f"  ✅ {file_path} 权限正常")
            except PermissionError:
                issues_found.append(f"{file_path} 权限异常")

    if not issues_found:
        print("✅ 文件权限检查通过")
        return True
    else:
        print("❌ 发现权限问题:")
        for issue in issues_found:
            print(f"  - {issue}")
        return False


def generate_security_report():
    """生成安全检查报告"""
    print("\n📊 生成安全检查报告...")

    report = {
        "timestamp": subprocess.run(
            [
                "python",
                "-c",
                "import datetime; print(datetime.datetime.now().isoformat())",
            ],
            capture_output=True,
            text=True,
        ).stdout.strip(),
        "checks": {
            "dependencies": check_python_dependencies(),
            "code_security": check_code_security(),
            "secrets": check_secrets(),
            "file_permissions": check_file_permissions(),
        },
    }

    # 保存报告
    report_file = Path("security_report.json")
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"📄 安全检查报告已保存到: {report_file}")

    # 统计结果
    passed = sum(1 for check in report["checks"].values() if check)
    total = len(report["checks"])

    print(f"\n📈 安全检查结果: {passed}/{total} 项通过")

    if passed == total:
        print("🎉 所有安全检查都通过了！")
        return True
    else:
        print("⚠️  存在安全问题，请及时修复")
        return False


def main():
    """主函数"""
    print("🔒 开始安全检查...")
    print("=" * 50)

    # 确保在正确的目录
    if not Path("app").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)

    # 运行安全检查
    success = generate_security_report()

    print("=" * 50)
    if success:
        print("✅ 安全检查完成，未发现问题")
        sys.exit(0)
    else:
        print("❌ 安全检查完成，发现问题需要修复")
        sys.exit(1)


if __name__ == "__main__":
    main()
