"""
任务管理服务
"""
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.orm import Session

from app.models.app import App
from app.models.task import Task, TaskDependency, TaskExecution, TaskTemplate
from app.models.user import User
from app.schemas.task import (
    TaskBatchOperation,
    TaskCreate,
    TaskDependencyCreate,
    TaskExecuteRequest,
    TaskExecutionQuery,
    TaskExecutionStats,
    TaskQuery,
    TaskStats,
    TaskTemplateCreate,
    TaskTemplateUpdate,
    TaskUpdate,
)


class TaskService:
    """任务管理服务"""

    def __init__(self):
        self.logger = logging.getLogger("task_service")

    def create_task(self, db: Session, task_data: TaskCreate, created_by: int) -> Task:
        """创建任务"""
        try:
            # 验证关联应用是否存在
            if task_data.app_id:
                app = db.query(App).filter(App.id == task_data.app_id).first()
                if not app:
                    raise ValueError(f"应用 {task_data.app_id} 不存在")

            # 验证Cron表达式
            if task_data.schedule_type == "cron" and task_data.cron_expression:
                self._validate_cron_expression(task_data.cron_expression)

            # 创建任务
            db_task = Task(**task_data.dict(), created_by=created_by)

            # 计算下次执行时间
            if task_data.schedule_type != "manual":
                db_task.next_run_at = self._calculate_next_run_time(
                    task_data.schedule_type,
                    task_data.cron_expression,
                    task_data.interval_seconds,
                )

            db.add(db_task)
            db.commit()
            db.refresh(db_task)

            self.logger.info(f"Created task: {db_task.name} (ID: {db_task.id})")
            return db_task

        except Exception as e:
            self.logger.error(f"Failed to create task: {str(e)}")
            db.rollback()
            raise

    def get_task(self, db: Session, task_id: int) -> Optional[Task]:
        """获取单个任务"""
        return db.query(Task).filter(Task.id == task_id).first()

    def get_tasks(self, db: Session, query: TaskQuery) -> List[Task]:
        """获取任务列表"""
        try:
            db_query = db.query(Task)

            # 应用筛选条件
            if query.app_id is not None:
                db_query = db_query.filter(Task.app_id == query.app_id)

            if query.task_type is not None:
                db_query = db_query.filter(Task.task_type == query.task_type)

            if query.schedule_type is not None:
                db_query = db_query.filter(Task.schedule_type == query.schedule_type)

            if query.is_active is not None:
                db_query = db_query.filter(Task.is_active == query.is_active)

            if query.created_by is not None:
                db_query = db_query.filter(Task.created_by == query.created_by)

            if query.keyword:
                keyword_filter = or_(
                    Task.name.contains(query.keyword),
                    Task.description.contains(query.keyword),
                    Task.command.contains(query.keyword),
                )
                db_query = db_query.filter(keyword_filter)

            # 排序和分页
            tasks = (
                db_query.order_by(desc(Task.created_at))
                .offset(query.skip)
                .limit(query.limit)
                .all()
            )

            return tasks

        except Exception as e:
            self.logger.error(f"Failed to get tasks: {str(e)}")
            raise

    def count_tasks(self, db: Session, query: TaskQuery) -> int:
        """统计任务数量"""
        try:
            db_query = db.query(func.count(Task.id))

            # 应用筛选条件
            if query.app_id is not None:
                db_query = db_query.filter(Task.app_id == query.app_id)

            if query.task_type is not None:
                db_query = db_query.filter(Task.task_type == query.task_type)

            if query.schedule_type is not None:
                db_query = db_query.filter(Task.schedule_type == query.schedule_type)

            if query.is_active is not None:
                db_query = db_query.filter(Task.is_active == query.is_active)

            if query.created_by is not None:
                db_query = db_query.filter(Task.created_by == query.created_by)

            if query.keyword:
                keyword_filter = or_(
                    Task.name.contains(query.keyword),
                    Task.description.contains(query.keyword),
                    Task.command.contains(query.command),
                )
                db_query = db_query.filter(keyword_filter)

            return db_query.scalar()

        except Exception as e:
            self.logger.error(f"Failed to count tasks: {str(e)}")
            raise

    def update_task(
        self, db: Session, task_id: int, task_data: TaskUpdate
    ) -> Optional[Task]:
        """更新任务"""
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return None

            # 验证关联应用是否存在
            if task_data.app_id is not None:
                app = db.query(App).filter(App.id == task_data.app_id).first()
                if not app:
                    raise ValueError(f"应用 {task_data.app_id} 不存在")

            # 验证Cron表达式
            if task_data.schedule_type == "cron" and task_data.cron_expression:
                self._validate_cron_expression(task_data.cron_expression)

            # 更新字段
            update_data = task_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(task, field, value)

            # 重新计算下次执行时间
            if (
                task_data.schedule_type is not None
                and task_data.schedule_type != "manual"
            ):
                task.next_run_at = self._calculate_next_run_time(
                    task.schedule_type, task.cron_expression, task.interval_seconds
                )
            elif task_data.schedule_type == "manual":
                task.next_run_at = None

            db.commit()
            db.refresh(task)

            self.logger.info(f"Updated task: {task.name} (ID: {task.id})")
            return task

        except Exception as e:
            self.logger.error(f"Failed to update task {task_id}: {str(e)}")
            db.rollback()
            raise

    def delete_task(self, db: Session, task_id: int) -> bool:
        """删除任务"""
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return False

            # 检查是否有正在运行的执行
            running_executions = (
                db.query(TaskExecution)
                .filter(
                    and_(
                        TaskExecution.task_id == task_id,
                        TaskExecution.status == "running",
                    )
                )
                .count()
            )

            if running_executions > 0:
                raise ValueError("无法删除正在运行的任务")

            db.delete(task)
            db.commit()

            self.logger.info(f"Deleted task: {task.name} (ID: {task.id})")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete task {task_id}: {str(e)}")
            db.rollback()
            raise

    def batch_operation(
        self, db: Session, operation: TaskBatchOperation
    ) -> Dict[str, Any]:
        """批量操作任务"""
        try:
            results = {"success_count": 0, "failed_count": 0, "errors": []}

            for task_id in operation.task_ids:
                try:
                    task = db.query(Task).filter(Task.id == task_id).first()
                    if not task:
                        results["errors"].append(f"任务 {task_id} 不存在")
                        results["failed_count"] += 1
                        continue

                    if operation.operation == "activate":
                        task.is_active = True
                    elif operation.operation == "deactivate":
                        task.is_active = False
                    elif operation.operation == "delete":
                        # 检查是否有正在运行的执行
                        running_executions = (
                            db.query(TaskExecution)
                            .filter(
                                and_(
                                    TaskExecution.task_id == task_id,
                                    TaskExecution.status == "running",
                                )
                            )
                            .count()
                        )

                        if running_executions > 0:
                            results["errors"].append(f"任务 {task_id} 正在运行，无法删除")
                            results["failed_count"] += 1
                            continue

                        db.delete(task)

                    results["success_count"] += 1

                except Exception as e:
                    results["errors"].append(f"任务 {task_id}: {str(e)}")
                    results["failed_count"] += 1

            db.commit()
            return results

        except Exception as e:
            self.logger.error(f"Failed to perform batch operation: {str(e)}")
            db.rollback()
            raise

    def get_task_stats(self, db: Session, app_id: Optional[int] = None) -> TaskStats:
        """获取任务统计信息"""
        try:
            base_query = db.query(Task)
            if app_id:
                base_query = base_query.filter(Task.app_id == app_id)

            # 基础统计
            total_tasks = base_query.count()
            active_tasks = base_query.filter(Task.is_active == True).count()
            inactive_tasks = base_query.filter(Task.is_active == False).count()
            scheduled_tasks = base_query.filter(Task.schedule_type != "manual").count()
            manual_tasks = base_query.filter(Task.schedule_type == "manual").count()

            # 按类型统计
            type_stats = db.query(Task.task_type, func.count(Task.id)).group_by(
                Task.task_type
            )

            if app_id:
                type_stats = type_stats.filter(Task.app_id == app_id)

            by_type = {task_type: count for task_type, count in type_stats.all()}

            # 按状态统计（基于最近执行状态）
            status_stats = (
                db.query(
                    TaskExecution.status,
                    func.count(func.distinct(TaskExecution.task_id)),
                )
                .join(Task)
                .group_by(TaskExecution.status)
            )

            if app_id:
                status_stats = status_stats.filter(Task.app_id == app_id)

            by_status = {status: count for status, count in status_stats.all()}

            return TaskStats(
                total_tasks=total_tasks,
                active_tasks=active_tasks,
                inactive_tasks=inactive_tasks,
                scheduled_tasks=scheduled_tasks,
                manual_tasks=manual_tasks,
                by_type=by_type,
                by_status=by_status,
            )

        except Exception as e:
            self.logger.error(f"Failed to get task stats: {str(e)}")
            raise

    def get_execution_stats(
        self, db: Session, task_id: Optional[int] = None, days: int = 30
    ) -> TaskExecutionStats:
        """获取执行统计信息"""
        try:
            time_threshold = datetime.now() - timedelta(days=days)

            base_query = db.query(TaskExecution).filter(
                TaskExecution.created_at >= time_threshold
            )

            if task_id:
                base_query = base_query.filter(TaskExecution.task_id == task_id)

            # 基础统计
            total_executions = base_query.count()
            success_executions = base_query.filter(
                TaskExecution.status == "success"
            ).count()
            failed_executions = base_query.filter(
                TaskExecution.status == "failed"
            ).count()
            running_executions = base_query.filter(
                TaskExecution.status == "running"
            ).count()

            # 平均执行时长
            avg_duration = db.query(func.avg(TaskExecution.duration_seconds)).filter(
                and_(
                    TaskExecution.created_at >= time_threshold,
                    TaskExecution.status == "success",
                    TaskExecution.duration_seconds.isnot(None),
                )
            )

            if task_id:
                avg_duration = avg_duration.filter(TaskExecution.task_id == task_id)

            average_duration = avg_duration.scalar()

            # 成功率
            success_rate = (
                (success_executions / total_executions * 100)
                if total_executions > 0
                else 0
            )

            return TaskExecutionStats(
                total_executions=total_executions,
                success_executions=success_executions,
                failed_executions=failed_executions,
                running_executions=running_executions,
                average_duration=average_duration,
                success_rate=success_rate,
            )

        except Exception as e:
            self.logger.error(f"Failed to get execution stats: {str(e)}")
            raise

    def _validate_cron_expression(self, cron_expression: str) -> bool:
        """验证Cron表达式"""
        try:
            from croniter import croniter

            return croniter.is_valid(cron_expression)
        except ImportError:
            # 如果没有安装croniter，进行简单验证
            parts = cron_expression.split()
            if len(parts) != 5:
                raise ValueError("Cron表达式必须包含5个部分")
            return True
        except Exception as e:
            raise ValueError(f"无效的Cron表达式: {str(e)}")

    def _calculate_next_run_time(
        self,
        schedule_type: str,
        cron_expression: Optional[str],
        interval_seconds: Optional[int],
    ) -> Optional[datetime]:
        """计算下次执行时间"""
        try:
            now = datetime.now()

            if schedule_type == "cron" and cron_expression:
                try:
                    from croniter import croniter

                    cron = croniter(cron_expression, now)
                    return cron.get_next(datetime)
                except ImportError:
                    # 简单的时间计算，仅支持基本格式
                    return now + timedelta(hours=1)  # 默认1小时后

            elif schedule_type == "interval" and interval_seconds:
                return now + timedelta(seconds=interval_seconds)

            return None

        except Exception as e:
            self.logger.error(f"Failed to calculate next run time: {str(e)}")
            return None


class TaskTemplateService:
    """任务模板服务"""

    def __init__(self):
        self.logger = logging.getLogger("task_template_service")

    def create_template(
        self, db: Session, template_data: TaskTemplateCreate, created_by: int
    ) -> TaskTemplate:
        """创建任务模板"""
        try:
            db_template = TaskTemplate(**template_data.dict(), created_by=created_by)

            db.add(db_template)
            db.commit()
            db.refresh(db_template)

            self.logger.info(
                f"Created task template: {db_template.name} (ID: {db_template.id})"
            )
            return db_template

        except Exception as e:
            self.logger.error(f"Failed to create task template: {str(e)}")
            db.rollback()
            raise

    def get_templates(
        self,
        db: Session,
        category: Optional[str] = None,
        is_public: Optional[bool] = None,
        created_by: Optional[int] = None,
    ) -> List[TaskTemplate]:
        """获取任务模板列表"""
        try:
            query = db.query(TaskTemplate)

            if category:
                query = query.filter(TaskTemplate.category == category)

            if is_public is not None:
                query = query.filter(TaskTemplate.is_public == is_public)

            if created_by is not None:
                query = query.filter(TaskTemplate.created_by == created_by)

            return query.order_by(
                desc(TaskTemplate.usage_count), desc(TaskTemplate.created_at)
            ).all()

        except Exception as e:
            self.logger.error(f"Failed to get task templates: {str(e)}")
            raise

    def use_template(self, db: Session, template_id: int) -> Optional[TaskTemplate]:
        """使用模板（增加使用次数）"""
        try:
            template = (
                db.query(TaskTemplate).filter(TaskTemplate.id == template_id).first()
            )
            if template:
                template.usage_count += 1
                db.commit()
                db.refresh(template)

            return template

        except Exception as e:
            self.logger.error(f"Failed to use template {template_id}: {str(e)}")
            db.rollback()
            raise


# 全局服务实例
task_service = TaskService()
task_template_service = TaskTemplateService()
