/**
 * API 相关类型定义
 */

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  size?: number
  search?: string
  sort?: string
  order?: 'asc' | 'desc'
}

// 错误响应类型
export interface ApiError {
  message: string
  error_code?: string
  details?: Record<string, any>
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置类型
export interface RequestConfig {
  url: string
  method?: HttpMethod
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
}

// 文件上传响应
export interface UploadResponse {
  filename: string
  url: string
  size: number
  type: string
}

// 批量操作响应
export interface BatchOperationResponse {
  success_count: number
  failed_count: number
  errors?: Array<{
    id: string | number
    error: string
  }>
}

// 状态类型
export type Status = 'active' | 'inactive' | 'pending' | 'error'

// 日志级别类型
export type LogLevel = 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'

// 排序方向
export type SortOrder = 'asc' | 'desc'

// 时间范围类型
export interface TimeRange {
  start: string
  end: string
}

// 统计数据类型
export interface Statistics {
  total: number
  active: number
  inactive: number
  error: number
}

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  version: string
  timestamp: string
  checks?: Record<string, {
    status: 'pass' | 'fail'
    message?: string
  }>
}

// WebSocket消息类型
export interface WebSocketMessage<T = any> {
  type: string
  data: T
  timestamp: string
}

// 实时状态更新
export interface RealtimeUpdate {
  type: 'status_change' | 'log_update' | 'metric_update'
  target_id: string
  data: any
}
