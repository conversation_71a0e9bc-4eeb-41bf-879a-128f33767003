"""
任务执行记录服务
"""
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.orm import Session

from app.models.task import Task, TaskExecution
from app.schemas.task import TaskExecution as TaskExecutionSchema
from app.schemas.task import TaskExecutionQuery, TaskExecutionStats, TaskExecutionUpdate


class TaskExecutionService:
    """任务执行记录服务"""

    def __init__(self):
        self.logger = logging.getLogger("task_execution_service")

    def get_execution(self, db: Session, execution_id: int) -> Optional[TaskExecution]:
        """获取单个执行记录"""
        return db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()

    def get_executions(
        self, db: Session, query: TaskExecutionQuery
    ) -> List[TaskExecution]:
        """获取执行记录列表"""
        try:
            db_query = db.query(TaskExecution)

            # 应用筛选条件
            if query.task_id is not None:
                db_query = db_query.filter(TaskExecution.task_id == query.task_id)

            if query.status is not None:
                db_query = db_query.filter(TaskExecution.status == query.status)

            if query.start_time is not None:
                db_query = db_query.filter(TaskExecution.created_at >= query.start_time)

            if query.end_time is not None:
                db_query = db_query.filter(TaskExecution.created_at <= query.end_time)

            # 排序和分页
            executions = (
                db_query.order_by(desc(TaskExecution.created_at))
                .offset(query.skip)
                .limit(query.limit)
                .all()
            )

            return executions

        except Exception as e:
            self.logger.error(f"Failed to get executions: {str(e)}")
            raise

    def count_executions(self, db: Session, query: TaskExecutionQuery) -> int:
        """统计执行记录数量"""
        try:
            db_query = db.query(func.count(TaskExecution.id))

            # 应用筛选条件
            if query.task_id is not None:
                db_query = db_query.filter(TaskExecution.task_id == query.task_id)

            if query.status is not None:
                db_query = db_query.filter(TaskExecution.status == query.status)

            if query.start_time is not None:
                db_query = db_query.filter(TaskExecution.created_at >= query.start_time)

            if query.end_time is not None:
                db_query = db_query.filter(TaskExecution.created_at <= query.end_time)

            return db_query.scalar()

        except Exception as e:
            self.logger.error(f"Failed to count executions: {str(e)}")
            raise

    def update_execution(
        self, db: Session, execution_id: int, execution_data: TaskExecutionUpdate
    ) -> Optional[TaskExecution]:
        """更新执行记录"""
        try:
            execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not execution:
                return None

            # 更新字段
            update_data = execution_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(execution, field, value)

            # 如果状态变为完成状态，计算执行时长
            if execution_data.status in ["success", "failed", "timeout", "cancelled"]:
                if execution_data.finished_at and execution.started_at:
                    execution.duration_seconds = int(
                        (
                            execution_data.finished_at - execution.started_at
                        ).total_seconds()
                    )
                elif not execution_data.finished_at and execution.started_at:
                    execution.finished_at = datetime.now()
                    execution.duration_seconds = int(
                        (execution.finished_at - execution.started_at).total_seconds()
                    )

            db.commit()
            db.refresh(execution)

            self.logger.info(f"Updated execution {execution_id}: {execution.status}")
            return execution

        except Exception as e:
            self.logger.error(f"Failed to update execution {execution_id}: {str(e)}")
            db.rollback()
            raise

    def delete_execution(self, db: Session, execution_id: int) -> bool:
        """删除执行记录"""
        try:
            execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not execution:
                return False

            # 只能删除已完成的执行记录
            if execution.status == "running":
                raise ValueError("无法删除正在运行的执行记录")

            db.delete(execution)
            db.commit()

            self.logger.info(f"Deleted execution {execution_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete execution {execution_id}: {str(e)}")
            db.rollback()
            raise

    def get_execution_stats(
        self, db: Session, task_id: Optional[int] = None, days: int = 30
    ) -> TaskExecutionStats:
        """获取执行统计信息"""
        try:
            time_threshold = datetime.now() - timedelta(days=days)

            base_query = db.query(TaskExecution).filter(
                TaskExecution.created_at >= time_threshold
            )

            if task_id:
                base_query = base_query.filter(TaskExecution.task_id == task_id)

            # 基础统计
            total_executions = base_query.count()
            success_executions = base_query.filter(
                TaskExecution.status == "success"
            ).count()
            failed_executions = base_query.filter(
                TaskExecution.status == "failed"
            ).count()
            running_executions = base_query.filter(
                TaskExecution.status == "running"
            ).count()

            # 平均执行时长
            avg_duration = db.query(func.avg(TaskExecution.duration_seconds)).filter(
                and_(
                    TaskExecution.created_at >= time_threshold,
                    TaskExecution.status == "success",
                    TaskExecution.duration_seconds.isnot(None),
                )
            )

            if task_id:
                avg_duration = avg_duration.filter(TaskExecution.task_id == task_id)

            average_duration = avg_duration.scalar()

            # 成功率
            success_rate = (
                (success_executions / total_executions * 100)
                if total_executions > 0
                else 0
            )

            return TaskExecutionStats(
                total_executions=total_executions,
                success_executions=success_executions,
                failed_executions=failed_executions,
                running_executions=running_executions,
                average_duration=average_duration,
                success_rate=success_rate,
            )

        except Exception as e:
            self.logger.error(f"Failed to get execution stats: {str(e)}")
            raise

    def get_execution_trends(
        self, db: Session, task_id: Optional[int] = None, days: int = 30
    ) -> Dict[str, Any]:
        """获取执行趋势数据"""
        try:
            time_threshold = datetime.now() - timedelta(days=days)

            # 按日期分组统计
            date_stats = db.query(
                func.date(TaskExecution.created_at).label("date"),
                func.count(TaskExecution.id).label("total"),
                func.sum(
                    func.case([(TaskExecution.status == "success", 1)], else_=0)
                ).label("success"),
                func.sum(
                    func.case([(TaskExecution.status == "failed", 1)], else_=0)
                ).label("failed"),
                func.avg(TaskExecution.duration_seconds).label("avg_duration"),
            ).filter(TaskExecution.created_at >= time_threshold)

            if task_id:
                date_stats = date_stats.filter(TaskExecution.task_id == task_id)

            date_stats = (
                date_stats.group_by(func.date(TaskExecution.created_at))
                .order_by(func.date(TaskExecution.created_at))
                .all()
            )

            # 按小时分组统计（最近24小时）
            hour_threshold = datetime.now() - timedelta(hours=24)
            hour_stats = db.query(
                func.extract("hour", TaskExecution.created_at).label("hour"),
                func.count(TaskExecution.id).label("total"),
                func.sum(
                    func.case([(TaskExecution.status == "success", 1)], else_=0)
                ).label("success"),
                func.sum(
                    func.case([(TaskExecution.status == "failed", 1)], else_=0)
                ).label("failed"),
            ).filter(TaskExecution.created_at >= hour_threshold)

            if task_id:
                hour_stats = hour_stats.filter(TaskExecution.task_id == task_id)

            hour_stats = (
                hour_stats.group_by(func.extract("hour", TaskExecution.created_at))
                .order_by(func.extract("hour", TaskExecution.created_at))
                .all()
            )

            return {
                "daily_trends": [
                    {
                        "date": stat.date.isoformat(),
                        "total": stat.total,
                        "success": stat.success,
                        "failed": stat.failed,
                        "avg_duration": float(stat.avg_duration)
                        if stat.avg_duration
                        else None,
                    }
                    for stat in date_stats
                ],
                "hourly_trends": [
                    {
                        "hour": int(stat.hour),
                        "total": stat.total,
                        "success": stat.success,
                        "failed": stat.failed,
                    }
                    for stat in hour_stats
                ],
            }

        except Exception as e:
            self.logger.error(f"Failed to get execution trends: {str(e)}")
            raise

    def get_task_execution_summary(self, db: Session, task_id: int) -> Dict[str, Any]:
        """获取任务执行摘要"""
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")

            # 基础统计
            total_executions = (
                db.query(func.count(TaskExecution.id))
                .filter(TaskExecution.task_id == task_id)
                .scalar()
            )

            # 最近执行
            latest_execution = (
                db.query(TaskExecution)
                .filter(TaskExecution.task_id == task_id)
                .order_by(desc(TaskExecution.created_at))
                .first()
            )

            # 成功率统计
            success_count = (
                db.query(func.count(TaskExecution.id))
                .filter(
                    and_(
                        TaskExecution.task_id == task_id,
                        TaskExecution.status == "success",
                    )
                )
                .scalar()
            )

            success_rate = (
                (success_count / total_executions * 100) if total_executions > 0 else 0
            )

            # 平均执行时长
            avg_duration = (
                db.query(func.avg(TaskExecution.duration_seconds))
                .filter(
                    and_(
                        TaskExecution.task_id == task_id,
                        TaskExecution.status == "success",
                        TaskExecution.duration_seconds.isnot(None),
                    )
                )
                .scalar()
            )

            # 最近失败的执行
            recent_failures = (
                db.query(TaskExecution)
                .filter(
                    and_(
                        TaskExecution.task_id == task_id,
                        TaskExecution.status == "failed",
                    )
                )
                .order_by(desc(TaskExecution.created_at))
                .limit(5)
                .all()
            )

            return {
                "task_id": task_id,
                "task_name": task.name,
                "total_executions": total_executions,
                "success_rate": success_rate,
                "average_duration": float(avg_duration) if avg_duration else None,
                "latest_execution": {
                    "id": latest_execution.id,
                    "status": latest_execution.status,
                    "created_at": latest_execution.created_at.isoformat(),
                    "duration_seconds": latest_execution.duration_seconds,
                }
                if latest_execution
                else None,
                "recent_failures": [
                    {
                        "id": execution.id,
                        "created_at": execution.created_at.isoformat(),
                        "error_message": execution.error_message,
                        "duration_seconds": execution.duration_seconds,
                    }
                    for execution in recent_failures
                ],
            }

        except Exception as e:
            self.logger.error(f"Failed to get task execution summary: {str(e)}")
            raise

    def cleanup_old_executions(self, db: Session, days: int = 90) -> int:
        """清理旧的执行记录"""
        try:
            time_threshold = datetime.now() - timedelta(days=days)

            # 只清理已完成的执行记录
            deleted_count = (
                db.query(TaskExecution)
                .filter(
                    and_(
                        TaskExecution.created_at < time_threshold,
                        TaskExecution.status.in_(
                            ["success", "failed", "timeout", "cancelled"]
                        ),
                    )
                )
                .delete()
            )

            db.commit()

            self.logger.info(f"Cleaned up {deleted_count} old execution records")
            return deleted_count

        except Exception as e:
            self.logger.error(f"Failed to cleanup old executions: {str(e)}")
            db.rollback()
            raise

    def retry_failed_execution(
        self, db: Session, execution_id: int
    ) -> Optional[TaskExecution]:
        """重试失败的执行"""
        try:
            original_execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not original_execution:
                return None

            if original_execution.status not in ["failed", "timeout"]:
                raise ValueError("只能重试失败或超时的执行")

            # 检查重试次数限制
            task = db.query(Task).filter(Task.id == original_execution.task_id).first()
            if not task:
                raise ValueError("关联的任务不存在")

            if original_execution.retry_count >= task.max_retries:
                raise ValueError("已达到最大重试次数")

            # 创建重试执行
            from app.services.task_executor import task_executor

            retry_execution = task_executor.execute_task(db, task.id)

            # 更新重试信息
            retry_execution.is_retry = True
            retry_execution.parent_execution_id = execution_id
            retry_execution.retry_count = original_execution.retry_count + 1

            db.commit()
            db.refresh(retry_execution)

            self.logger.info(
                f"Created retry execution {retry_execution.id} for {execution_id}"
            )
            return retry_execution

        except Exception as e:
            self.logger.error(f"Failed to retry execution {execution_id}: {str(e)}")
            db.rollback()
            raise

    def get_execution_logs(self, db: Session, execution_id: int) -> Dict[str, str]:
        """获取执行日志"""
        try:
            execution = (
                db.query(TaskExecution).filter(TaskExecution.id == execution_id).first()
            )
            if not execution:
                raise ValueError(f"执行记录 {execution_id} 不存在")

            return {
                "stdout": execution.stdout or "",
                "stderr": execution.stderr or "",
                "error_message": execution.error_message or "",
            }

        except Exception as e:
            self.logger.error(f"Failed to get execution logs: {str(e)}")
            raise


# 全局执行记录服务实例
task_execution_service = TaskExecutionService()
