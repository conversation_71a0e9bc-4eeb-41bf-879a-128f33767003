# 前端环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改

# 应用基本信息
VITE_APP_TITLE=应用项目管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=应用项目管理系统前端

# API 配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# WebSocket 配置
VITE_WS_URL=ws://localhost:8000/ws

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_PWA=false

# 主题配置
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true

# 国际化配置
VITE_DEFAULT_LOCALE=zh-CN
VITE_ENABLE_I18N=false

# 性能配置
VITE_ENABLE_LAZY_LOADING=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000

# 开发配置
VITE_DEV_SERVER_PORT=3000
VITE_DEV_SERVER_HOST=localhost
VITE_DEV_OPEN_BROWSER=true

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_DROP_CONSOLE=true
VITE_BUILD_DROP_DEBUGGER=true

# 监控配置
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_ID=

# 错误监控
VITE_ENABLE_ERROR_MONITORING=false
VITE_SENTRY_DSN=

# CDN 配置
VITE_CDN_URL=
VITE_ENABLE_CDN=false
