"""
性能优化工具
"""
import time
import functools
import logging
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
from contextlib import contextmanager
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy import text, event
from sqlalchemy.engine import Engine

from app.core.logging import get_logger

logger = get_logger("performance")

F = TypeVar('F', bound=Callable[..., Any])


def timing_decorator(func: F) -> F:
    """性能计时装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(
                f"Function {func.__name__} executed in {execution_time:.4f} seconds"
            )
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Function {func.__name__} failed after {execution_time:.4f} seconds: {e}"
            )
            raise
    return wrapper


@contextmanager
def performance_monitor(operation_name: str):
    """性能监控上下文管理器"""
    start_time = time.time()
    start_memory = None
    
    try:
        import psutil
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
    except ImportError:
        pass
    
    try:
        yield
    finally:
        execution_time = time.time() - start_time
        
        memory_info = ""
        if start_memory:
            try:
                end_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_diff = end_memory - start_memory
                memory_info = f", Memory: {memory_diff:+.2f}MB"
            except:
                pass
        
        logger.info(
            f"Operation '{operation_name}' completed in {execution_time:.4f}s{memory_info}"
        )


class DatabasePerformanceMonitor:
    """数据库性能监控"""
    
    def __init__(self):
        self.query_stats: Dict[str, List[float]] = {}
        self.slow_query_threshold = 1.0  # 秒
    
    def log_query(self, query: str, execution_time: float):
        """记录查询性能"""
        if query not in self.query_stats:
            self.query_stats[query] = []
        
        self.query_stats[query].append(execution_time)
        
        if execution_time > self.slow_query_threshold:
            logger.warning(
                f"Slow query detected ({execution_time:.4f}s): {query[:100]}..."
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {}
        for query, times in self.query_stats.items():
            stats[query[:50]] = {
                "count": len(times),
                "avg_time": sum(times) / len(times),
                "max_time": max(times),
                "min_time": min(times)
            }
        return stats


# 全局数据库性能监控器
db_monitor = DatabasePerformanceMonitor()


@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询执行前的事件处理"""
    context._query_start_time = time.time()


@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询执行后的事件处理"""
    total = time.time() - context._query_start_time
    db_monitor.log_query(statement, total)


class QueryOptimizer:
    """查询优化器"""
    
    @staticmethod
    def optimize_pagination(query, page: int, size: int, max_size: int = 100):
        """优化分页查询"""
        # 限制每页大小
        size = min(size, max_size)
        offset = (page - 1) * size
        
        return query.offset(offset).limit(size)
    
    @staticmethod
    def add_eager_loading(query, *relationships):
        """添加预加载关系"""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(selectinload(relationship))
            else:
                query = query.options(relationship)
        return query
    
    @staticmethod
    def add_joined_loading(query, *relationships):
        """添加连接加载"""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(joinedload(relationship))
            else:
                query = query.options(relationship)
        return query


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._cache_times: Dict[str, float] = {}
        self.default_ttl = 300  # 5分钟
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if key in self._cache:
            if time.time() - self._cache_times[key] < self.default_ttl:
                return self._cache[key]
            else:
                # 缓存过期，清理
                del self._cache[key]
                del self._cache_times[key]
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存"""
        self._cache[key] = value
        self._cache_times[key] = time.time()
    
    def delete(self, key: str):
        """删除缓存"""
        if key in self._cache:
            del self._cache[key]
            del self._cache_times[key]
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_times.clear()


# 全局缓存管理器
cache_manager = CacheManager()


def cached(ttl: int = 300, key_func: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            logger.debug(f"Cache set for {cache_key}")
            
            return result
        return wrapper
    return decorator


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
    
    def process_in_batches(self, items: List[Any], processor: Callable[[List[Any]], Any]):
        """批量处理数据"""
        results = []
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            with performance_monitor(f"Batch processing {len(batch)} items"):
                batch_result = processor(batch)
                results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
        return results


def optimize_database_session(db: Session) -> Session:
    """优化数据库会话"""
    # 设置查询超时
    db.execute(text("SET statement_timeout = '30s'"))
    
    # 设置工作内存
    db.execute(text("SET work_mem = '256MB'"))
    
    return db


def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计信息"""
    return {
        "database_queries": db_monitor.get_stats(),
        "cache_stats": {
            "cache_size": len(cache_manager._cache),
            "cache_keys": list(cache_manager._cache.keys())[:10]  # 只显示前10个
        }
    }
