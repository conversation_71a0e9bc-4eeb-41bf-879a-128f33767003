"""
集成测试配置
"""
import asyncio
from typing import Generator

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.auth import get_current_user
from app.core.database import Base, get_db
from app.main import app
from app.models.user import User

# 集成测试数据库配置
INTEGRATION_DATABASE_URL = "sqlite:///./integration_test.db"

integration_engine = create_engine(
    INTEGRATION_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

IntegrationSessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=integration_engine
)


def override_get_db():
    """覆盖数据库依赖"""
    try:
        db = IntegrationSessionLocal()
        yield db
    finally:
        db.close()


def override_get_current_user():
    """覆盖当前用户依赖"""
    return User(
        id=1,
        username="integrationuser",
        email="<EMAIL>",
        is_active=True,
        is_superuser=False,
    )


# 覆盖依赖
app.dependency_overrides[get_db] = override_get_db
app.dependency_overrides[get_current_user] = override_get_current_user


@pytest.fixture(scope="session")
def integration_event_loop():
    """创建集成测试事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def integration_db_engine():
    """集成测试数据库引擎"""
    Base.metadata.create_all(bind=integration_engine)
    yield integration_engine
    Base.metadata.drop_all(bind=integration_engine)


@pytest.fixture(scope="function")
def integration_db_session(integration_db_engine):
    """集成测试数据库会话"""
    connection = integration_db_engine.connect()
    transaction = connection.begin()
    session = IntegrationSessionLocal(bind=connection)

    yield session

    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def integration_client():
    """集成测试客户端"""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def integration_user_data():
    """集成测试用户数据"""
    return {
        "username": "integrationuser",
        "email": "<EMAIL>",
        "password": "integration123",
        "is_active": True,
        "is_superuser": False,
    }


@pytest.fixture
def integration_app_data():
    """集成测试应用数据"""
    return {
        "name": "Integration Test App",
        "description": "Integration test application",
        "frontend_dir": "/tmp/integration/frontend",
        "backend_dir": "/tmp/integration/backend",
        "frontend_start_cmd": "npm start",
        "backend_start_cmd": "python main.py",
        "frontend_stop_cmd": "pkill -f npm",
        "backend_stop_cmd": "pkill -f python",
        "frontend_port": 3000,
        "backend_port": 8000,
        "is_active": True,
    }


@pytest.fixture
def integration_task_data():
    """集成测试任务数据"""
    return {
        "name": "Integration Test Task",
        "description": "Integration test task description",
        "task_type": "command",
        "command": "echo 'Integration Test'",
        "schedule_type": "manual",
        "is_active": True,
        "max_retries": 3,
        "timeout_seconds": 300,
    }


@pytest.fixture
def integration_log_data():
    """集成测试日志数据"""
    return {
        "service_type": "system",
        "level": "INFO",
        "message": "Integration test log message",
    }


@pytest.fixture
def integration_alert_rule_data():
    """集成测试告警规则数据"""
    return {
        "name": "Integration CPU Alert",
        "description": "Integration test CPU alert",
        "metric_name": "cpu_percent",
        "threshold_value": 80.0,
        "comparison_operator": ">",
        "severity": "high",
        "duration_minutes": 5,
        "evaluation_interval": 60,
        "notification_enabled": True,
        "is_active": True,
    }


class IntegrationTestHelper:
    """集成测试辅助类"""

    def __init__(self, client: TestClient, db_session):
        self.client = client
        self.db_session = db_session
        self.token = None
        self.headers = {}

    def login(self, username="admin", password="admin123"):
        """登录获取token"""
        response = self.client.post(
            "/api/v1/auth/login", data={"username": username, "password": password}
        )

        if response.status_code == 200:
            self.token = response.json()["access_token"]
            self.headers = {"Authorization": f"Bearer {self.token}"}
            return True
        return False

    def create_test_user(self, user_data):
        """创建测试用户"""
        from app.core.security import get_password_hash

        user = User(
            username=user_data["username"],
            email=user_data["email"],
            hashed_password=get_password_hash(user_data["password"]),
            is_active=user_data.get("is_active", True),
            is_superuser=user_data.get("is_superuser", False),
        )

        self.db_session.add(user)
        self.db_session.commit()
        return user

    def create_test_app(self, app_data, user_id=1):
        """创建测试应用"""
        from app.models.app import App

        app = App(
            name=app_data["name"],
            description=app_data.get("description"),
            frontend_dir=app_data.get("frontend_dir"),
            backend_dir=app_data.get("backend_dir"),
            frontend_start_cmd=app_data.get("frontend_start_cmd"),
            backend_start_cmd=app_data.get("backend_start_cmd"),
            frontend_stop_cmd=app_data.get("frontend_stop_cmd"),
            backend_stop_cmd=app_data.get("backend_stop_cmd"),
            frontend_port=app_data.get("frontend_port"),
            backend_port=app_data.get("backend_port"),
            is_active=app_data.get("is_active", True),
            created_by=user_id,
        )

        self.db_session.add(app)
        self.db_session.commit()
        return app

    def create_test_task(self, task_data, app_id, user_id=1):
        """创建测试任务"""
        from app.models.task import Task

        task = Task(
            app_id=app_id,
            name=task_data["name"],
            description=task_data.get("description"),
            task_type=task_data["task_type"],
            command=task_data.get("command"),
            schedule_type=task_data.get("schedule_type", "manual"),
            is_active=task_data.get("is_active", True),
            max_retries=task_data.get("max_retries", 3),
            timeout_seconds=task_data.get("timeout_seconds", 300),
            created_by=user_id,
        )

        self.db_session.add(task)
        self.db_session.commit()
        return task

    def cleanup(self):
        """清理测试数据"""
        # 这里可以添加清理逻辑
        pass


@pytest.fixture
def integration_helper(integration_client, integration_db_session):
    """集成测试辅助工具"""
    helper = IntegrationTestHelper(integration_client, integration_db_session)
    yield helper
    helper.cleanup()


# 性能测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "performance: mark test as performance test")
    config.addinivalue_line("markers", "e2e: mark test as end-to-end test")
    config.addinivalue_line("markers", "slow: mark test as slow running test")
