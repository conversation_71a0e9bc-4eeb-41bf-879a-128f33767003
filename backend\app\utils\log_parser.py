"""
日志解析工具
"""
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class LogParser:
    """日志解析器"""

    # 常见日志格式的正则表达式
    LOG_PATTERNS = {
        "standard": r"^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \[(\w+)\] (.+)$",
        "django": r"^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) (\w+) (.+)$",
        "nginx": r'^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) - - \[([^\]]+)\] "([^"]+)" (\d{3}) (\d+) "([^"]*)" "([^"]*)"$',
        "apache": r'^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) - - \[([^\]]+)\] "([^"]+)" (\d{3}) (\d+)$',
        "syslog": r"^(\w{3} \d{1,2} \d{2}:\d{2}:\d{2}) (\w+) (\w+): (.+)$",
    }

    @staticmethod
    def parse_log_line(line: str, pattern_name: str = "standard") -> Optional[Dict]:
        """
        解析单行日志

        Args:
            line: 日志行
            pattern_name: 模式名称

        Returns:
            解析结果字典或None
        """
        if pattern_name not in LogParser.LOG_PATTERNS:
            return None

        pattern = LogParser.LOG_PATTERNS[pattern_name]
        match = re.match(pattern, line.strip())

        if not match:
            return None

        if pattern_name == "standard":
            return {
                "timestamp": match.group(1),
                "level": match.group(2),
                "message": match.group(3),
            }
        elif pattern_name == "django":
            return {
                "timestamp": match.group(1),
                "level": match.group(2),
                "message": match.group(3),
            }
        elif pattern_name == "nginx":
            return {
                "ip": match.group(1),
                "timestamp": match.group(2),
                "request": match.group(3),
                "status": int(match.group(4)),
                "size": int(match.group(5)),
                "referer": match.group(6),
                "user_agent": match.group(7),
            }
        elif pattern_name == "apache":
            return {
                "ip": match.group(1),
                "timestamp": match.group(2),
                "request": match.group(3),
                "status": int(match.group(4)),
                "size": int(match.group(5)),
            }
        elif pattern_name == "syslog":
            return {
                "timestamp": match.group(1),
                "host": match.group(2),
                "process": match.group(3),
                "message": match.group(4),
            }

        return None

    @staticmethod
    def detect_log_format(file_path: Path, sample_lines: int = 10) -> str:
        """
        检测日志格式

        Args:
            file_path: 日志文件路径
            sample_lines: 采样行数

        Returns:
            检测到的格式名称
        """
        if not file_path.exists():
            return "standard"

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = [f.readline().strip() for _ in range(sample_lines)]

            # 统计各格式的匹配数
            format_scores = {}

            for pattern_name in LogParser.LOG_PATTERNS:
                score = 0
                for line in lines:
                    if line and LogParser.parse_log_line(line, pattern_name):
                        score += 1
                format_scores[pattern_name] = score

            # 返回得分最高的格式
            best_format = max(format_scores, key=format_scores.get)
            return best_format if format_scores[best_format] > 0 else "standard"

        except Exception:
            return "standard"

    @staticmethod
    def parse_log_file(
        file_path: Path, pattern_name: Optional[str] = None
    ) -> List[Dict]:
        """
        解析整个日志文件

        Args:
            file_path: 日志文件路径
            pattern_name: 模式名称，如果为None则自动检测

        Returns:
            解析结果列表
        """
        if not file_path.exists():
            return []

        if pattern_name is None:
            pattern_name = LogParser.detect_log_format(file_path)

        results = []

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    parsed = LogParser.parse_log_line(line, pattern_name)
                    if parsed:
                        parsed["line_number"] = line_num
                        parsed["raw_line"] = line.strip()
                        results.append(parsed)
                    else:
                        # 无法解析的行也保存
                        results.append(
                            {
                                "line_number": line_num,
                                "raw_line": line.strip(),
                                "level": "UNKNOWN",
                                "message": line.strip(),
                            }
                        )
        except Exception as e:
            print(f"Error parsing log file {file_path}: {e}")

        return results

    @staticmethod
    def extract_log_level(message: str) -> str:
        """
        从消息中提取日志级别

        Args:
            message: 日志消息

        Returns:
            日志级别
        """
        message_upper = message.upper()

        # 按优先级检查
        if any(keyword in message_upper for keyword in ["FATAL", "CRITICAL"]):
            return "CRITICAL"
        elif any(keyword in message_upper for keyword in ["ERROR", "ERR"]):
            return "ERROR"
        elif any(keyword in message_upper for keyword in ["WARN", "WARNING"]):
            return "WARNING"
        elif any(keyword in message_upper for keyword in ["INFO", "INFORMATION"]):
            return "INFO"
        elif any(keyword in message_upper for keyword in ["DEBUG", "TRACE"]):
            return "DEBUG"
        else:
            return "INFO"  # 默认级别

    @staticmethod
    def filter_logs_by_level(logs: List[Dict], level: str) -> List[Dict]:
        """
        按级别过滤日志

        Args:
            logs: 日志列表
            level: 日志级别

        Returns:
            过滤后的日志列表
        """
        return [log for log in logs if log.get("level", "").upper() == level.upper()]

    @staticmethod
    def filter_logs_by_keyword(logs: List[Dict], keyword: str) -> List[Dict]:
        """
        按关键词过滤日志

        Args:
            logs: 日志列表
            keyword: 关键词

        Returns:
            过滤后的日志列表
        """
        keyword_lower = keyword.lower()
        return [
            log
            for log in logs
            if keyword_lower in log.get("message", "").lower()
            or keyword_lower in log.get("raw_line", "").lower()
        ]

    @staticmethod
    def get_log_statistics(logs: List[Dict]) -> Dict:
        """
        获取日志统计信息

        Args:
            logs: 日志列表

        Returns:
            统计信息字典
        """
        if not logs:
            return {"total": 0, "by_level": {}, "time_range": None}

        # 按级别统计
        level_counts = {}
        for log in logs:
            level = log.get("level", "UNKNOWN")
            level_counts[level] = level_counts.get(level, 0) + 1

        # 时间范围（如果有时间戳）
        timestamps = []
        for log in logs:
            timestamp_str = log.get("timestamp")
            if timestamp_str:
                try:
                    # 尝试解析时间戳
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                    timestamps.append(timestamp)
                except ValueError:
                    continue

        time_range = None
        if timestamps:
            time_range = {"start": min(timestamps), "end": max(timestamps)}

        return {"total": len(logs), "by_level": level_counts, "time_range": time_range}
