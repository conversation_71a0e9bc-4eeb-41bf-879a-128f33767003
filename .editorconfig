# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Python files
[*.py]
indent_size = 4
max_line_length = 88

# Markdown files
[*.md]
trim_trailing_whitespace = false

# YAML files
[*.{yml,yaml}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# Shell scripts
[*.sh]
indent_size = 2

# Makefile
[Makefile]
indent_style = tab

# SQL files
[*.sql]
indent_size = 2

# Configuration files
[*.{ini,cfg,conf}]
indent_size = 4

# Docker files
[Dockerfile*]
indent_size = 2

# Git files
[.gitignore]
indent_size = 2
