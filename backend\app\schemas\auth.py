"""
认证相关的Pydantic模式
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, EmailStr


class Token(BaseModel):
    """令牌响应模式"""

    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    """令牌数据模式"""

    username: Optional[str] = None


class UserLogin(BaseModel):
    """用户登录模式"""

    username: str
    password: str


class UserBase(BaseModel):
    """用户基础模式"""

    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    """用户创建模式"""

    password: str


class UserUpdate(BaseModel):
    """用户更新模式"""

    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None


class UserInDB(UserBase):
    """数据库中的用户模式"""

    id: int
    hashed_password: str
    is_superuser: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class User(UserBase):
    """用户响应模式"""

    id: int
    is_superuser: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class UserProfile(User):
    """用户详细信息模式"""

    roles: List[str] = []
    permissions: List[str] = []
    department: Optional[str] = None


class PasswordChange(BaseModel):
    """密码修改模式"""

    old_password: str
    new_password: str


class PasswordReset(BaseModel):
    """密码重置模式"""

    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""

    token: str
    new_password: str
